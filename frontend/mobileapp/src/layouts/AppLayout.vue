<template>
  <div
    v-if="isUserDataReady && isAppReady"
    class="size-full overflow-y-auto"
    data-v-app-layout
    ref="appLayout"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  >
    <div
      data-pull-indicator
      class="mt-12 flex flex-col items-center transition-all duration-300 ease-out"
      :style="{ height: `${pullDistance}px`, opacity: pullOpacity }"
    >
      <template v-if="isPulling">
        <div
          v-if="!isLoading"
          class="flex flex-col items-center justify-center"
        >
          <MaterialDesignIcon
            icon="keyboard_arrow_down"
            class="text-primary mt-2 text-5xl"
          />
          <span class="text-sm text-gray-400">Pull to refresh</span>
        </div>
        <div
          v-if="isLoading"
          class="flex flex-col items-center justify-center gap-2 pt-4"
        >
          <LoadingSpinner class="text-primary size-8" />
          <span class="text-sm text-gray-400">Loading...</span>
        </div>
      </template>
    </div>
    <DialogsWrapper />
    <router-view />
    <BottomMenu />
  </div>
  <Splash v-else-if="isLoading">
    <LoadingSpinner class="absolute size-20 text-[#C9E386]" />
  </Splash>
  <div
    v-else-if="!isAppReady"
    class="flex h-full flex-col items-center justify-center"
  >
    <template v-if="isTrueClient === false">
      <img class="max-w-xs" alt="Aventur logo" src="/assets/logo-black.svg" />
      <BaseTitle class="mt-5 text-lg">
        You're not an active Aventur client</BaseTitle
      >
    </template>
    <template v-else>
      <img class="max-w-xs" alt="Aventur logo" src="/assets/logo-black.svg" />
      <BaseTitle class="mt-5 text-lg"> Oops! Something went wrong </BaseTitle>
    </template>
    <p class="text-center">
      Please try
      <a class="underline" @click="logout">again</a>
      or contact us for further assistance.
    </p>
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { RouterView } from 'vue-router';
  import { onMounted, provide, ref, useTemplateRef } from 'vue';
  import { DialogsWrapper } from 'vuejs-confirm-dialog';
  import { GetCurrentUserOutput, signOut } from '@aws-amplify/auth';

  import { useAuth } from '@aventur-shared/modules/auth';
  import {
    provideUserAbility,
    useUserStore,
  } from '@aventur-shared/modules/users';
  import { useRefData } from '@aventur-shared/stores';
  import BaseTitle from '@aventur-shared/components/BaseTitle.vue';
  import LoadingSpinner from '@aventur-shared/components/LoadingSpinner.vue';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { isClient } from '@aventur-shared/utils/user';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';

  import router from '@/router';
  import { useAppStore } from '@/stores/appStore';
  import {
    AventurEvents,
    type ClientSignInEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import { useResetView } from '@/composables/useResetView';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  import Splash from '@/components/Splash.vue';
  import BottomMenu from '@/components/bottom-menu/BottomMenu.vue';
  //

  const { trackEvent, setTrackingIdentity } = useEventTracking();

  defineProps<{
    user: GetCurrentUserOutput;
  }>();

  const isTrueClient = ref<boolean | undefined>();
  const isSidebarOpen = ref(false);

  const { isLoading } = useAPIState();
  const { setIsReady: setAppReady } = useAppStore();
  const { isReady: isAppReady } = storeToRefs(useAppStore());
  const { isUserDataReady } = useAuth();
  const { getRefData } = useRefData();
  const { getUserData } = useUserStore();
  const { userId, getAbility, user: userValue } = storeToRefs(useUserStore());
  const { loadClientData, loadClientGoals } = useClientStore();

  provideUserAbility(getAbility);

  const appLayoutTemplateRef = useTemplateRef<HTMLElement>('appLayout');
  useResetView(appLayoutTemplateRef, router);

  const {
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    isPulling,
    pullOpacity,
    pullDistance,
  } = usePullRefresh(appLayoutTemplateRef);

  onMounted(async () => {
    isTrueClient.value = await isClient();
    try {
      if (!isTrueClient.value) {
        throw new Error('Unauthorized adviser login.');
      }

      await getRefData();
      await getUserData();

      await loadClientData(userId.value as ClientId);
      await loadClientGoals(userId.value as ClientId);

      setAppReady(true);

      setTrackingIdentity(userValue.value);
      trackEvent<ClientSignInEvent>(AventurEvents.ClientSignIn);
    } catch {
      setAppReady(false);
    }
  });

  const logout = async () => {
    await signOut();
  };

  const toggleSidebar = (): void => {
    isSidebarOpen.value = !isSidebarOpen.value;
  };
  provide('toggleSidebar', toggleSidebar);
</script>
