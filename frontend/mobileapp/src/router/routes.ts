import { RouteRecordRaw } from 'vue-router';
//

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: { name: 'dashboard' },
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('@/pages/Dashboard.vue'),
    meta: { title: 'Dashboard' },
  },
  {
    path: '/portfolio',
    name: 'portfolio',
    component: () => import('@/pages/Portfolio.vue'),
    meta: { title: 'Portfolio' },
  },
  {
    path: '/avril',
    component: () => import('@/pages/Avril.vue'),
    meta: { title: 'Avril' },
  },
  {
    path: '/goals',
    alias: '/profile/goals',
    component: () => import('@/pages/Goals.vue'),
    children: [
      {
        path: '',
        meta: {
          title: 'Goals',
          breadcrumbs: [],
        },
        component: () => import('@modules/clients/components/GoalsIndex.vue'),
      },
      {
        path: '/goals/add',
        meta: {
          title: 'Goals',
          breadcrumbs: [{ to: { path: '/Goals' }, label: 'Goals' }],
        },
        component: () =>
          import('@modules/clients/components/goals/ui/AddGoalItem.vue'),
        props: (route) => Object.assign({}, { groupId: route.query.group }),
      },
      {
        path: '/goals/:id',
        meta: {
          title: 'Goals',
          breadcrumbs: [{ to: { path: '/Goals' }, label: 'Goals' }],
        },
        component: () =>
          import('@modules/clients/components/goals/ui/GoalItem.vue'),
      },
    ],
  },
  {
    path: '/profile',
    component: () => import('@/pages/Profile.vue'),
    children: [
      {
        path: '',
        meta: {
          title: 'Profile',
          breadcrumbs: [],
        },
        component: () => import('@modules/clients/components/ProfileIndex.vue'),
      },
      {
        path: 'aboutyou',
        name: 'profile-aboutyou',
        meta: {
          title: 'About you',
          breadcrumbs: [{ to: { path: '/profile' }, label: 'Profile' }],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/PersonalDetailsHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/PersonalDetails.vue'),
        },
      },
      {
        path: 'aboutyou/edit',
        meta: {
          title: 'About you',
          breadcrumbs: [
            { to: { path: '/profile' }, label: 'Profile' },
            {
              to: { path: '/profile/aboutyou' },
              label: 'About you',
            },
          ],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/PersonalDetailsHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/personal-details/aboutyou/AboutYouForm.vue'
            ),
        },
      },
      {
        path: 'contacts',
        name: 'profile-contacts',
        meta: {
          title: 'Contact details',
          breadcrumbs: [{ to: { path: '/profile' }, label: 'Profile' }],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/ContactDetailsHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/ContactDetails.vue'),
        },
      },
      {
        path: 'contacts/edit',
        meta: {
          title: 'Contact details',
          breadcrumbs: [
            { to: { path: '/profile' }, label: 'Profile' },
            {
              to: { path: '/profile/contacts' },
              label: 'Contact details',
            },
          ],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/ContactDetailsHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/personal-details/contacts/ContactDetailsForm.vue'
            ),
        },
      },
      {
        path: 'addresses',
        name: 'profile-addresses',
        meta: {
          title: 'Address details',
          breadcrumbs: [{ to: { path: '/profile' }, label: 'Profile' }],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/AddressDetailsHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/AddressDetails.vue'),
        },
      },
      {
        // covers `address/add` path
        path: 'address/:id',
        meta: {
          title: 'Address details',
          breadcrumbs: [
            { to: { path: '/profile' }, label: 'Profile' },
            {
              to: { path: '/profile/addresses' },
              label: 'Address details',
            },
          ],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/AddressDetailsHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/personal-details/addresses/AddressForm.vue'
            ),
        },
      },
      {
        path: 'family',
        name: 'profile-family',
        meta: {
          title: 'Family details',
          breadcrumbs: [{ to: { path: '/profile' }, label: 'Profile' }],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/FamilyDetailsHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/FamilyDetails.vue'),
        },
      },
      // covers `family/add` path
      {
        path: 'family/:id',
        meta: {
          title: 'Family details',
          breadcrumbs: [
            { to: { path: '/profile' }, label: 'Profile' },
            {
              to: { path: '/profile/family' },
              label: 'Family details',
            },
          ],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/FamilyDetailsHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/personal-details/family/FamilyMemberForm.vue'
            ),
        },
      },
      {
        path: 'assets-debts',
        name: 'profile-assets-debts',
        meta: {
          title: 'Assets & Debts',
          breadcrumbs: [{ to: { path: '/profile' }, label: 'Profile' }],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/AssetsDebtsHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/AssetsDebts.vue'),
        },
      },
      {
        // covers `assets/add` path
        path: 'assets/:id',
        meta: {
          title: 'Assets & Debts',
          breadcrumbs: [
            { to: { path: '/profile' }, label: 'Profile' },
            { to: { path: '/profile/assets-debts' }, label: 'Assets & Debts' },
          ],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/AssetsDebtsHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/assets-debts/assets/AssetForm.vue'
            ),
        },
      },
      {
        // covers `debts/add` path
        path: 'debts/:id',
        meta: {
          title: 'Assets & Debts',
          breadcrumbs: [
            { to: { path: '/profile' }, label: 'Profile' },
            { to: { path: '/profile/assets-debts' }, label: 'Assets & Debts' },
          ],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/AssetsDebtsHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/assets-debts/debts/DebtForm.vue'
            ),
        },
      },
      {
        path: 'income',
        name: 'profile-income',
        meta: {
          title: 'Income',
          breadcrumbs: [],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/IncomeHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/income/Income.vue'),
        },
      },
      {
        // covers `income/add` path
        path: 'income/:id',
        meta: {
          title: 'Income',
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/IncomeHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/income/IncomeForm.vue'),
        },
      },
      {
        path: 'expenditures',
        name: 'profile-expenditures',
        meta: {
          title: 'Expenditures',
          breadcrumbs: [],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/ExpendituresHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/expenditures/Expenditures.vue'
            ),
        },
      },
      {
        // covers `expenditures/add` path
        path: 'expenditures/:id',
        meta: {
          title: 'Expenditures',
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/ExpendituresHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/expenditures/ExpenditureForm.vue'
            ),
        },
      },
      {
        path: 'marketing',
        name: 'profile-marketing',
        meta: {
          title: 'Marketing Preferences',
          breadcrumbs: [{ to: { path: '/profile' }, label: 'Profile' }],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/MarketingSettingsHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/MarketingSettings.vue'),
        },
      },
      {
        path: 'account',
        name: 'profile-account',
        meta: {
          title: 'Account Settings',
          breadcrumbs: [{ to: { path: '/profile' }, label: 'Profile' }],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/AccountSettingsHeader.vue'
            ),
          default: () =>
            import('@modules/clients/components/profile/AccountSettings.vue'),
        },
      },
      {
        path: 'account/edit',
        meta: {
          title: 'Account Settings',
          breadcrumbs: [],
        },
        components: {
          header: () =>
            import(
              '@modules/clients/components/profile/headers/AccountSettingsHeader.vue'
            ),
          default: () =>
            import(
              '@modules/clients/components/profile/settings/account-settings/AccountSettingsForm.vue'
            ),
        },
      },
    ],
  },
  {
    path: '/logout',
    meta: { title: 'Logout' },
    component: () => import('@/pages/Logout.vue'),
  },
];

export default routes;
