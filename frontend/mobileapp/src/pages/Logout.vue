<template>
  <Splash>
    <LoadingSpinner class="absolute size-20 text-[#C9E386]" />
  </Splash>
</template>

<script setup lang="ts">
  import { onMounted } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@aventur-shared/modules/users';
  import LoadingSpinner from '@aventur-shared/components/LoadingSpinner.vue';

  import {
    AventurEvents,
    type ClientSignOutEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import Splash from '@/components/Splash.vue';
  //

  const router = useRouter();
  const { signOut } = useUserStore();
  const { trackEvent } = useEventTracking();

  onMounted(async () => {
    try {
      await signOut();
      await router.push('/');

      trackEvent<ClientSignOutEvent>(AventurEvents.ClientSignOut);
    } catch {
      //
    }
  });
</script>
