import { track } from '@amplitude/analytics-browser';
import { User } from '@aventur-shared/modules/users';
import * as amplitude from '@amplitude/analytics-browser';
//

const AMPLITUDE_EVENT_TYPE = 'Aventur';

export enum AventurEvents {
  ClientSignIn = 'client-signed-in',
  ClientSignOut = 'client-signed-out',
}

interface IAventurEvent {
  event: AventurEvents;
  display_name: string;
  options?: Record<string, unknown>;
}

export interface ClientSignInEvent extends IAventurEvent {
  event: AventurEvents.ClientSignIn;
}

export interface ClientSignOutEvent extends IAventurEvent {
  event: AventurEvents.ClientSignOut;
}

type AventurEvent = ClientSignInEvent | ClientSignOutEvent;

type EventOptions<T extends IAventurEvent> = {
  [P1 in keyof T['options']]: P1 extends keyof T['options']
    ? T['options'][P1]
    : never;
};

const setAmplitudeUserDetails = (user: User) => {
  if (import.meta.env.VITE_AMPLITUDE_KEY) {
    amplitude.setUserId(user.email);
    const identify = new amplitude.Identify();
    identify
      .set('jarvis_id', user.id)
      .set('first_name', user.firstName)
      .set('last_name', user.lastName);
    amplitude.identify(identify);
    amplitude.setGroup('user-role', user.groups ?? '');
  }
};

const trackAventurEvent = <T extends AventurEvent>(
  event: AventurEvents,
  options?: EventOptions<T>,
) => {
  if (import.meta.env.VITE_AMPLITUDE_KEY) {
    track(event, {
      event_type: AMPLITUDE_EVENT_TYPE,
      ...(options ?? {}),
    });
  }
};

export const useEventTracking = () => ({
  setTrackingIdentity: setAmplitudeUserDetails,
  trackEvent: trackAventurEvent,
});
