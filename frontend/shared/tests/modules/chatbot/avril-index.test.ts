import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { createTestingPinia } from '@pinia/testing';
import { useChatbotStore } from '@aventur-shared/modules/chatbot/stores/chatbotStore';
import { Tab } from '@aventur-shared/modules/chatbot/types/chat-tab';
import AvrilIndex from '@aventur-shared/modules/chatbot/components/pages/AvrilIndex.vue';
import { XMarkIcon } from '@heroicons/vue/24/solid';

const routerGo = vi.fn();
vi.mock('vue-router', () => ({
  useRouter: () => ({
    go: routerGo,
  }),
}));

vi.mock('@aventur-shared/modules/chatbot/utils/helpers', () => ({
  scrollToBottom: vi.fn(),
  formatToChatFeedTimestamp: () => 'timestamp',
}));

import { scrollToBottom } from '@aventur-shared/modules/chatbot/utils/helpers';

describe('AvrilIndex.vue', () => {
  let wrapper: ReturnType<typeof mount>;
  let chatbotStore: ReturnType<typeof useChatbotStore>;

  beforeEach(async () => {
    vi.clearAllMocks();

    const pinia = createTestingPinia({
      stubActions: false,
      createSpy: vi.fn,
      initialState: {
        chatbotStore: {
          messages: [],
          activeTab: Tab.Chat,
          isTyping: false,
        },
      },
    });

    chatbotStore = useChatbotStore();
    chatbotStore.fetchMessages = vi.fn().mockResolvedValue([
      {
        messageLayout: 'user_text_layout',
        message: 'Hello',
        completed: true,
      },
    ]);
    chatbotStore.postMessage = vi.fn();
    chatbotStore.newConversation = vi.fn();

    wrapper = mount(AvrilIndex, {
      global: {
        plugins: [pinia],
        stubs: {
          'x-mark-icon': {
            template:
              '<div data-test="x-mark-icon" @click="$emit(\'click\')"></div>',
          },
          'text-field': true,
          UserText: true,
          AssistantText: true,
          DateOfBirth: true,
          Relations: true,
        },
      },
    });

    await flushPromises();
  });

  it('renders loaded messages after fetchMessages resolves', async () => {
    expect(chatbotStore.fetchMessages).toHaveBeenCalled();

    expect(wrapper.text()).toContain('timestamp');

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.loadedMessages).toBe(true);
  });

  it('navigates back when x-mark-icon is clicked (no withdrawn consent)', async () => {
    // Ensure no withdrawn consent messages
    chatbotStore.messages = [
      {
        messageLayout: 'user_text_layout',
        message: 'Hello',
        completed: true,
      },
    ];
    await nextTick();

    const icon = wrapper.findComponent(XMarkIcon);
    await icon.trigger('click');

    expect(routerGo).toHaveBeenCalledWith(-1);
    expect(chatbotStore.newConversation).not.toHaveBeenCalled();
  });

  it('clears conversation and navigates back when x-mark-icon is clicked with withdrawn consent', async () => {
    // Set up withdrawn consent message
    chatbotStore.messages = [
      {
        messageLayout: 'user__consent_withdraw_layout',
        completed: true,
        withdrawn: true,
        message: undefined,
      },
      {
        messageLayout: 'assistant_text_layout',
        message: 'Your consent has been withdrawn.',
        completed: true,
      },
    ];
    await nextTick();

    const icon = wrapper.findComponent(XMarkIcon);
    await icon.trigger('click');

    expect(chatbotStore.newConversation).toHaveBeenCalledOnce();
    expect(routerGo).toHaveBeenCalledWith(-1);
  });

  it('clears chat', async () => {
    expect(chatbotStore.newConversation).toHaveBeenCalledTimes(0);

    const clearChatButton = wrapper.find('button:nth-of-type(1)');
    expect(clearChatButton.text()).toBe('Clear Chat');

    await clearChatButton.trigger('click');
    await nextTick();
    expect(chatbotStore.newConversation).toHaveBeenCalledOnce();
  });

  it('disables the input when there is a permission form', async () => {
    chatbotStore.messages = [
      {
        messageLayout: 'consent_form_layout',
        message: '',
        completed: false,
      },
    ];
    await nextTick();

    const input = wrapper.find('input');
    expect(input.attributes('disabled')).toBeDefined();
  });

  it('disables the input when consent is withdrawn', async () => {
    chatbotStore.messages = [
      {
        messageLayout: 'user__consent_withdraw_layout',
        completed: true,
        withdrawn: true,
        message: undefined,
      },
    ];
    await nextTick();

    const input = wrapper.find('input');
    expect(input.attributes('disabled')).toBeDefined();
  });

  it('disables the input when consent withdrawal is pending', async () => {
    chatbotStore.messages = [
      {
        messageLayout: 'user__consent_withdraw_layout',
        completed: false,
        withdrawn: false,
        message: undefined,
      },
    ];
    await nextTick();

    const input = wrapper.find('input');
    expect(input.attributes('disabled')).toBeDefined();
  });

  it('enables the input when consent withdrawal is cancelled', async () => {
    chatbotStore.messages = [
      {
        messageLayout: 'user__consent_withdraw_layout',
        completed: true,
        withdrawn: false,
        message: undefined,
      },
    ];
    await nextTick();

    const input = wrapper.find('input');
    expect(input.attributes('disabled')).toBeUndefined();
  });

  it('calls scrollToBottom when activeTab or messages update', async () => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    scrollToBottom.mockClear();

    chatbotStore.activeTab = Tab.Analytics;
    await nextTick();
    await nextTick();
    expect(scrollToBottom).toHaveBeenCalled();

    chatbotStore.messages.push({
      messageLayout: 'user_text_layout',
      message: 'New message',
      completed: true,
    });
    await nextTick();
    await nextTick();
    expect(scrollToBottom).toHaveBeenCalledTimes(2);
  });

  it('has form with correct structure', async () => {
    const form = wrapper.find('form');
    expect(form.exists()).toBe(true);

    const input = wrapper.find('input[placeholder="Start Typing"]');
    expect(input.exists()).toBe(true);
    expect(input.attributes('placeholder')).toBe('Start Typing');
  });

  it('has input with correct classes including disabled styling', async () => {
    const input = wrapper.find('input');
    expect(input.classes()).toContain('disabled:opacity-50');
    expect(input.classes()).toContain('disabled:cursor-not-allowed');
    expect(input.classes()).toContain('disabled:bg-gray-100');
  });

  it('displays typing indicator when isTyping is true', async () => {
    chatbotStore.isTyping = true;
    await nextTick();

    const typingIndicator = wrapper.find('.opacity-80');
    expect(typingIndicator.exists()).toBe(true);
    expect(typingIndicator.text()).toContain('Avril is typing');

    // Check that input container has opacity-30 class
    const inputContainer = wrapper.find('.inset-x-0');
    expect(inputContainer.classes()).toContain('opacity-30');
  });

  it('hides typing indicator when isTyping is false', async () => {
    chatbotStore.isTyping = false;
    await nextTick();

    const typingIndicator = wrapper.find('.opacity-0');
    expect(typingIndicator.exists()).toBe(true);
    expect(typingIndicator.classes()).not.toContain('opacity-80');
  });

  it('displays Analytics tab when activeTab is Analytics', async () => {
    chatbotStore.activeTab = Tab.Analytics;
    chatbotStore.messages = [
      {
        messageLayout: 'user_text_layout',
        message: 'Test message',
        completed: true,
      },
    ];
    await nextTick();

    const analyticsView = wrapper.find('.overflow-y-auto.px-4');
    expect(analyticsView.exists()).toBe(true);

    // Should not show the chat input
    const chatInput = wrapper.find('input');
    expect(chatInput.exists()).toBe(false);
  });

  it('focuses input after typing stops when it was previously focused', async () => {
    const input = wrapper.find('input');
    const inputElement = input.element as HTMLInputElement;

    // Mock the focus method
    const focusSpy = vi.spyOn(inputElement, 'focus');

    // Simulate input being focused first
    await input.trigger('focus');
    await nextTick();

    // Set typing to true, then false
    chatbotStore.isTyping = true;
    await nextTick();

    chatbotStore.isTyping = false;
    await nextTick();
    await nextTick(); // Wait for the watch callback

    expect(focusSpy).toHaveBeenCalled();
  });
});
