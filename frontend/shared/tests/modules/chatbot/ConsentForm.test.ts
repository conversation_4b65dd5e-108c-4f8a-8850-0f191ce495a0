import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import { createTestingPinia } from '@pinia/testing';
import { useChatbotStore } from '@aventur-shared/modules/chatbot/stores/chatbotStore';
import { useUserStore } from '@aventur-shared/modules/users/stores/userStore';
import ConsentForm from '@aventur-shared/modules/chatbot/components/messages/ConsentForm.vue';
import { ConsentFormValues } from '@aventur-shared/modules/chatbot/types/form-model';

// Mock aws-amplify/auth
vi.mock('aws-amplify/auth', () => ({
  fetchAuthSession: vi.fn(),
}));

// Mock grant-consent API
vi.mock('@aventur-shared/modules/chatbot/api/grant-consent', () => ({
  default: vi.fn(),
}));

// Import the mocked functions
import { fetchAuthSession } from 'aws-amplify/auth';
import grantConsent from '@aventur-shared/modules/chatbot/api/grant-consent';

const mockFetchAuthSession = vi.mocked(fetchAuthSession);
const mockGrantConsent = vi.mocked(grantConsent);

describe('ConsentForm.vue', () => {
  let wrapper: ReturnType<typeof mount>;
  let chatbotStore: ReturnType<typeof useChatbotStore>;
  let userStore: ReturnType<typeof useUserStore>;
  let mockMessage: ConsentFormValues;
  let pinia: ReturnType<typeof createTestingPinia>;

  beforeEach(async () => {
    vi.clearAllMocks();

    pinia = createTestingPinia({
      stubActions: false,
      createSpy: vi.fn,
      initialState: {
        userStore: {
          user: {
            id: 'test-user-123',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            type: 'client',
            permissions: [],
          },
        },
      },
    });

    chatbotStore = useChatbotStore();
    userStore = useUserStore();

    userStore.user = {
      id: 123,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      type: 'client',
      permissions: [],
    };

    chatbotStore.removeMessage = vi.fn();
    userStore.getUserData = vi.fn().mockResolvedValue(undefined);

    mockMessage = {
      id: 1,
      messageLayout: 'consent_form_layout',
      timestamp: '2024-01-01T00:00:00Z',
    };

    wrapper = mount(ConsentForm, {
      props: {
        message: mockMessage,
      },
      global: {
        plugins: [pinia],
      },
    });

    await flushPromises();
  });

  describe('Initial rendering', () => {
    it('renders the initial permission form by default', () => {
      expect(wrapper.find('[data-testid="initial-form"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="initial-title"]').text()).toBe(
        'Data Access Consent Required',
      );
      expect(
        wrapper.find('[data-testid="initial-description"]').text(),
      ).toContain('Before proceeding, Avril needs your permission to access');
      expect(wrapper.find('[data-testid="grant-button"]').text()).toBe(
        'Grant Permission',
      );
      expect(wrapper.find('[data-testid="deny-button"]').text()).toBe('Deny');
    });

    it('does not show granted or denied messages initially', () => {
      expect(wrapper.find('[data-testid="granted-message"]').exists()).toBe(
        false,
      );
      expect(wrapper.find('[data-testid="denied-message"]').exists()).toBe(
        false,
      );
    });
  });

  describe('Button interactions', () => {
    it('calls handleAccept when Grant Permission button is clicked', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      expect(mockGrantConsent).toHaveBeenCalledWith(123);
      expect(mockFetchAuthSession).toHaveBeenCalledWith({ forceRefresh: true });
      expect(userStore.getUserData).toHaveBeenCalled();
    });

    it('shows denied message when Deny button is clicked', async () => {
      const denyButton = wrapper.find('[data-testid="deny-button"]');
      await denyButton.trigger('click');
      await nextTick();

      expect(wrapper.find('[data-testid="initial-form"]').exists()).toBe(false);
      expect(wrapper.find('[data-testid="denied-message"]').exists()).toBe(
        true,
      );
      expect(wrapper.find('[data-testid="denied-title"]').text()).toBe(
        'Permission Denied',
      );
      expect(
        wrapper.find('[data-testid="denied-description"]').text(),
      ).toContain('You are unable to chat with Avril without granting');
    });

    it('shows back to initial form when Back button is clicked in denied state', async () => {
      // First deny permission
      const denyButton = wrapper.find('[data-testid="deny-button"]');
      await denyButton.trigger('click');
      await nextTick();

      // Then click back
      const backButton = wrapper.find('[data-testid="back-button"]');
      await backButton.trigger('click');
      await nextTick();

      expect(wrapper.find('[data-testid="initial-form"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="denied-message"]').exists()).toBe(
        false,
      );
    });

    it('calls removeMessage when Continue button is clicked in granted state', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});

      // First grant permission
      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Then click continue
      const continueButton = wrapper.find('[data-testid="continue-button"]');
      await continueButton.trigger('click');

      expect(chatbotStore.removeMessage).toHaveBeenCalledWith(1);
    });
  });

  describe('Loading states', () => {
    it('shows loading state during grant process', async () => {
      mockGrantConsent.mockImplementation(
        () => new Promise((resolve) => setTimeout(resolve, 100)),
      );

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await nextTick();

      expect(grantButton.text()).toBe('Granting...');
      expect(grantButton.attributes('disabled')).toBeDefined();

      const denyButton = wrapper.find('[data-testid="deny-button"]');
      expect(denyButton.attributes('disabled')).toBeDefined();
    });

    it('resets loading state after grant completes', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Should show granted message, not loading state
      expect(wrapper.find('[data-testid="granted-message"]').exists()).toBe(
        true,
      );
      expect(wrapper.find('[data-testid="granted-title"]').text()).toBe(
        'Permission Granted Successfully',
      );
    });

    it('resets loading state even if grant fails', async () => {
      mockGrantConsent.mockRejectedValue(new Error('Grant failed'));

      // Suppress console error for this test
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Should still be in initial state with buttons enabled
      expect(grantButton.text()).toBe('Grant Permission');
      expect(grantButton.attributes('disabled')).toBeUndefined();

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Disabled prop', () => {
    it('disables buttons when disabled prop is true', async () => {
      await wrapper.setProps({
        message: mockMessage,
        disabled: true,
      });

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      const denyButton = wrapper.find('[data-testid="deny-button"]');

      expect(grantButton.attributes('disabled')).toBeDefined();
      expect(denyButton.attributes('disabled')).toBeDefined();
    });

    it('disables continue button when disabled prop is true in granted state', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});

      // First grant permission
      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Set disabled prop
      await wrapper.setProps({
        message: mockMessage,
        disabled: true,
      });

      const continueButton = wrapper.find('[data-testid="continue-button"]');
      expect(continueButton.attributes('disabled')).toBeDefined();
    });

    it('disables back button when disabled prop is true in denied state', async () => {
      // First deny permission
      const denyButton = wrapper.find('[data-testid="deny-button"]');
      await denyButton.trigger('click');
      await nextTick();

      // Set disabled prop
      await wrapper.setProps({
        message: mockMessage,
        disabled: true,
      });

      const backButton = wrapper.find('[data-testid="back-button"]');
      expect(backButton.attributes('disabled')).toBeDefined();
    });
  });

  describe('Error handling', () => {
    it('does not proceed with grant if userId is not available', async () => {
      // Create a new wrapper with no userId for this test
      const noUserPinia = createTestingPinia({
        stubActions: false,
        createSpy: vi.fn,
        initialState: {
          userStore: {
            user: null,
          },
        },
      });

      const noUserWrapper = mount(ConsentForm, {
        props: {
          message: mockMessage,
        },
        global: {
          plugins: [noUserPinia],
        },
      });

      const grantButton = noUserWrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      expect(mockGrantConsent).not.toHaveBeenCalled();
    });

    it('handles grantConsent errors gracefully', async () => {
      mockGrantConsent.mockRejectedValue(new Error('API Error'));

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Should reset loading state even on error
      expect(grantButton.text()).toBe('Grant Permission');
      expect(grantButton.attributes('disabled')).toBeUndefined();
      // Should show grant error message
      expect(wrapper.find('[data-testid="grant-error"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="grant-error"]').text()).toBe(
        'Failed to grant consent. Please try again.',
      );
    });

    it('handles fetchAuthSession errors gracefully', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockRejectedValue(new Error('Auth failed'));

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Should reset loading state even on error
      expect(grantButton.text()).toBe('Grant Permission');
      expect(grantButton.attributes('disabled')).toBeUndefined();
      // Should show grant error message
      expect(wrapper.find('[data-testid="grant-error"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="grant-error"]').text()).toBe(
        'Failed to grant consent. Please try again.',
      );
    });

    it('handles getUserData errors gracefully', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});
      userStore.getUserData = vi
        .fn()
        .mockRejectedValue(new Error('User data error'));

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Should reset loading state even on error
      expect(grantButton.text()).toBe('Grant Permission');
      expect(grantButton.attributes('disabled')).toBeUndefined();
      // Should show grant error message
      expect(wrapper.find('[data-testid="grant-error"]').exists()).toBe(true);
      expect(wrapper.find('[data-testid="grant-error"]').text()).toBe(
        'Failed to grant consent. Please try again.',
      );
    });

    it('does not show error message initially', () => {
      expect(wrapper.find('[data-testid="grant-error"]').exists()).toBe(false);
    });
  });

  describe('State transitions', () => {
    it('transitions from initial state to denied message', async () => {
      expect(wrapper.find('[data-testid="initial-title"]').text()).toBe(
        'Data Access Consent Required',
      );

      const denyButton = wrapper.find('[data-testid="deny-button"]');
      await denyButton.trigger('click');
      await nextTick();

      expect(wrapper.find('[data-testid="denied-title"]').text()).toBe(
        'Permission Denied',
      );
    });

    it('transitions from initial state to granted message', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});

      expect(wrapper.find('[data-testid="initial-title"]').text()).toBe(
        'Data Access Consent Required',
      );

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      expect(wrapper.find('[data-testid="granted-title"]').text()).toBe(
        'Permission Granted Successfully',
      );
    });

    it('transitions from denied back to initial state', async () => {
      // Deny first
      const denyButton = wrapper.find('[data-testid="deny-button"]');
      await denyButton.trigger('click');
      await nextTick();

      expect(wrapper.find('[data-testid="denied-title"]').text()).toBe(
        'Permission Denied',
      );

      // Go back
      const backButton = wrapper.find('[data-testid="back-button"]');
      await backButton.trigger('click');
      await nextTick();

      expect(wrapper.find('[data-testid="initial-title"]').text()).toBe(
        'Data Access Consent Required',
      );
    });
  });

  describe('Granted state content', () => {
    beforeEach(async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});

      const grantButton = wrapper.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();
    });

    it('shows correct granted message content', () => {
      expect(wrapper.find('[data-testid="granted-title"]').text()).toBe(
        'Permission Granted Successfully',
      );
      expect(
        wrapper.find('[data-testid="granted-description"]').text(),
      ).toContain('Thank you for granting permission');
      expect(
        wrapper.find('[data-testid="granted-additional"]').text(),
      ).toContain('You can withdraw your consent at any time');
      expect(wrapper.find('[data-testid="continue-button"]').text()).toBe(
        'Continue',
      );
    });
  });

  describe('Continue button behavior', () => {
    it('does not call removeMessage if message has no id', async () => {
      mockGrantConsent.mockResolvedValue();
      mockFetchAuthSession.mockResolvedValue({});

      // Create message without id
      const messageWithoutId = {
        messageLayout: 'consent_form_layout',
        timestamp: '2024-01-01T00:00:00Z',
      };

      const wrapperWithoutId = mount(ConsentForm, {
        props: {
          message: messageWithoutId,
        },
        global: {
          plugins: [pinia],
        },
      });

      // Grant permission
      const grantButton = wrapperWithoutId.find('[data-testid="grant-button"]');
      await grantButton.trigger('click');
      await flushPromises();

      // Click continue
      const continueButton = wrapperWithoutId.find(
        '[data-testid="continue-button"]',
      );
      await continueButton.trigger('click');

      expect(chatbotStore.removeMessage).not.toHaveBeenCalled();
    });
  });
});
