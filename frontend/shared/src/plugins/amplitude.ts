import { App } from 'vue';
import * as amplitude from '@amplitude/analytics-browser';
//

export default {
  install: async (app: App) => {
    if (import.meta.env.VITE_AMPLITUDE_KEY) {
      amplitude.init(String(import.meta.env.VITE_AMPLITUDE_KEY), {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        appVersion: __APP_VERSION__,
        autocapture: {
          attribution: false,
          pageViews: true,
          sessions: false,
          formInteractions: false,
          fileDownloads: true,
          elementInteractions: false,
        },
        serverZone: 'EU',
      });
    }
  },
};
