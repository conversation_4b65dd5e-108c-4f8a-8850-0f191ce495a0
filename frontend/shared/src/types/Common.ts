type DeepNullableArray<T> = Array<DeepNullable<T>>;
type DeepNullableObject<T> = { [P in keyof T]: DeepNullable<T[P]> };

export type Maybe<T> = T | null | undefined;
export type Nullable<T> = T | null;
export type ArrayElement<A> = A extends readonly (infer T)[] ? T : never;

export type DeepNullable<T> =
  T extends Array<infer U>
    ? DeepNullableArray<U>
    : T extends object
      ? DeepNullableObject<T>
      : Nullable<T>;

export type NonNullableFields<T, E extends keyof T = never> = {
  [K in keyof T]: [E] extends [never]
    ? NonNullable<T[K]>
    : K extends E
      ? NonNullable<T[K]>
      : T[K];
};

export interface Options<TId = string | number | boolean> {
  id: TId;
  name: string;
}

export type WithRequired<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type WithPartialRequired<T, K extends keyof T> = Partial<T> &
  Required<Pick<T, K>>;

export type AnyFunction = (...args: any[]) => any;
