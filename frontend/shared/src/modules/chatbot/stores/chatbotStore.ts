import { defineStore } from 'pinia';
import { mapToDto } from '../types/message';
import { Tab } from '../types/chat-tab';
import getMessages from '../api/get-messages';
import streamMessage from '../api/post-message';
import newConversation from '../api/new-conversation';
import { AllFormValues, isConsentForm } from '../types/form-model';
import { generate_placeholder_message_metadata } from '../utils/helpers';

type State = {
  messages: AllFormValues[];
  activeTab: Tab;
  isTyping: boolean;
  status: {
    pending: boolean;
    error: Error | null;
  };
};

type fetchMessagesOptions = { force: boolean };

type Getters = {
  getMessages: (state: State) => AllFormValues[];
  isBusy(state: State): boolean;
};

type Actions = {
  fetchMessages: (options?: fetchMessagesOptions) => Promise<AllFormValues[]>;
  setMessages: (messages: AllFormValues[]) => void;
  removeMessage: (messageId: number) => void;
  isChatbotPermissionError: (error: unknown) => boolean;
  postMessage: (message: AllFormValues, append?: boolean) => void;
  newConversation: () => void;
};

export const useChatbotStore = defineStore<'chatbot', State, Getters, Actions>(
  'chatbot',
  {
    state: () => ({
      messages: [],
      activeTab: Tab.Chat,
      isTyping: false,
      status: {
        error: null,
        pending: false,
      },
    }),
    getters: {
      getMessages: (state) => state.messages,
      getIsTyping: (state) => state.isTyping,
      isBusy(state: State) {
        return state.status.pending;
      },
    },
    actions: {
      async fetchMessages(
        options = { force: false },
      ): Promise<AllFormValues[]> {
        const { force } = options;
        if (this.messages.length && !force) {
          return this.getMessages;
        }

        try {
          this.status.pending = true;

          const messages = await getMessages();
          this.setMessages(messages);

          this.status.pending = false;
        } catch (e: unknown) {
          this.status.pending = false;

          // Check for chatbot-specific permission errors
          if (this.isChatbotPermissionError(e)) {
            // Only add permission form if the most recent message isn't already a permission form
            const lastMessage = this.messages[this.messages.length - 1];
            if (!lastMessage || !isConsentForm(lastMessage)) {
              const { id, timestamp } = generate_placeholder_message_metadata();
              const permissionMessage: AllFormValues = {
                messageLayout: 'consent_form_layout',
                message: '',
                timestamp: timestamp,
                id: id,
              };
              this.messages.push(permissionMessage);
            }
          } else {
            // Handle other errors in status
            if (e instanceof Error) {
              this.status.error = e;
            } else {
              this.status.error = new Error(
                'Something went wrong while fetching messages.',
              );
            }
          }
        }

        return this.getMessages;
      },
      setMessages(messages: AllFormValues[]) {
        this.messages = messages;
      },
      removeMessage(messageId: string | number) {
        this.messages = this.messages.filter(
          (msg: AllFormValues) => msg.id !== messageId,
        );
      },
      isChatbotPermissionError(error: unknown): boolean {
        // Check if error contains the specific chatbot permission error structure
        if (typeof error === 'object' && error !== null) {
          const errorObj = error as any;

          // Check for our custom chatbot permission error in various locations
          const detailLocations = [
            errorObj.response?.data?.detail,
            errorObj.detail,
            errorObj.response?.detail,
            // AWS Amplify might store it differently
            errorObj.response?.data,
            errorObj.data,
          ];

          for (const detail of detailLocations) {
            if (detail?.type === 'chatbot_authorization_error') {
              return true;
            }
            // Also check if detail is a string containing our error info
            if (
              typeof detail === 'string' &&
              detail.includes('chatbot_authorization_error')
            ) {
              return true;
            }
          }

          // Check AWS Amplify specific error properties
          if (errorObj._response) {
            // Try to parse the _response body if it's a string
            if (typeof errorObj._response.body === 'string') {
              try {
                const parsedBody = JSON.parse(errorObj._response.body);
                if (parsedBody.detail?.type === 'chatbot_authorization_error') {
                  return true;
                }
              } catch (_) {
                // Could not parse response body as JSON
              }
            }
            // Check if response body is already an object
            if (typeof errorObj._response.body === 'object') {
              if (
                errorObj._response.body?.detail?.type ===
                'chatbot_authorization_error'
              ) {
                return true;
              }
            }
          }
        }
        return false;
      },
      async postMessage(model: AllFormValues, append?: boolean) {
        try {
          await this.fetchMessages();

          // Check if a permission form was just added by fetchMessages
          const lastMessage = this.messages[this.messages.length - 1];
          if (lastMessage && isConsentForm(lastMessage)) {
            return; // Don't proceed with posting if permission is needed
          }

          if (append) {
            // placeholder id & timestamp, overwritten upon response by an id & timestamp issued by the backend
            const { id, timestamp } = generate_placeholder_message_metadata();
            model.id = id;
            model.timestamp = timestamp;
            this.messages.push(model);
          }

          this.isTyping = true;

          for await (const incomingMessage of streamMessage(mapToDto(model))) {
            const lastMessage: AllFormValues =
              this.messages[this.messages.length - 1];

            if (
              append &&
              incomingMessage.messageLayout === 'new_id_layout' &&
              lastMessage.messageLayout === 'user_text_layout'
            ) {
              // replace the user text message metadata with that issued by the backend
              lastMessage.id = incomingMessage.id;
              lastMessage.timestamp = incomingMessage.timestamp;
            } else if (
              incomingMessage.messageLayout === 'assistant_text_layout' &&
              incomingMessage.id === lastMessage.id
            ) {
              // repeatedly overwrite duplicate assistant text messages as each is streamed in with a few more tokens than the last
              this.messages[this.messages.length - 1] = incomingMessage;
            } else if (
              incomingMessage.messageLayout === 'set_incomplete_layout' &&
              incomingMessage.id === model.id
            ) {
              // the data operation sent in the last message failed on the backend, so we are unlocking the form in the chat for resubmission
              for (let i = this.messages.length - 1; i >= 0; --i) {
                if (this.messages[i].id === incomingMessage.id) {
                  this.messages[i].completed = false;
                }
              }
            } else {
              // append a brand new message to the conversation
              this.messages.push(incomingMessage);
            }
          }

          this.isTyping = false;
        } catch (e: unknown) {
          this.isTyping = false;

          // Check for chatbot-specific permission errors
          if (this.isChatbotPermissionError(e)) {
            // Only add permission form if the most recent message isn't already a permission form
            const lastMessage = this.messages[this.messages.length - 1];
            if (!lastMessage || !isConsentForm(lastMessage)) {
              const { id, timestamp } = generate_placeholder_message_metadata();
              const permissionMessage: AllFormValues = {
                messageLayout: 'consent_form_layout',
                message: '',
                timestamp: timestamp,
                id: id,
              };
              this.messages.push(permissionMessage);
            }
          } else {
            // Handle other errors in status
            if (e instanceof Error) {
              this.status.error = e;
            } else {
              this.status.error = new Error(
                'Something went wrong while posting message.',
              );
            }
          }
        }
      },
      async newConversation() {
        try {
          this.status.pending = true;

          const messages = await newConversation();

          this.setMessages(messages);

          this.status.pending = false;
        } catch (e: unknown) {
          if (e instanceof Error) {
            this.status.error = e;
          } else {
            this.status.error = new Error(
              'Something went wrong while fetching messages.',
            );
          }
        }
      },
    },
  },
);
