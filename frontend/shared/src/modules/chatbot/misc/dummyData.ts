import { AllFormValues } from '../types/form-model';

export const MessagesTestData: AllFormValues[] = [
  {
    id: 1,
    timestamp: '2025-02-15T12:00:00Z',
    messageLayout: 'assistant_text_layout',
    message: "Hi there! I'm <PERSON><PERSON><PERSON>, how can I help?",
  },
  {
    id: 2,
    timestamp: '2025-02-15T12:00:00Z',
    messageLayout: 'user__health_score_layout',
    overallScore: 72,
  },
  {
    id: 3,
    timestamp: '2025-02-16T12:00:00Z',
    messageLayout: 'user_text_layout',
    message: "I'd like to update my date of birth.",
  },
  {
    id: 4,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'assistant_text_layout',
    message: 'Sure, please provide your date of birth in the field below.',
  },
  {
    id: 5,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'assistant_text_layout',
    message: 'Thanks.',
  },
  {
    id: 6,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'user__relations_layout',
    completed: false,
    availableFields: null,
    relations: [
      {
        id: null,
        firstName: 'John',
        lastName: 'Doe',
        relationshipType: 'Spouse',
        dateOfBirth: '1990-01-01',
      },
    ],
  },
  {
    id: 7,
    timestamp: '2025-02-18T12:00:00Z',
    messageLayout: 'user__addresses_layout',
    completed: false,
    availableFields: null,
    addresses: [
      {
        id: null,
        addressLineOne: 'ONE',
        addressLineTwo: 'line two',
        addressLineThree: 'line three',
        addressLineFour: 'line four',
        city: 'London',
        postCode: 'AS22ISS',
        countryId: 1,
        movedInDate: '2024-05-05',
        movedOutDate: '2024-05-05',
        isPrimary: false,
      },
      {
        id: null,
        addressLineOne: 'TWO',
        addressLineTwo: 'line two',
        addressLineThree: 'line three',
        addressLineFour: 'line four',
        city: 'London',
        postCode: 'AS22ISS',
        countryId: 1,
        movedInDate: '2024-05-05',
        movedOutDate: '2024-05-05',
        isPrimary: true,
      },
      {
        id: null,
        addressLineOne: 'THREE',
        addressLineTwo: 'line two',
        addressLineThree: 'line three',
        addressLineFour: 'line four',
        city: 'London',
        postCode: 'AS22ISS',
        countryId: 1,
        movedInDate: '2024-05-05',
        movedOutDate: '2024-05-05',
        isPrimary: false,
      },
    ],
  },
];
