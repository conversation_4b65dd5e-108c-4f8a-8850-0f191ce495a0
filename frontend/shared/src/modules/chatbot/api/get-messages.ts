import { apiClient } from '@aventur-shared/services/api';
import { UnauthorizedError } from '@aventur-shared/services/api';
import { dtoToMessageMapper } from '../utils/mappers/dtoToMessageMapper';
import { AllFormValues, AssistantTextFormValues } from '../types/form-model';
import { generate_placeholder_message_metadata } from '../utils/helpers';

export default async (): Promise<AllFormValues[]> => {
  let response: Array<any>;

  try {
    response = await apiClient.get<Promise<Array<any>>>(
      `/api/v2/chatbot/messages`,
    );
  } catch (error) {
    // Let unauthorized errors (including chatbot permission errors) propagate to the store
    if (error instanceof UnauthorizedError) {
      throw error;
    }

    // Check if this is a 401 error in any form and re-throw it
    if (error && typeof error === 'object') {
      const errorObj = error as any;
      // Check various places where the 401 status might be stored
      if (
        errorObj.$metadata?.httpStatusCode === 401 ||
        errorObj._response?.statusCode === 401
      ) {
        throw error;
      }
    }

    // Backstop error displayed to user in the event the backend does not send a viable in-chat error message to the front end via the stream and instead sends an HTTP error. As this error message is not persisted in the backend it will disappear on page refresh
    const { id, timestamp } = generate_placeholder_message_metadata();

    return Promise.resolve([
      {
        id: id,
        timestamp: timestamp,
        messageLayout: 'assistant_text_layout',
        message:
          'Unfortunately our server had an issue retrieving your existing conversation. Please try again in a moment.',
      } as AssistantTextFormValues,
    ]);
  }

  return await Promise.all(
    response.map(async (dto) => {
      return await dtoToMessageMapper(dto);
    }),
  );
};
