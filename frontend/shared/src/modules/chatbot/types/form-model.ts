import { Nullable } from '@aventur-shared/types/Common';
import { MessageLayoutType } from './message';
//

interface FormValues {
  id?: number;
  timestamp?: string;
  messageLayout: string;
}

export interface UserTextFormValues extends FormValues {
  message: Nullable<string>;
}

export interface AssistantTextFormValues extends FormValues {
  message: Nullable<string>;
}

export interface ConsentFormValues extends FormValues {
  message?: Nullable<string>;
}

export interface WithdrawConsentFormValues extends FormValues {
  completed: boolean;
  withdrawn?: boolean; // true if consent was actually withdrawn, false if cancelled
  message?: Nullable<string>;
}

export interface DataFormValues extends FormValues {
  availableFields: string[] | null;
}

export interface EditableDataFormValues extends DataFormValues {
  completed: boolean;
}

export interface Company_FormValues extends DataFormValues {
  email: Nullable<string>;
  phoneNumber: Nullable<string>;
  address: Nullable<string>;
  website: Nullable<string>;
  documentationAddress: Nullable<string>;
}

export interface User_HealthScore_FormValues extends DataFormValues {
  overallScore: Nullable<number>;
}

export interface User_PersonalDetails_FormValues
  extends EditableDataFormValues {
  firstName: Nullable<string>;
  lastName: Nullable<string>;
  email: Nullable<string>;
  dateOfBirth: Nullable<string>;
  phoneNumber: Nullable<string>;
  mobileNumber: Nullable<string>;
}

export interface AddressFormValue {
  id: Nullable<number>;
  addressLineOne: Nullable<string>;
  addressLineTwo: Nullable<string>;
  addressLineThree: Nullable<string>;
  addressLineFour: Nullable<string>;
  city: Nullable<string>;
  postCode: Nullable<string>;
  countryId: Nullable<number>;
  movedInDate: Nullable<string>;
  movedOutDate: Nullable<string>;
  isPrimary: Nullable<boolean>;
}

export interface User_Addresses_FormValues extends EditableDataFormValues {
  addresses: Nullable<AddressFormValue[]>;
}

export interface RelationFormValue {
  id: Nullable<number>;
  firstName: Nullable<string>;
  lastName: Nullable<string>;
  relationshipType: Nullable<string>;
  dateOfBirth: Nullable<string>;
}

export interface User_Relations_FormValues extends EditableDataFormValues {
  relations: Nullable<RelationFormValue[]>;
}

const initialValuesUserText = {
  messageLayout: 'user_text_layout',
  message: '',
};

const initialValuesAssistantText = {
  messageLayout: 'assistant_text_layout',
  message: '',
};

const initialValuesWithdrawConsentForm = {
  messageLayout: 'user__consent_withdraw_layout',
  completed: false,
  withdrawn: false,
  message: '',
};

const initialValues_Company = {
  messageLayout: 'company',
  email: '',
  phoneNumber: '',
  address: '',
  website: '',
  documentationAddress: '',
};

const initialValues_User_PersonalDetails = {
  messageLayout: 'user_personal_details',
  completed: false,
  firstName: '',
  lastName: '',
  email: '',
  dateOfBirth: '',
  phoneNumber: '',
  mobileNumber: '',
};

const initialValues_User_Addresses = {
  messageLayout: 'user__addresses_layout',
  completed: false,
  addresses: [] as AddressFormValue[],
};

const initialValues_User_Relations = {
  messageLayout: 'user__relations_layout',
  completed: false,
  relations: [] as RelationFormValue[],
};

const initialValues = {
  user_text_layout: initialValuesUserText,
  assistant_text_layout: initialValuesAssistantText,
  user__consent_withdraw_layout: initialValuesWithdrawConsentForm,
  company: initialValues_Company,
  user_personal_details: initialValues_User_PersonalDetails,
  user_addresses: initialValues_User_Addresses,
  user_relations: initialValues_User_Relations,
};

export type AllFormValues =
  | FormValues
  | UserTextFormValues
  | AssistantTextFormValues
  | ConsentFormValues
  | WithdrawConsentFormValues
  | Company_FormValues
  | User_HealthScore_FormValues
  | User_Addresses_FormValues
  | User_PersonalDetails_FormValues
  | User_Relations_FormValues;

export const isUserText = (
  value: AllFormValues,
): value is UserTextFormValues => {
  return value.messageLayout === 'user_text_layout';
};

export const isAssistantText = (
  value: AllFormValues,
): value is AssistantTextFormValues => {
  return value.messageLayout === 'assistant_text_layout';
};

export const isConsentForm = (
  value: AllFormValues,
): value is ConsentFormValues => {
  return value.messageLayout === 'consent_form_layout';
};

export const isWithdrawConsentForm = (
  value: AllFormValues,
): value is WithdrawConsentFormValues => {
  return value.messageLayout === 'user__consent_withdraw_layout';
};

export const isCompany = (
  value: AllFormValues,
): value is Company_FormValues => {
  return value.messageLayout === 'company_layout';
};

export const isUser_PersonalDetails = (
  value: AllFormValues,
): value is User_PersonalDetails_FormValues => {
  return value.messageLayout === 'user__personal_details_layout';
};

export const isUser_Addresses = (
  value: AllFormValues,
): value is User_Addresses_FormValues => {
  return value.messageLayout === 'user__addresses_layout';
};

export const isUser_Relations = (
  value: AllFormValues,
): value is User_Relations_FormValues => {
  return value.messageLayout === 'user__relations_layout';
};

export const getInitialValues = (
  messageLayout: MessageLayoutType,
): AllFormValues => {
  return initialValues[messageLayout];
};

export function isEditableDataForm(
  value: AllFormValues,
): value is Extract<AllFormValues, { completed: boolean }> {
  return 'completed' in value;
}
