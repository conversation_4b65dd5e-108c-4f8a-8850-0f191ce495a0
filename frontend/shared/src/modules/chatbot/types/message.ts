import {
  AllFormValues,
  isUser<PERSON><PERSON><PERSON>,
  isUser_Addresses,
  isUser_PersonalDetails,
  isUser_Relations,
  isWithdrawConsentForm,
} from '@aventur-shared/modules/chatbot/types/form-model';

export type MessageLayoutType =
  | 'new_id_layout'
  | 'user_text_layout'
  | 'assistant_text_layout'
  | 'set_incomplete_layout'
  | 'company_layout'
  | 'consent_form_layout'
  | 'user__consent_withdraw_layout'
  | 'user__health_score_layout'
  | 'user__personal_details_layout'
  | 'user__addresses_layout'
  | 'user__relations_layout';

export interface Message {
  id: number;
  timestamp: string;
  data_layout: MessageLayoutType;
}

export interface DataMessage extends Message {
  available_fields: string[] | null;
  data: Record<string, any>;
}

interface Body {
  data_layout: string;
}

export interface UserTextData extends Body {
  message: string;
}

interface DataBody extends Body {
  id: number;
  timestamp: string;
  completed: boolean;
  available_fields: string[];
}

export interface WithdrawConsentData extends DataBody {
  withdrawn?: boolean;
  message?: string;
}

export interface User_PersonalDetails_Data extends DataBody {
  first_name: string;
  last_name: string;
  email: string;
  date_of_birth: string;
  phone_number: string;
  mobile_number: string;
}

export interface User_Addresses_Data extends DataBody {
  addresses: {
    id: number;
    address_line_one: string;
    address_line_two: string;
    address_line_three: string;
    address_line_four: string;
    city: string;
    post_code: string;
    country_id: number;
    moved_in_date: string;
    moved_out_date: string;
    is_primary: boolean;
  }[];
}

export interface User_Relations_Data extends DataBody {
  relations: {
    id: number;
    first_name: string;
    last_name: string;
    relationship_type: string;
    date_of_birth: string;
  }[];
}

export const mapToDto = (messageInformation: AllFormValues) => {
  if (isUserText(messageInformation)) {
    return {
      data_layout: messageInformation.messageLayout,
      message: messageInformation.message,
    } as UserTextData;
  }

  if (isWithdrawConsentForm(messageInformation)) {
    return {
      id: messageInformation.id,
      timestamp: messageInformation.timestamp,
      data_layout: messageInformation.messageLayout,
      completed: messageInformation.completed,
      withdrawn: messageInformation.withdrawn,
      message: messageInformation.message,
    } as WithdrawConsentData;
  }

  if (isUser_PersonalDetails(messageInformation)) {
    return {
      id: messageInformation.id,
      timestamp: messageInformation.timestamp,
      data_layout: messageInformation.messageLayout,
      completed: messageInformation.completed,
      available_fields: messageInformation.availableFields,
      first_name: messageInformation.firstName,
      last_name: messageInformation.lastName,
      email: messageInformation.email,
      date_of_birth: messageInformation.dateOfBirth,
      phone_number: messageInformation.phoneNumber,
      mobile_number: messageInformation.mobileNumber,
    } as User_PersonalDetails_Data;
  }

  if (isUser_Addresses(messageInformation)) {
    return {
      id: messageInformation.id,
      timestamp: messageInformation.timestamp,
      data_layout: messageInformation.messageLayout,
      completed: messageInformation.completed,
      addresses: messageInformation.addresses?.map((address) => ({
        id: address.id,
        address_line_one: address.addressLineOne,
        address_line_two: address.addressLineTwo,
        address_line_three: address.addressLineThree,
        address_line_four: address.addressLineFour,
        city: address.city,
        post_code: address.postCode,
        country_id: address.countryId,
        moved_in_date: address.movedInDate,
        moved_out_date: address.movedOutDate,
        is_primary:
          address.isPrimary === undefined || address.isPrimary === null
            ? false
            : address.isPrimary,
      })),
    } as User_Addresses_Data;
  }

  if (isUser_Relations(messageInformation)) {
    return {
      id: messageInformation.id,
      timestamp: messageInformation.timestamp,
      data_layout: messageInformation.messageLayout,
      completed: messageInformation.completed,
      relations: messageInformation.relations?.map((relation) => ({
        id: relation.id,
        first_name: relation.firstName,
        last_name: relation.lastName,
        relationship_type: relation.relationshipType,
        date_of_birth: relation.dateOfBirth,
      })),
    } as User_Relations_Data;
  }

  throw new Error('Unsupported message type');
};
