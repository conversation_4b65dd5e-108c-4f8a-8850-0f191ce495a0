import { DataMessage, Message } from '../../types/message';

import {
  AllFormValues,
  AssistantTextFormValues,
  Company_FormValues,
  ConsentFormValues,
  UserTextFormValues,
  User_Addresses_FormValues,
  User_HealthScore_FormValues,
  User_PersonalDetails_FormValues,
  User_Relations_FormValues as User_Relations_FormValues,
  WithdrawConsentFormValues,
} from '../../types/form-model';
import {
  HealthScoreApiResponse,
  mapToHealthScoreDTO,
} from '@aventur-shared/modules/clients/api/client/get-client-health-score';
//

interface NewIdMessage extends Message {
  data_layout: 'new_id_layout';
}

interface UserTextMessage extends Message {
  data_layout: 'user_text_layout';
  message: string;
}

interface AssistantTextMessage extends Message {
  data_layout: 'assistant_text_layout';
  message: string;
}

interface SetIncompleteMessage extends Message {
  data_layout: 'set_incomplete_layout';
}

interface WithdrawConsentFormMessage extends Message {
  data_layout: 'user__consent_withdraw_layout';
  completed: boolean;
  message?: string;
}

interface ConsentFormMessage extends Message {
  data_layout: 'consent_form_layout';
  message?: string;
}

interface Company_Message extends DataMessage {
  data_layout: 'company_layout';
}
interface User_HealthScore_Message extends DataMessage {
  data_layout: 'user__health_score_layout';
}

interface User_PersonalDetails_Message extends DataMessage {
  data_layout: 'user__personal_details_layout';
  completed: boolean;
}

interface User_Addresses_Message extends DataMessage {
  data_layout: 'user__addresses_layout';
  completed: boolean;
}

interface User_Relations_Message extends DataMessage {
  data_layout: 'user__relations_layout';
  completed: boolean;
}

const newIdMappings = (msg: NewIdMessage): AllFormValues => ({
  id: msg.id,
  timestamp: msg.timestamp,
  messageLayout: msg.data_layout,
});

const userTextMappings = (msg: UserTextMessage): UserTextFormValues => ({
  id: msg.id,
  timestamp: msg.timestamp,
  messageLayout: msg.data_layout,
  message: msg.message,
});

const assistantTextMappings = (
  msg: AssistantTextMessage,
): AssistantTextFormValues => ({
  id: msg.id,
  timestamp: msg.timestamp,
  messageLayout: msg.data_layout,
  message: msg.message,
});

const setIncompleteMappings = (msg: SetIncompleteMessage): AllFormValues => ({
  id: msg.id,
  messageLayout: msg.data_layout,
});

const withdrawConsentFormMappings = (
  msg: WithdrawConsentFormMessage,
): WithdrawConsentFormValues => ({
  id: msg.id,
  timestamp: msg.timestamp,
  messageLayout: msg.data_layout,
  completed: msg.completed === undefined ? false : msg.completed,
  message: msg.message,
});

const consentFormMappings = (msg: ConsentFormMessage): ConsentFormValues => ({
  id: msg.id,
  timestamp: msg.timestamp,
  messageLayout: msg.data_layout,
  message: msg.message,
});

const company_Mappings = (msg: Company_Message): Company_FormValues => ({
  id: msg.id,
  timestamp: msg.timestamp,
  messageLayout: msg.data_layout,
  availableFields: msg.available_fields,
  email: msg.data.email,
  phoneNumber: msg.data.phone_number,
  address: msg.data.address,
  website: msg.data.website,
  documentationAddress: msg.data.documentation_address,
});

const user_HealthScore_Mappings = (
  msg: User_HealthScore_Message,
): User_HealthScore_FormValues => {
  const healthScore = mapToHealthScoreDTO(msg.data as HealthScoreApiResponse);

  return {
    id: msg.id,
    timestamp: msg.timestamp,
    messageLayout: msg.data_layout,
    availableFields: null,
    overallScore: healthScore.overallScore,
  };
};

const user_PersonalDetails_Mappings = (
  msg: User_PersonalDetails_Message,
): User_PersonalDetails_FormValues => ({
  id: msg.id,
  timestamp: msg.timestamp,
  messageLayout: msg.data_layout,
  completed:
    msg.completed === undefined || msg.completed === null
      ? false
      : msg.completed,
  availableFields: msg.available_fields,
  firstName: msg.data.first_name,
  lastName: msg.data.last_name,
  email: msg.data.email,
  dateOfBirth: msg.data.date_of_birth,
  phoneNumber: msg.data.phone_number,
  mobileNumber: msg.data.mobile_number,
});

const user_Addresses_Mappings = (
  msg: User_Addresses_Message,
): User_Addresses_FormValues => {
  const dataArray = Array.isArray(msg.data) ? msg.data : [msg.data];

  return {
    id: msg.id,
    timestamp: msg.timestamp,
    messageLayout: msg.data_layout,
    completed:
      msg.completed === undefined || msg.completed === null
        ? false
        : msg.completed,
    availableFields: null,
    addresses: dataArray.map((address: any) => ({
      id: address.id === undefined ? null : address.id,
      addressLineOne: address.address_line_one,
      addressLineTwo: address.address_line_two,
      addressLineThree: address.address_line_three,
      addressLineFour: address.address_line_four,
      city: address.city,
      postCode: address.post_code,
      countryId: address.country_id,
      movedInDate: address.moved_in_date,
      movedOutDate: address.moved_out_date,
      isPrimary:
        address.is_primary === undefined || address.is_primary === null
          ? false
          : address.is_primary,
    })),
  };
};

const user_Relations_Mappings = (
  msg: User_Relations_Message,
): User_Relations_FormValues => {
  const dataArray = Array.isArray(msg.data) ? msg.data : [msg.data];

  return {
    id: msg.id,
    timestamp: msg.timestamp,
    messageLayout: msg.data_layout,
    completed:
      msg.completed === undefined || msg.completed === null
        ? false
        : msg.completed,
    availableFields: null,
    relations: dataArray.map((relation: any) => ({
      id: relation.id === undefined ? null : relation.id,
      firstName: relation.first_name,
      lastName: relation.last_name,
      relationshipType: relation.relationship_type,
      dateOfBirth: relation.date_of_birth,
    })),
  };
};

const mapper = (msg: Message) => {
  switch (msg.data_layout) {
    case 'new_id_layout':
      return newIdMappings(msg as NewIdMessage);
    case 'user_text_layout':
      return userTextMappings(msg as UserTextMessage);
    case 'assistant_text_layout':
      return assistantTextMappings(msg as AssistantTextMessage);
    case 'set_incomplete_layout':
      return setIncompleteMappings(msg as SetIncompleteMessage);
    case 'user__consent_withdraw_layout':
      return withdrawConsentFormMappings(msg as WithdrawConsentFormMessage);
    case 'consent_form_layout':
      return consentFormMappings(msg as ConsentFormMessage);
    case 'company_layout':
      return company_Mappings(msg as Company_Message);
    case 'user__health_score_layout':
      return user_HealthScore_Mappings(msg as User_HealthScore_Message);
    case 'user__personal_details_layout':
      return user_PersonalDetails_Mappings(msg as User_PersonalDetails_Message);
    case 'user__addresses_layout':
      return user_Addresses_Mappings(msg as User_Addresses_Message);
    case 'user__relations_layout':
      return user_Relations_Mappings(msg as User_Relations_Message);
    default:
      throw new Error(
        `Unknown message layout: ${msg.data_layout satisfies never}`,
      );
  }
};

export const dtoToMessageMapper = (dto: Message): AllFormValues => mapper(dto);
