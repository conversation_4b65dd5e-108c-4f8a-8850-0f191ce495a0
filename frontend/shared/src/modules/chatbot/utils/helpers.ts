import { Nullable } from '@aventur-shared/types/Common';
import { Ref } from 'vue';
import { AllFormValues } from '../types/form-model';

export const scrollToBottom = (
  chatContainer: Ref<HTMLElement | null>,
): void => {
  if (chatContainer.value) {
    chatContainer.value.scrollTo({
      top: chatContainer.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

const formatToHammerCase = (key: string): string => {
  return key
    .split('_')
    .filter((word) => word) // Removes empty strings
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

const formatAmount = (
  value: string | number,
  precision: number = 2,
  compact: boolean = false,
  type: 'currency' | 'percent' = 'currency',
) => {
  if (typeof value === 'number') {
    if (type === 'currency') {
      return new Intl.NumberFormat('en-UK', {
        style: 'currency',
        currency: 'GBP',
        notation: compact ? 'compact' : 'standard',
        minimumFractionDigits: precision,
        maximumFractionDigits: precision,
      }).format(value);
    } else if (type === 'percent') {
      return new Intl.NumberFormat('en-UK', {
        style: 'percent',
        minimumFractionDigits: precision,
        maximumFractionDigits: precision,
      }).format(value);
    }
  }
};

function formatToUKDateIntl(
  isoDate: Nullable<string> | undefined,
): Nullable<string> {
  if (
    !isoDate ||
    isoDate === null ||
    isoDate === undefined ||
    isoDate === '' ||
    isoDate === 'null'
  ) {
    return '';
  }

  const date = new Date(isoDate);
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date.');
  }

  const formatter = new Intl.DateTimeFormat('en-GB');
  return formatter.format(date);
}

function stripEmptyValues(schema: AllFormValues) {
  return Object.entries(schema).filter(
    ([_, value]) => value !== null && value !== undefined && value !== '',
  );
}

function formatToChatFeedTimestamp(message: AllFormValues): string {
  if (!message.timestamp) return '';
  const date = new Date(message.timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const prefix = message.messageLayout === 'user_text_layout' ? 'You' : 'Avril';
  return `${prefix} ${hours}:${minutes}`;
}

function generate_placeholder_message_metadata(): {
  id: number;
  timestamp: string;
} {
  const ts = new Date();
  const id = ts.getTime() * 1000;
  const timestamp = ts.toISOString();
  return { id, timestamp };
}

export {
  formatToHammerCase,
  formatAmount,
  formatToUKDateIntl,
  stripEmptyValues,
  formatToChatFeedTimestamp,
  generate_placeholder_message_metadata,
};
