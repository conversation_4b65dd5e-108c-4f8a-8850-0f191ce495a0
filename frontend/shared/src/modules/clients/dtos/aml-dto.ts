interface IdentityValidationSearchCriteriaDTO {
  first_name: string;
  last_name: string;
  address: string;
  city: string;
  post_code: string;
  country_code: string;
  date_of_birth: string;
}

interface IdentidyValidationResultDetailDTO {
  code: string;
  description: string;
}

export interface IdentityValidationResultDTO {
  result: string;
  date: string;
  detail: IdentidyValidationResultDetailDTO[];
  criteria: IdentityValidationSearchCriteriaDTO;
}

export interface Role {
  title: string;
}

export interface Match {
  id: string;
  first_name: string;
  last_name: string;
  image: string;
  categories: string[];
  roles: Role[];
  match_status: number;
}

export interface ClientMonitoringListDTO {
  id: string;
  matches: Match[];
  status: string;
}
