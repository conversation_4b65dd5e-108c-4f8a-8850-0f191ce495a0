<template>
  <input-wrapper
    :name="props.name"
    :error-message="errorMessage"
    :label="label"
    :is-required="isRequired"
  >
    <simple-input
      :id="name"
      :value="value"
      :model-value="modelValue"
      :name="name"
      :type="type || 'text'"
      :autocomplete="autocomplete"
      :disabled="disabled"
      :placeholder="placeholder"
      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm disabled:bg-gray-100 disabled:text-gray-400 disabled:disabled:shadow-none group-[.text-right]:text-right sm:text-sm lg:text-base"
      :class="{
        'border-red-600': !!errorMessage,
        'text-red-600': !!errorMessage,
        'focus:border-primary focus:ring-primary': !isReadonly,
        'cursor-not-allowed bg-gray-100 text-gray-400 focus:border-gray-300 focus:ring-0':
          isReadonly,
      }"
      :is-readonly="isReadonly"
      @blur="$emit('blur')"
      @update:model-value="(val) => $emit('update:modelValue', val)"
      @focus="$emit('focus')"
      @focusout="$emit('focusout')"
    />
    <span
      v-if="props.hint && !props.errorMessage"
      class="flex py-1 text-xs leading-tight text-gray-500"
      >Hint: {{ hint }}</span
    >
  </input-wrapper>
</template>

<script setup lang="ts">
  import InputWrapper from '../InputWrapper.vue';
  import SimpleInput from './SimpleInput.vue';

  const props = defineProps<{
    id: string;
    value: string | number | null;
    modelValue: string | number | null;
    autocomplete?: string;
    name: string;
    label: string;
    placeholder?: string;
    hint?: string;
    disabled?: boolean;
    isRequired?: boolean;
    errorMessage?: string;
    type?: string;
    isReadonly?: boolean;
  }>();

  defineEmits(['update:modelValue', 'blur', 'focus', 'focusout']);
</script>
