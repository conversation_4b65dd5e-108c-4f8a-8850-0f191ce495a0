<template>
  <div
    v-if="show"
    class="relative flex justify-between rounded border px-4 py-3"
    :class="
      classes.container[type as string] ||
      'bg-slate-100 border-slate-200 text-slate-700'
    "
    role="alert"
  >
    <div class="flex items-center">
      <span :class="{ 'mr-2': icon }">
        <template v-if="isError && icon">
          <exclamation-circle-icon
            class="size-6 text-red-400"
            aria-hidden="true"
          />
        </template>
        <template v-if="isWarning && icon">
          <exclamation-triangle-icon
            class="size-6 text-yellow-400"
            aria-hidden="true"
          />
        </template>
        <template v-if="isSuccess && icon">
          <check-circle-icon class="size-6 text-green-400" aria-hidden="true" />
        </template>
        <template v-if="isInfo && icon">
          <information-circle-icon
            class="size-6 text-blue-400"
            aria-hidden="true"
          />
        </template>
        <template v-if="isSecondary && icon">
          <information-circle-icon
            class="size-6 text-gray-400"
            aria-hidden="true"
          />
        </template>
      </span>
      <strong v-if="title" class="font-bold">{{ title }}</strong>
      <span v-if="message" class="block sm:inline">{{ message }}</span>
      <div
        v-else
        class="flex-1"
        :class="classes.text[type as string] || 'text-slate-700'"
      >
        <slot />
      </div>
    </div>
    <span v-if="dismissible" class="inset-y-0 right-0">
      <x-mark-icon class="size-4 cursor-pointer opacity-50" @click="onDismiss">
        <title>Close</title>
      </x-mark-icon>
    </span>
  </div>
</template>

<script setup lang="ts">
  import {
    CheckCircleIcon,
    ExclamationCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    XMarkIcon,
  } from '@heroicons/vue/24/solid';
  import { computed, ref } from 'vue';

  const onDismiss = () => {
    props.onDismiss && props.id
      ? props.onDismiss(props.id)
      : (show.value = false);
  };

  const props = withDefaults(
    defineProps<{
      type?: 'error' | 'warning' | 'success' | 'info' | 'secondary';
      icon?: boolean;
      title?: string;
      message?: string;
      id?: number;
      dismissible?: boolean;
      onDismiss?: (id: number) => void;
    }>(),
    {
      id: undefined,
      type: undefined,
      icon: true,
      title: undefined,
      message: undefined,
      dismissible: false,
      onDismiss: undefined,
    },
  );

  const show = ref<boolean>(true);

  const isError = computed(() => props.type === 'error');
  const isWarning = computed(() => props.type === 'warning');
  const isSuccess = computed(() => props.type === 'success');
  const isInfo = computed(() => props.type === 'info');
  const isSecondary = computed(() => props.type === 'secondary');

  const classes = computed(() => ({
    container: {
      error: 'bg-red-100 border-red-200 text-red-700',
      warning: 'bg-yellow-100 border-yellow-200 text-yellow-700',
      success: 'bg-green-100 border-green-200 text-green-700',
      info: 'bg-blue-100 border-blue-200 text-blue-700',
      secondary: 'bg-gray-100 border-gray-200 text-gray-700',
    },
    text: {
      error: 'ml-2 text-red-700',
      warning: 'ml-2 text-yellow-700',
      success: 'ml-2 text-green-700',
      info: 'ml-2 text-blue-700',
      secondary: 'ml-2 text-gray-700',
    },
  }));
</script>
