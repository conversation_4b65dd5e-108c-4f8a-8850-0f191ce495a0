import { find, map, sortBy, trim } from 'lodash';
import { _ActionsTree, _GettersTree, defineStore } from 'pinia';
import { apiClient } from '@aventur-shared/services/api';
import { Options } from '@aventur-shared/types/Common';
import {
  AdviceGroup,
  AdviceType,
  AdvisorRole,
  Cashflow,
  Country,
  DocumentTemplateTypeDTO,
  NoEmailReason,
  ProductType,
  ProductTypeGroup,
  Provider,
  RefDataBlobDTO,
  Goal as RefGoal,
} from '@aventur-shared/types/store/Refdata';
import { RelationshipType } from '@aventur-shared/types/Relationship';
import { objectivesExamples } from '@aventur-shared/stores/objectives.data';

export type Goal = RefGoal & { objectivesExamples: string[] };

export type RefDataState = {
  countries: Country[];
  nationalities: Options[];
  providers: Provider[];
  cashflows: Cashflow[];
  goals: Goal[];
  products: ProductType[];
  productTypeGroups: ProductTypeGroup[];
  adviceGroups: AdviceGroup[];
  adviceTypes: AdviceType[];
  genders: Options[];
  titles: Options[];
  maritalStatuses: Options[];
  relationshipTypes: Options[];
  clientSources: Options<number>[];
  clientStatuses: Options<number>[];
  clientTypes: Options[];
  holdingStatuses: Options<number>[];
  holdingsReviewableInACase: Options<number>[];
  portfolioModels: Options[];
  advisorsRoles: AdvisorRole[];
  noEmailReasons: NoEmailReason[];
  documentTemplateTypes: DocumentTemplateTypeDTO;
  riskLevels: Options<number>[];
  refdata_loaded: boolean;
};

const mapGoals = (goals: RefGoal[]): Goal[] => {
  return goals.map((goal) => ({
    ...goal,
    objectivesExamples: objectivesExamples[goal.id],
  }));
};

const mapRelationshipTypes = (relationshipTypes: Options[]): Options[] => {
  const mappings = {
    [RelationshipType.BusinessPartner]: 'Business Partner',
    [RelationshipType.BusinessDirector]: 'Business (Director)',
    [RelationshipType.BusinessShareholder]: 'Business (Shareholder)',
    [RelationshipType.TrustBeneficiary]: 'Trust (Beneficiary)',
    [RelationshipType.TrustTrustee]: 'Trust (Trustee)',
  };

  return map(relationshipTypes, (relationshipType) => ({
    id: relationshipType.id,
    name: mappings[String(relationshipType.id)] || relationshipType.name,
  }));
};

const mapCountries = (countries: Country[]): Country[] =>
  map(countries, (country) => ({
    ...country,
    name: trim(country.name, '\'"'),
  }));

const mapClientSources = (clientSources: Options[]): Options[] =>
  map(clientSources, (clientSource) => {
    if (clientSource.name.startsWith('Acquisition')) {
      return {
        label: 'Acquisitions',
        id: clientSource.id,
        name: clientSource.name.replace(/\s/, ' - '),
      };
    }
    if (clientSource.name.startsWith('Referral')) {
      return {
        label: 'Referrals',
        id: clientSource.id,
        name: clientSource.name.replace(/\s/, ' - '),
      };
    }
    return { label: 'General', ...clientSource };
  });

const mapClientStatuses = (clientStatuses: Options[]): Options[] =>
  map(sortBy(clientStatuses, 'name'), (clientStatus) => {
    return {
      id: clientStatus.id,
      name: clientStatus.name.replace(
        /(\w+)\s(.+)/,
        (s_, core, ext) => `${core} - ${ext}`,
      ),
    };
  });

const mapPortfolioModels = (portfolioModels: Options[]): Options[] =>
  map(portfolioModels, (portfolioModel) => {
    const group = /Aventur (Active|Passive|Income|ESG) [1-5]/.exec(
      portfolioModel.name,
    );
    if (group?.length) {
      return {
        label: `Aventur ${group[1]}`,
        id: portfolioModel.id,
        name: portfolioModel.name,
      };
    }
    return { label: 'Other', ...portfolioModel };
  });

interface Getters extends _GettersTree<RefDataState> {
  getGoals(state: RefDataState): Goal[];
  getGoalById(state: RefDataState): (goalId: number) => Goal | undefined;
  getProductTypes: (state: RefDataState) => ProductType[];
}

type Actions = _ActionsTree;

export const useRefData = defineStore<
  'refdata',
  RefDataState,
  Getters,
  Actions
>('refdata', {
  state: () =>
    ({
      nationalities: [],
      providers: [],
      cashflows: [],
      goals: [],
      products: [],
      productTypeGroups: [],
      adviceTypes: [],
      genders: [],
      titles: [],
      maritalStatuses: [],
      relationshipTypes: [],
      clientSources: [],
      clientStatuses: [],
      clientTypes: [],
      countries: [],
      holdingStatuses: [],
      holdingsReviewableInACase: [],
      adviceGroups: [],
      portfolioModels: [],
      advisorsRoles: [],
      noEmailReasons: [],
      documentTemplateTypes: [],
      riskLevels: [],
      refdata_loaded: false,
    }) as RefDataState,
  getters: {
    isReady: (state) => state.refdata_loaded,
    getProviders: (state) => sortBy(state.providers, 'name'),
    getAdviceTypes: (state) => sortBy(state.adviceTypes, 'name'),
    getAdviceGroups: (state) => sortBy(state.adviceGroups, 'name'),
    getRelationshipTypes: (state) => state.relationshipTypes,
    getProductTypes: (state) => state.products,
    getProductTypeGroups: (state) => state.productTypeGroups ?? [],
    getCashflows: (state) => state.cashflows,
    getCashflowMap: (state) => {
      const flows = new Map();
      state.cashflows.forEach(function (item: any) {
        flows.set(item.id, {
          flow_type: item.flow_type,
          group_id: item.id,
          group_name: item.group_name,
          name: item.name,
        });
      });
      return flows;
    },
    getCashflowGroups: (
      state,
    ): Array<{
      flow_type: 1 | 2;
      group_id: number;
      group_name: string;
    }> => {
      return [
        ...new Map(
          state.cashflows.map((item: any) => [
            item['income_expenditure_group_id'],
            {
              flow_type: item.flow_type,
              group_id: item.income_expenditure_group_id,
              group_name: item.group_name,
            },
          ]),
        ).values(),
      ];
    },
    getGoals: (state) => state.goals ?? [],
    getGoalById(state) {
      return (id: number) => find(state.goals, { id });
    },
    getNonDefaultGoals: (state) => {
      return state.goals
        .filter((item: any) => !item.is_default)
        .map((item: any) => {
          return { id: item.id, name: item.name };
        });
    },
    getCashflowGroupNameFromId() {
      return (id: number) => this.getCashflowMap.get(id).group_name;
    },
    getCashflowNameFromId() {
      return (id: number) => this.getCashflowMap.get(id).name;
    },
    getCashflowGroupsForType() {
      return (type: number) => {
        return this.getCashflowGroups
          .filter((item: any) => item.flow_type == type)
          .map((item: any) => {
            return { id: item.group_id, name: item.group_name };
          });
      };
    },
    getCashflowsForType() {
      return (type: number): [] => {
        return this.cashflows
          .filter((item: any) => item.flow_type == type)
          .map((item: any) => item.id);
      };
    },
    getCashflowOptionsForGroup() {
      return (group_id: number | null): Options<number>[] => {
        if (group_id) {
          const data: any = this.cashflows.filter(
            (item: any) => item.income_expenditure_group_id == group_id,
          );
          return data.map((item: any) => {
            return { id: item.id, name: item.name };
          });
        } else {
          return [];
        }
      };
    },
    getProductTypesByID() {
      return (id: number | null) => {
        const prod = this.getProductType.find((item: any) => item.id == id);

        const group = this.getProductGroups.find(
          (item: any) => item.id == prod.product_type_group_id,
        );

        return {
          id: id,
          name: prod.name,
          group: group.name,
        };
      };
    },
    getProductTypeByName() {
      return (name: string) => {
        return this.getProductType.find((item: any) => item.name === name);
      };
    },
    getProviderByName() {
      return (name: string) => {
        return this.getProviders.find((item: any) => item.name === name);
      };
    },
    getProviderByID() {
      return (id: number) => {
        return this.getProviders.find((item: any) => item.id == id);
      };
    },
    getNationalities: (state) => state.nationalities ?? [],
    getNationalityByID() {
      return (id: number) => {
        return this.getNationalities.find((item: any) => item.id == id);
      };
    },
    getGenders: (state) => state.genders ?? [],
    getTitles: (state) => state.titles ?? [],
    getMaritalStatuses: (state) => state.maritalStatuses ?? [],
    getClientSources: (state) => state.clientSources ?? [],
    getClientStatuses: (state) => state.clientStatuses ?? [],
    getClientTypes: (state) => state.clientTypes ?? [],
    getPortfolioModels: (state) => state.portfolioModels ?? [],
    getProductsByGroupId(): (groupId: number) => ProductType[] {
      return (groupId: number) => {
        return this.products.filter(
          (item: ProductType) => item.product_type_group_id == groupId,
        );
      };
    },
    getAssets(): ProductTypeGroup[] {
      return this.productTypeGroups.filter(
        (item: ProductTypeGroup) => item.asset_or_debt === 'asset',
      );
    },
    getDebts(): ProductTypeGroup[] {
      return this.productTypeGroups.filter(
        (item: ProductTypeGroup) => item.asset_or_debt === 'debt',
      );
    },
    getProductById(): (productId: number) => ProductType {
      return (productId: number) => {
        return this.products.find((item: ProductType) => item.id == productId);
      };
    },
    getProductGroupById(): (groupId: number) => ProductTypeGroup {
      return (groupId: number) => {
        return this.productTypeGroups.find(
          (item: ProductType) => item.id == groupId,
        );
      };
    },
    getIncomeGroups(): Options<number>[] {
      return this.getCashflowGroupsForType(1);
    },
    getIncomeGroupById() {
      return (id: number) => {
        return this.getIncomeGroups.find(
          (item: Options<number>) => item.id === id,
        ).name;
      };
    },
    getCountries: (state) => state.countries,
    getCountryByCode(state) {
      return (code: string) => {
        return state.countries.find((country: Country) =>
          [country.iso_alpha2, country.iso_alpha3].includes(code),
        );
      };
    },
    getCountryById(state) {
      return (id: Country['id']) => {
        return state.countries.find((country: Country) => country.id === id);
      };
    },
    getAdviceGroupsByProductTypeGroupId() {
      return (productTypeGroupId: number) => {
        return this.adviceGroups
          .filter(
            (item: AdviceGroup) =>
              item.product_type_group_id == productTypeGroupId,
          )
          .map((item: AdviceGroup) => {
            return { id: item.id, name: item.name };
          });
      };
    },
    getAdviceTypesForProposedAccountsByTypeId: (state) => {
      return (accountTypeId: number) => {
        const productTypeGroupId = state.products.find(
          (product) => product.id === accountTypeId,
        )?.product_type_group_id;
        const compatibleAdviceTypes =
          state.productTypeGroups.find(
            (group) => group.id === productTypeGroupId,
          )?.compatible_advice_types || [];
        return state.adviceTypes.filter(
          (adviceType) =>
            adviceType.proposed &&
            compatibleAdviceTypes.includes(adviceType.id),
        );
      };
    },
    getAdviceTypesForProposedAccountsByTypeGroupId: (state) => {
      return (accountTypeGroupId: number) => {
        const compatibleAdviceTypes =
          state.productTypeGroups.find(
            (group) => group.id === accountTypeGroupId,
          )?.compatible_advice_types || [];
        return state.adviceTypes.filter(
          (adviceType) =>
            adviceType.proposed &&
            compatibleAdviceTypes.includes(adviceType.id),
        );
      };
    },
    getAdviceTypesForExistingAccountsByTypeGroupId: (state) => {
      return (accountTypeGroupId: number) => {
        const compatibleAdviceTypes =
          state.productTypeGroups.find(
            (group) => group.id === accountTypeGroupId,
          )?.compatible_advice_types || [];
        return state.adviceTypes.filter(
          (adviceType) =>
            adviceType.existing &&
            compatibleAdviceTypes.includes(adviceType.id),
        );
      };
    },
    getHoldingStatuses: (state) => state.holdingStatuses,
    getHoldingsReviewableInACase: (state) => state.holdingsReviewableInACase,
    getAdvisorsRoles: (state) => state.advisorsRoles,
    getNoEmailReasons: (state) => state.noEmailReasons,
    getRiskLevels: (state) => state.riskLevels,
  },
  actions: {
    async getRefData() {
      try {
        const {
          countries,
          nationalities,
          genders,
          goals,
          providers,
          cashflows,
          product_types,
          product_type_groups,
          advice_types,
          advice_groups,
          personal_titles,
          marital_statuses,
          relationship_types,
          client_sources,
          client_statuses,
          client_types,
          portfolio_models,
          holding_statuses,
          no_email_reasons,
          assignee_groups,
          risk_level,
        } = await apiClient.get<RefDataBlobDTO>('/api/v2/refdata');

        const holdingsReviewableInACase = await apiClient.get(
          '/api/v1/refdata/statuses',
          {
            category: 'reviewable_in_a_case',
          },
        );

        this.countries = mapCountries(countries as Country[]);
        this.nationalities = nationalities;
        this.providers = providers;
        this.cashflows = cashflows;
        this.goals = mapGoals(goals as RefGoal[]);
        this.products = product_types;
        this.productTypeGroups = product_type_groups;
        this.adviceTypes = advice_types;
        this.adviceGroups = advice_groups;
        this.genders = genders;
        this.titles = personal_titles;
        this.maritalStatuses = marital_statuses;
        this.relationshipTypes = mapRelationshipTypes(
          relationship_types as Options[],
        );
        this.clientSources = mapClientSources(client_sources as Options[]);
        this.clientStatuses = mapClientStatuses(client_statuses as Options[]);
        this.clientTypes = client_types as Options[];
        this.holdingStatuses = holding_statuses;
        this.holdingsReviewableInACase = holdingsReviewableInACase;
        this.portfolioModels = mapPortfolioModels(
          portfolio_models as Options[],
        );
        this.advisorsRoles = assignee_groups;
        this.noEmailReasons = no_email_reasons;
        this.riskLevels = risk_level;
        this.refdata_loaded = true;
      } catch (error) {
        return error;
      }
    },
  },
});
