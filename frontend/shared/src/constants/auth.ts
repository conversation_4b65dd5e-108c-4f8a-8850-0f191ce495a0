export const signUpAttributes = ['given_name', 'family_name'];

export const formFields = {
  signIn: {
    username: {
      label: 'Username',
      placeholder: 'Your email',
    },
  },
  signUp: {
    given_name: {
      order: 1,
      label: 'First name',
      placeholder: 'Your first name',
      isRequired: true,
    },
    family_name: {
      order: 2,
      label: 'Last name',
      placeholder: 'Your last name',
      isRequired: true,
    },
    email: {
      order: 3,
      placeholder: 'Your valid email',
    },
    password: {
      order: 4,
      isRequired: false,
    },
    confirm_password: {
      order: 5,
      isRequired: false,
    },
  },
};

export type SignupFormData = FormData & {
  given_name?: string;
  family_name?: string;
  email?: string;
  password?: string;
  confirm_password?: string;
  acknowledgement?: string;
};

export type SignupRequestData = {
  options: {
    autoSignIn: boolean;
    userAttributes: { given_name: string; family_name: string; email: string };
  };
  password: string;
  username: string;
};
