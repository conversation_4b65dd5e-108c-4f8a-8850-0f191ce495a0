import { createApp } from 'vue';

import { Amplify } from 'aws-amplify';
import AmplifyVue from '@aws-amplify/ui-vue';
import { fetchAuthSession } from 'aws-amplify/auth';
import { createPinia } from 'pinia';
import { vMaska } from 'maska/vue';
import VueTippy from 'vue-tippy';
import Toast from 'vue-toastification';
import * as ConfirmDialog from 'vuejs-confirm-dialog';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

import { API_NAME } from '@aventur-shared/services/api';
import { useAPIState } from '@aventur-shared/composables/useAPIState';
import { toastPluginOptions } from '@aventur-shared/composables/useToast';
import {
  amplitude,
  breakpoints,
  yupValidationMethods,
} from '@aventur-shared/plugins';

import sentry from '@/plugins/sentry';

import App from './App.vue';
import router from './router';

import './assets/css/style.pcss';

import { ITippyConfig } from '@aventur-shared/types/ITippy';
const TippyConfig: ITippyConfig = {
  directive: 'tippy',
  defaultProps: {
    placement: 'top',
    delay: [500, null],
  },
};

const toastOptions = {
  ...toastPluginOptions,
};

import awsConfig from './aws-exports';
//

Amplify.configure(awsConfig, {
  API: {
    REST: {
      headers: async (options: { apiName: string }) => {
        if (options.apiName !== API_NAME) {
          return {} as Record<string, string>;
        }

        const { idToken } = (await fetchAuthSession()).tokens ?? {};
        return {
          Authorization: `Bearer ${idToken}`,
        };
      },
      retryStrategy: {
        strategy: 'no-retry',
      },
    },
  },
});

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

router.beforeEach((/* to, from */) => {
  const { setError: setAPIError } = useAPIState();
  setAPIError(undefined); // clear API error if any
});

const app = createApp(App);

// directives
app.directive('maska', vMaska);

app.use(sentry);
app.use(amplitude);
app.use(breakpoints);
app.use(router);
app.use(AmplifyVue);
app.use(pinia);
app.use(ConfirmDialog);
app.use(Toast, toastOptions);
app.use(yupValidationMethods);
app.use(VueTippy, TippyConfig);

app.mount('#app');
