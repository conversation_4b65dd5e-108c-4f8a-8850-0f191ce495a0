import {
  apiClient,
  parseErrorFromResponse,
} from '@aventur-shared/services/api';
import {
  getMonitoringList,
  postIdentityValidationCheck,
} from '@aventur-shared/modules/clients/api';
import { onMounted, ref } from 'vue';
import {
  ClientMonitoringListDTO,
  useClientStore,
} from '@aventur-shared/modules/clients';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { useToast } from '@aventur-shared/composables/useToast';

export function useAMLCheck(clientId) {
  const { getIdentityValidationStatus: amlState } =
    storeToRefs(useClientStore());

  const identityValidationLoading = ref(false);
  const monitoringListLoading = ref(false);
  const monitoringListStatus = ref<number | null>(null);
  const monitoringDetail = ref<ClientMonitoringListDTO | null>(null);
  const toast = useToast();
  const { loadClientIdentityVerificationStatus } = useClientStore();

  const identityVerificationDetail = computed(() => {
    if (identityValidationLoading.value) {
      return { result: 'Loading...', date: '' };
    }
    if (amlState.value === null) {
      return { result: 'Required', date: '' };
    }
    if (amlState.value.result === null) {
      return { result: 'Fail', date: '' };
    }
    return amlState.value;
  });

  const runIdentityValidationCheck = async () => {
    if (clientId.value) {
      identityValidationLoading.value = true;
      try {
        await apiClient.get(
          `/api/internal/v1/clients/${clientId.value}/is-aml-ready`,
        );
        await postIdentityValidationCheck(clientId.value);
        await loadClientIdentityVerificationStatus(clientId.value);
        await loadMonitoringList();
      } catch (error: any) {
        toast.error(
          `Error validating identity '${error.response.statusCode}' - ${error.response.data.detail}`,
        );
      } finally {
        identityValidationLoading.value = false;
      }
    }
  };

  const postMatchStatusUpdate = async (session_id, match_id, match_status) => {
    console.log(`${session_id} ${match_id} ${match_status}`);
    try {
      await apiClient.post(
        `/api/internal/v1/clients/${clientId.value}/aml/monitoring`,
        {
          session_id,
          match_id,
          match_status,
        },
      );
    } catch (error: any) {
      toast.error(
        `Error validating identity '${error.response.statusCode}' - ${error.response.data.detail}`,
      );
    }
  };

  const loadMonitoringList = async () => {
    if (clientId.value) {
      monitoringListLoading.value = true;
      try {
        monitoringDetail.value = await getMonitoringList(clientId.value);
        monitoringListStatus.value = 1;
      } catch (error: any) {
        switch (error?.response?.statusCode) {
          case 400: {
            monitoringDetail.value = null;
            monitoringListStatus.value = 2;
            break;
          }
          case 404: {
            monitoringDetail.value = null;
            monitoringListStatus.value = 3;
            break;
          }
          default: {
            const message = parseErrorFromResponse(error);
            monitoringListStatus.value = 4;
            toast.error(`Error getting monitoring list: ${message}`);
          }
        }
      } finally {
        monitoringListLoading.value = false;
      }
    }
  };

  const matchStatusList = [
    { label: 'Not Reviewed', value: 0 },
    { label: 'Review Updates', value: 1 },
    { label: 'Match', value: 2 },
    { label: 'No Match', value: 3 },
    { label: 'Not Sure', value: 4 },
  ];

  onMounted(async () => {
    await loadMonitoringList();
    if (amlState.value === null) {
      await loadClientIdentityVerificationStatus(clientId.value);
    }
  });

  return {
    runIdentityValidationCheck,
    loadMonitoringList,
    monitoringListLoading,
    identityValidationLoading,
    monitoringDetail,
    identityVerificationDetail,
    monitoringListStatus,
    matchStatusList,
    postMatchStatusUpdate,
  };
}
