<template>
  <client-profile-layout theme="light">
    <container fluid class="mb-8 bg-white">
      <container class="border-b border-gray-200 bg-white py-4">
        <h2 class="text-2xl font-semibold">Anti Money Laundering</h2>
      </container>
    </container>
    <container>
      <div class="grid gap-4">
        <alert
          v-show="!profileCompleted"
          type="warning"
          class="my-4 text-sm"
          data-testid="incomplete-profile-warning"
        >
          Client profile is incomplete. Please,
          <router-link
            :to="`/clients/${clientId}/factfind`"
            class="whitespace-nowrap underline hover:cursor-pointer hover:text-blue-600 hover:no-underline"
            >click here
          </router-link>
          to provide the required details before proceeding with AML checks.
        </alert>
        <box>
          <box-section
            class="flex items-center justify-between"
            divider="bottom"
          >
            <h3 class="text-base font-semibold text-gray-900">
              Identity Verification
            </h3>
            <custom-button
              theme="primary"
              class="-my-2"
              :disabled="!profileCompleted"
              :is-busy="identityValidationLoading"
              data-testid="run-aml-check"
              @click="runIdentityValidationCheck"
              >Run Check
            </custom-button>
          </box-section>
          <box-section>
            <div
              data-testid="identity-verification-result"
              class="flex flex-row items-center gap-2"
            >
              <check-circle-icon
                v-if="identityVerificationDetail.result === 'Pass'"
                class="size-5"
                style="fill: green"
              />
              <question-mark-circle-icon
                v-else-if="identityVerificationDetail.result === 'Refer'"
                class="size-5"
                style="fill: orange"
              />
              <exclamation-circle-icon
                v-else
                class="size-5"
                style="fill: red"
              />
              <p>{{ identityVerificationDetail.result }}</p>
              <p>{{ identityVerificationDetail.date }}</p>
            </div>
          </box-section>
          <box-section
            divider="top"
            v-if="!identityValidationLoading && amlState"
          >
            <h3 class="text-base font-semibold text-gray-900">
              Search Criteria
            </h3>
            <div class="grid grid-cols-2 gap-x-6 gap-y-2">
              <div class="font-medium">First Name</div>
              <div>{{ amlState.criteria.first_name }}</div>

              <div class="font-medium">Last Name</div>
              <div>{{ amlState.criteria.last_name }}</div>

              <div class="font-medium">Address</div>
              <div>{{ amlState.criteria.address }}</div>

              <div class="font-medium">City</div>
              <div>{{ amlState.criteria.city }}</div>

              <div class="font-medium">Post Code</div>
              <div>{{ amlState.criteria.post_code }}</div>

              <div class="font-medium">Country</div>
              <div>{{ amlState.criteria.country_code }}</div>

              <div class="font-medium">Date of Birth</div>
              <div>{{ amlState.criteria.date_of_birth }}</div>
            </div>
          </box-section>
          <box-section
            divider="top"
            v-if="
              identityVerificationDetail.result !== 'Fail' &&
              !identityValidationLoading &&
              amlState
            "
          >
            <h3 class="text-base font-semibold text-gray-900">Search Result</h3>
            <table>
              <thead>
                <tr>
                  <th>Code</th>
                  <th>Description</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="row in amlState.detail">
                  <td>{{ row.code }}</td>
                  <td>{{ row.description }}</td>
                </tr>
              </tbody>
            </table>
          </box-section>
        </box>
        <box>
          <box-section
            class="flex items-center justify-between"
            divider="bottom"
          >
            <h3 class="text-base font-semibold text-gray-900">
              PEP & Sanction Monitoring
            </h3>
          </box-section>
          <box-section>
            <Alert
              v-if="monitoringListStatus === 1"
              class="my-3"
              type="success"
              message="Monitoring is active"
            />
            <Alert
              v-if="monitoringListStatus === 2"
              class="my-3"
              type="warning"
              message="Monitoring is not setup"
            />
            <Alert
              v-if="monitoringListStatus === 3"
              class="my-3"
              type="error"
              message="Monitoring is not active"
            />
            <Alert
              v-if="monitoringListStatus === 4"
              class="my-3"
              type="error"
              message="Error loading monitoring"
            />
            <div
              v-if="monitoringDetail !== null"
              data-testid="monitoring-detail"
            >
              <ul class="list-inside list-none">
                <li>Ref: {{ monitoringDetail?.id }}</li>
                <li>Status: {{ monitoringDetail?.status }}</li>
                <li>Matches: {{ monitoringDetail?.matches.length }}</li>
              </ul>
              <table class="min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th
                      scope="col"
                      class="w-1/6 px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500"
                    >
                      Match status
                    </th>
                    <th
                      scope="col"
                      class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500"
                    >
                      Image
                    </th>
                    <th
                      scope="col"
                      class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500"
                    >
                      First Name
                    </th>
                    <th
                      scope="col"
                      class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500"
                    >
                      Last Name
                    </th>
                    <th
                      scope="col"
                      class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500"
                    >
                      Category
                    </th>
                    <th
                      scope="col"
                      class="px-3 py-3.5 text-left text-xs font-medium uppercase tracking-wide text-gray-500"
                    >
                      Role
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr
                    v-for="match in monitoringDetail?.matches ?? []"
                    :key="match.id"
                  >
                    <td class="whitespace-nowrap text-sm text-gray-500">
                      <select-field
                        v-model="match.match_status"
                        label=""
                        :id="`match_status[${match.id}]`"
                        :name="`match_status[${match.id}]`"
                        :options="matchStatusList"
                        :can-clear="true"
                        :searchable="false"
                        @on-select="handleMatchStatusSelect(match.id, $event)"
                      />
                    </td>
                    <td
                      class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"
                    >
                      <img
                        v-if="match.image"
                        style="height: auto; width: 100px"
                        :src="match.image"
                        alt="Picture"
                      />
                      <user-circle-icon v-else class="size-20" />
                    </td>
                    <td
                      class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"
                    >
                      {{ match.first_name }}
                    </td>
                    <td
                      class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"
                    >
                      {{ match.last_name }}
                    </td>
                    <td
                      class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"
                    >
                      <ul class="list-disc">
                        <li v-for="cat in match.categories">{{ cat }}</li>
                      </ul>
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-500">
                      <ul class="list-disc">
                        <li v-for="role in match.roles">{{ role.title }}</li>
                      </ul>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p
              v-else-if="monitoringListLoading"
              data-testid="monitoring-loading"
            >
              Loading monitoring details...
            </p>
          </box-section>
        </box>
      </div>
    </container>
  </client-profile-layout>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import {
    CheckCircleIcon,
    ExclamationCircleIcon,
    QuestionMarkCircleIcon,
    UserCircleIcon,
  } from '@heroicons/vue/24/solid';
  import { Alert, Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import { ClientProfileLayout } from '@modules/ui/layouts';
  import Container from '@modules/ui/layouts/container.vue';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import { useClientId } from '@aventur-shared/modules/clients/models';
  import { useAMLCheck } from '@modules/aml/composables/useAMLCheck';
  import { SelectField } from '@aventur-shared/components/form';
  import { useToast } from '@aventur-shared/composables/useToast';

  const route = useRoute();
  const toast = useToast();
  const { loadClientData } = useClientStore();

  const clientId = ref(useClientId(route.params));
  const {
    runIdentityValidationCheck,
    monitoringListLoading,
    identityValidationLoading,
    monitoringDetail,
    identityVerificationDetail,
    monitoringListStatus,
    matchStatusList,
    postMatchStatusUpdate,
  } = useAMLCheck(clientId);

  const {
    getProfileCompleted: profileCompleted,
    getIdentityValidationStatus: amlState,
  } = storeToRefs(useClientStore());

  onMounted(async () => {
    try {
      if (clientId.value) {
        await loadClientData(clientId.value);
      }
    } catch (e) {
      toast.error(e as Error);
    }
  });

  const handleMatchStatusSelect = (match_id, event: Event) => {
    postMatchStatusUpdate(monitoringDetail.value?.id, match_id, event);
  };
</script>
