<template>
  <client-profile-layout theme="light" :breadcrumbs="[{ label: `Edit` }]">
    <container>
      <div class="py-4">
        <client-details-form
          :is-submit-busy="isSubmitBusy"
          :client-id="clientId"
          :title="`Client ${getProfile.firstName} ${getProfile.lastName} details`"
          :form-values="formValues"
          @on-form-submit="handleFormSubmit"
        />
      </div>
    </container>
  </client-profile-layout>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import { formatForForm } from '@aventur-shared/utils/dateTime';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';
  import {
    createMarketingContact,
    updateMarketingContact,
  } from '@aventur-shared/modules/clients/api';
  import useClientStatus from '@aventur-shared/composables/useClientStatus';
  import { ClientProfileLayout } from '@modules/ui/layouts';
  import {
    ClientType,
    ClientTypeEnum,
  } from '@aventur-shared/modules/clients/models';
  import Container from '@modules/ui/layouts/container.vue';
  import ClientDetailsForm, {
    type FormValues,
    toBeClientLink,
  } from '@modules/clients/components/client/client-details-form';
  import { editClientAction } from '@modules/clients/use-cases/edit-client';

  const route = useRoute();
  const toast = useToast();
  const isSubmitBusy = ref(false);

  const { isActiveStatus } = useClientStatus();
  const { loadClientData, loadClientMarketingData } = useClientStore();
  const {
    profile: getProfile,
    getClientId,
    getClientMarketingId,
  } = storeToRefs(useClientStore());

  const clientId = Number(route.params.id) as ClientId;

  const formValues = computed<FormValues>(() => {
    return {
      clientType: getProfile.value.clientType,
      clientOwner: getProfile.value.advisor.id,
      clientSource: getProfile.value.clientSource,
      clientStatus: getProfile.value.clientStatus,
      dateOfBirth: getProfile.value.dateOfBirth
        ? formatForForm(getProfile.value.dateOfBirth)
        : null,
      email: getProfile.value.email,
      noEmailReason: getProfile.value.noEmailReasonId,
      firstName: getProfile.value.firstName,
      lastName: getProfile.value.lastName,
      phoneNumber: getProfile.value.phoneNumber,
      mobileNumber: getProfile.value.mobileNumber,
      title: getProfile.value.title,
      linkedClients: getProfile.value.linkedClients,
      marketingId: getProfile.value.marketingId,
      clientAgreementId: getProfile.value.clientAgreementId,
      privacyNoticeId: getProfile.value.privacyNoticeId,
    };
  });

  const handleFormSubmit = async (formValues: FormValues) => {
    isSubmitBusy.value = true;
    try {
      await editClientAction({
        id: getClientId.value,
        ...(formValues as Required<FormValues>),
        linkedClients: formValues.linkedClients
          ? formValues.linkedClients.filter(toBeClientLink)
          : [],
        clientType: formValues.clientType as ClientType,
        dateOfBirth: formValues.dateOfBirth
          ? new Date(formValues.dateOfBirth)
          : null,
        noEmailReasonId: formValues.noEmailReason
          ? formValues.noEmailReason
          : null,
      });
      toast.success('Client has been updated');

      // create or update marketing contact
      const data = {
        email: formValues.email as string,
        firstName: formValues.firstName as string,
        lastName: formValues.lastName as string,
        phone: formValues.mobileNumber ?? formValues.phoneNumber ?? null,
      };
      if (getClientMarketingId.value) {
        if (
          getProfile.value.clientType == ClientTypeEnum.Individual &&
          isActiveStatus(formValues.clientStatus as number)
        ) {
          try {
            await updateMarketingContact(getClientId.value, {
              id: getClientMarketingId.value,
              ...data,
            });
          } catch (e) {
            //
          }
        }
      } else {
        if (formValues.email) {
          try {
            const { id: marketingId } = await createMarketingContact(
              getClientId.value,
              { ...data },
            );
            getProfile.value.marketingId = Number(marketingId);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (e) {
            //
          }
        }
      }
    } catch (e: unknown) {
      toast.error(e as Error);
    }
    isSubmitBusy.value = false;
  };

  onMounted(async () => {
    try {
      await loadClientData(clientId);
      await loadClientMarketingData(clientId);
    } catch (e) {
      toast.error(e as Error);
    }
  });
</script>
