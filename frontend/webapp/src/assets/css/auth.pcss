@import '@aws-amplify/ui-vue/styles.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

[data-amplify-authenticator] {
  --amplify-components-authenticator-router-box-shadow: 0 0 0 0;
  --amplify-components-authenticator-router-border-width: 1px;
  --amplify-components-authenticator-form-padding: var(--amplify-space-medium)
    var(--amplify-space-xl) var(--amplify-space-xl);
  --amplify-components-fieldcontrol-focus-box-shadow: 0 0 0 2px
    theme('colors.primary.DEFAULT');
  --amplify-components-tabs-item-active-border-color: theme(
    'colors.primary.DEFAULT'
  );
  --amplify-components-tabs-item-color: theme('colors.primary.DEFAULT/.7');
  --amplify-components-tabs-item-active-color: theme('colors.primary.DEFAULT');
  --amplify-components-tabs-item-hover-color: theme('colors.primary.DEFAULT');
  --amplify-components-button-primary-active-border-color: theme(
    'colors.primary.DEFAULT/.9'
  );
  --amplify-components-button-primary-active-background-color: theme(
    'colors.primary.DEFAULT/.9'
  );
  --amplify-components-button-primary-focus-border-color: theme(
    'colors.primary.DEFAULT/.9'
  );
  --amplify-components-button-primary-focus-background-color: theme(
    'colors.primary.DEFAULT/.9'
  );
  --amplify-components-button-primary-hover-background-color: theme(
    'colors.primary.DEFAULT/.9'
  );
  --amplify-components-button-primary-background-color: theme(
    'colors.primary.DEFAULT'
  );
  --amplify-components-button-link-color: theme('colors.primary.DEFAULT');
  --amplify-components-button-border-color: theme('colors.gray.500/.5');
  --amplify-components-button-font-weight: var(--amplify-font-weights-normal);

  .aventur-logo {
    @apply px-14 py-5;
  }

  .amplify-button {
    &:not([role='switch']) {
      border-radius: var(--amplify-components-button-border-radius) !important;
      margin-top: var(--amplify-space-medium);
    }

    &.amplify-alert__dismiss {
      @apply hidden;
      background-color: var(--amplify-colors-red-10);
      border-color: transparent;
      &:hover,
      &:active,
      &:focus {
        background-color: var(--amplify-colors-red-20);
        border-color: var(--amplify-colors-red-40);
      }
      &:focus {
        box-shadow: 0 0 0 1px var(--amplify-colors-red-60);
      }
    }
  }

  .amplify-heading--3 {
    color: theme('colors.primary.DEFAULT');
    font-size: var(--amplify-font-sizes-medium);
    font-weight: var(--amplify-font-weights-bold);
  }

  #signIn-panel > [data-amplify-form] {
     padding-bottom: var(--amplify-space-zero);
  }
  #signUp-panel > [data-amplify-form] {
    .amplify-field.amplify-passwordfield {
      @apply hidden;
    }
  }

  .amplify-field-group {
    .amplify-input {
      @apply border-gray-500/50 !important;

      border-radius: var(--amplify-components-button-border-radius);
      border-start-start-radius: var(
        --amplify-components-button-border-radius
      ) !important;
      border-end-start-radius: var(
        --amplify-components-button-border-radius
      ) !important;

      [type='password'] {
        border-radius: var(--amplify-components-button-border-radius) 0 0
          var(--amplify-components-button-border-radius) !important;
      }
    }

    :not(:last-child) .amplify-input {
      border-end-end-radius: 0;
      border-start-end-radius: 0;
    }
  }
}

[data-amplify-router] {
  @apply rounded-lg border-primary/30 mx-10 bg-primary/5 shadow shadow-primary/10 !important;

  .amplify-tabs__list {
    border-top-right-radius: var(--amplify-radii-medium);
    border-top-left-radius: var(--amplify-radii-medium);
  }
  .amplify-tabs__item:first-child {
    border-top-left-radius: var(--amplify-radii-medium);
  }
  .amplify-tabs__item:last-child {
    border-top-right-radius: var(--amplify-radii-medium);
  }
}

[data-fullwidth='false'][data-variation='link'] {
  @apply m-auto w-fit !important;
}
