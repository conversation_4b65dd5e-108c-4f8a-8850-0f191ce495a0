<template>
  <Authenticator
    :login-mechanisms="['email']"
    :services="services"
    :sign-up-attributes="signUpAttributes"
    :form-fields="formFields"
  >
    <template #header>
      <div class="aventur-logo">
        <img alt="Aventur logo" src="/assets/logo-black.svg" />
      </div>
    </template>
    <template #default="{ user }">
      <AppLayout :user="user" />
    </template>

    <template v-slot:sign-up-fields>
      <AuthenticatorSignUpFormFields />
    </template>

    <template #footer>
      <div
        class="my-4 flex flex-col items-center justify-center space-y-3 text-sm"
      >
        <p
          class="flex flex-col items-center justify-center space-x-0 md:flex-row md:space-x-4"
        >
          <span>Aventur &copy; {{ currentYear }}</span>
          <span class="hidden text-gray-300 md:inline-block">|</span>
          <a
            target="_blank"
            class="text-primary underline hover:no-underline"
            href="https://www.aventur.co.uk"
            >Home</a
          >
          <span class="hidden text-gray-300 md:inline-block">|</span>
          <a
            target="_blank"
            class="text-primary underline hover:no-underline"
            href="https://www.aventur.co.uk/images/Aventur%20Group%20-%20Website%20Privacy%20Policy%20-%202022-01-07.pdf"
            >Privacy Policy</a
          >
        </p>
        <p class="px-4 text-justify text-xs text-gray-400">
          Aventur is a trading name of Aventur Wealth Ltd (FRN 968770) and is
          authorised and regulated by the Financial Conduct Authority. Aventur
          Wealth Ltd Registered Address: No.1 London Bridge, London, SE19BG.
          Registered in England & Wales No. 12632431. Aventur Group Ltd
          Registered Address: No.1 London Bridge, London, SE19BG. Registered in
          England & Wales No. 13099489. Contact the Financial Ombudsman here.
          Some forms of Estate Planning are not regulated and are dealt with by
          a third-party firm separate to Aventur.
        </p>
      </div>
    </template>
  </Authenticator>
  <UserFeedback />
  <ReloadPrompt />
</template>

<script setup lang="ts">
  import { get } from 'lodash';
  import { post } from 'aws-amplify/api';
  import { I18n } from 'aws-amplify/utils';
  import { ref, watch } from 'vue';
  import {
    Authenticator,
    AuthenticatorSignUpFormFields,
  } from '@aws-amplify/ui-vue';
  import {
    SignInInput,
    fetchMFAPreference,
    signIn,
    updateMFAPreference,
  } from '@aws-amplify/auth';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { isAdvisor } from '@aventur-shared/utils/user';
  import {
    type SignupRequestData,
    formFields,
    signUpAttributes,
  } from '@aventur-shared/constants/auth';

  import UserFeedback from '@/components/UserFeedback.vue';
  import ReloadPrompt from '@modules/ui/reload-prompt/ReloadPrompt.vue';
  import AppLayout from './layouts/AppLayout.vue';
  //

  const toast = useToast();

  I18n.putVocabulariesForLanguage('en', {
    'Sign In': 'Login',
    'Sign in': 'Login',
    'Create Account': 'Register',
    'Forgot your password?': 'Reset Password',
    'Back to Sign In': 'Back to Login',
  });

  const currentYear = new Date().getFullYear();
  const authFlowType =
    import.meta.env.VITE_AWS_COGNITO_AUTHENTICATION_FLOW_TYPE ?? undefined;

  const disableSubmitButton = () => {
    const button = document.querySelector(
      '[data-amplify-button]',
    ) as HTMLButtonElement;

    button.setAttribute('disabled', 'true');
    button.classList.add('amplify-button--disabled');
    button.innerText = 'Please, wait';
  };

  const enableSubmitButton = () => {
    const button = document.querySelector(
      '[data-amplify-button]',
    ) as HTMLButtonElement;

    button.setAttribute('disabled', 'false');
    button.classList.remove('amplify-button--disabled');
    button.innerText = 'Register';
  };

  const signUpInProgress = ref(false);
  watch(signUpInProgress, (value) => {
    value && disableSubmitButton();
    !value && enableSubmitButton();
  });

  const services = {
    async handleSignIn(input: SignInInput) {
      const { nextStep } = await signIn({
        ...input,
        options: {
          authFlowType,
        },
      });

      if (nextStep.signInStep === 'DONE') {
        // Enable EMAIL MFA (if not active yet) for advisers only,
        // so that it's not used for client login in mobile app
        if (await isAdvisor()) {
          const { enabled } = await fetchMFAPreference();
          if (!enabled?.includes('EMAIL')) {
            await updateMFAPreference({ email: 'ENABLED' });
          }
        }
      }

      return { nextStep };
    },
    async handleSignUp(formData: SignupRequestData) {
      signUpInProgress.value = true;
      try {
        const {
          email,
          given_name: first_name,
          family_name: last_name,
        } = get(formData, 'options.userAttributes');

        const signUp = post({
          apiName: 'auth',
          path: '/auth/signup',
          options: {
            body: {
              data: { email, first_name, last_name },
            },
          },
        });

        await signUp.response;
        toast.success('Please, check your email to confirm your account.');
        return { nextStep: { signUpStep: 'DONE' } };
      } catch (e) {
        toast.error(e as Error);
        return { nextStep: 'signUp' };
      } finally {
        signUpInProgress.value = false;
      }
    },
  };
</script>

<style lang="postcss">
  @import './assets/css/auth.pcss';
</style>
