from functools import lru_cache

from pydantic import computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict

from api import APP_ENV, ROOT_PATH


class TestsSettings(BaseSettings):
    db_username: str = "test"
    db_password: str = "test123"
    db_database_name: str = "test_db"
    db_database_template_name: str = "test_db_template"
    db_host: str = "127.0.0.1"
    db_port: str = "5444"
    db_sync_protocol: str = "postgresql"
    db_async_protocol: str = "postgresql+asyncpg"

    redis_host: str = "redis"
    redis_port: int = 6379

    internal_api_base_url: str = "http://localhost:8082"

    aws_cognito_endpoint_url: str = "http://cognito:9229"

    # Data-processing API
    data_processing_base_url: str = "https://example.com/api"

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    def get_connection_url(self, database_name: str | None = None) -> str:
        database_name = database_name or self.db_database_name
        return (
            f"{self.db_username}:{self.db_password}@{self.db_host}:{self.db_port}/{database_name}"
        )

    @property
    def sync_url(self) -> str:
        return f"{self.db_sync_protocol}://{self.get_connection_url()}"

    @property
    def async_url(self) -> str:
        return f"{self.db_async_protocol}://{self.get_connection_url()}"

    @computed_field
    @property
    def db_url(self) -> str:
        return f"{self.db_async_protocol}://{self.get_connection_url()}"

    @property
    def postgres_url(self) -> str:
        return f"{self.db_sync_protocol}://{self.get_connection_url(database_name='postgres')}"

    def template_url(self, template_name: str) -> str:
        return f"{self.db_async_protocol}://{self.get_connection_url(database_name=template_name)}"


@lru_cache()
def get_tests_settings():
    if APP_ENV:
        env_file = (ROOT_PATH / ".env").with_suffix(f".{APP_ENV}")
        if env_file.exists():
            return TestsSettings(_env_file=env_file)

    return TestsSettings()
