from datetime import date
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

import boto3
import pytest
from httpx import AS<PERSON><PERSON><PERSON>sport, AsyncClient

from api.enums import User<PERSON>ole, UserStatus, enums
from api.security.token_validation import TokenData
from app.administrators.services.create import AdministratorCreateService
from app.clients.services.client_access import ClientAccessService
from app.shared_kernel.dtos.administrators import CreateAdministratorDTO
from app.shared_kernel.dtos.client_dtos import ClientDetailDTO
from app.users.services.user_service import create_client
from common.config.settings import get_settings
from database.models import ClientModel
from tests.acceptance.steps import Steps
from tests.acceptance.steps.steps_utils import StepsUtils
from tests.unit.gateway_accessors.zignsec.conftest import (  # noqa
    get_monitoring_detail_response,
    post_monitoring_membercheck_response,
    post_validate_identity_high_response,
    post_validate_identity_low_response,
)


@pytest.fixture
def active_campaign_contact():
    yield dict(
        id="1",
        email="<EMAIL>",
        firstName="John",
        lastName="Doe",
        phone="123456789",
    )


@pytest.fixture
def active_campaign_contact_list():
    yield dict(
        id="1",
        status=3,
        sourceid=1,
        list="1",
    )


@pytest.fixture()
def active_campaign_subscription():
    yield dict(
        id="1",
        email="<EMAIL>",
        firstName="John",
        lastName="Doe",
        phone="123456789",
    )


@pytest.fixture(scope="session")
def localstack_sqs_client():
    settings = get_settings()
    sqs_client = boto3.client(
        "sqs",
        endpoint_url=settings.localstack_endpoint_url,
        region_name=settings.localstack_aws_region,
        aws_access_key_id=settings.aws_access_key_id,
        aws_secret_access_key=settings.aws_secret_access_key,
    )
    yield sqs_client


@pytest.fixture()
def clean_up_cognito_users(cognito_proxy):
    yield
    users = cognito_proxy.list_users()
    for user in users:
        cognito_proxy.delete_cognito_user(email=user)


@pytest.fixture
def user() -> dict:
    return dict(
        email="<EMAIL>",
        roles=[UserRole.Client],
    )


@pytest.fixture
async def permissions_patch_access(uow, cognito_proxy, user, clean_up_cognito_users):
    async with uow:
        create_service = AdministratorCreateService(cognito_proxy, uow)
        administrator = await create_service.create(
            dto=CreateAdministratorDTO(
                email="<EMAIL>",
                first_name="",
                last_name="",
                reached_competent_adviser_date=date.today(),
                reached_competent_adviser_status=True,
                roles=[UserRole.SuperAdmin],
                status=UserStatus.Active,
            )
        )
        await uow.owners.get(administrator.id)
        await create_client(
            uow,
            str(uuid4()),
            user["email"],
            client_status_id=enums.ClientStatus.ActiveTransactional,
        )
        await uow.commit()
        client = await uow.clients.get_by_email(user["email"])
        uow.session.expunge(client)

    client_access_service = ClientAccessService(
        uow=uow,
        cognito=cognito_proxy,
        logger=MagicMock(),
        mailer=MagicMock(),
        sync_cognito_command=AsyncMock(),
    )
    await client_access_service(
        client_data=ClientDetailDTO(
            id=client.id,
            email=client.email,
            client_status_id=client.client_status_id,
            type="client",
            addresses=[],
            links=[],
        ),
        status=True,
    )

    async with uow:
        client = await uow.clients.get_by_email(user["email"])
        cognito_id = client.cognito_id

    import api.security.token_validation as token_validation

    old_method = token_validation.ValidateToken.validate_token

    try:
        token_validation.ValidateToken.validate_token = lambda a, b: TokenData(
            {
                "cognito:groups": user["roles"],
                "email": user["email"],
                "sub": cognito_id,
            }
        )
        yield cognito_id
    finally:
        token_validation.ValidateToken.validate_token = old_method


@pytest.fixture()
async def client_access(request, permissions_patch_access):
    from common.config.settings import get_settings
    from tests.config import get_tests_settings

    # TODO make dynamic or something
    old_db_url = get_settings().db_url
    get_settings().db_url = get_tests_settings().async_url

    from api.main import app

    try:
        async with AsyncClient(
            base_url="http://test", follow_redirects=True, transport=ASGITransport(app=app)
        ) as client:
            client.cognito_id = permissions_patch_access
            yield client
    finally:
        get_settings().db_url = old_db_url


@pytest.fixture()
def steps(client_access: AsyncClient) -> Steps:
    yield Steps(client_access)


@pytest.fixture()
def steps_utils(steps) -> StepsUtils:
    yield StepsUtils(steps=steps)


@pytest.fixture()
async def client_actor(uow, user) -> ClientModel:
    async with uow:
        actor = await uow.clients.get_by_email(user["email"])
        uow.session.expunge(actor)
    return actor
