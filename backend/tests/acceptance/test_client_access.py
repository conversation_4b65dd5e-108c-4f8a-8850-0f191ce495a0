from unittest.mock import Mock, patch

import pytest

from api.enums import ClientStatus
from tests.acceptance.steps import UnexpectedResponseError


@pytest.fixture(scope="session", autouse=True)
def override_container():
    from api.main import container  # type: ignore

    mock_timestream = Mock()
    mock_timestream.execute = Mock(return_value=[])
    with container.timestream_database.query_session.override(mock_timestream):
        yield


async def test_initial_client_status(steps):
    # Given
    owner = await steps.add_owner()
    client_id = await steps.add_client(owner_id=owner["id"])

    # Then
    client_details = await steps.get_client(client_id=client_id)
    assert not client_details["access_enabled"]


async def test_enable_client_access(steps):
    # Given
    owner = await steps.add_owner()
    client_id = await steps.add_client(owner_id=owner["id"])

    # When
    with (
        patch("app.clients.services.client_access.raise_for_invalid_email"),
        patch(
            "app.clients.services.client_access.ClientAccessService._enable_client_access"
        ) as mock_enable_client,
    ):
        await steps.set_client_access(client_id=client_id, status=True)

    # Then
    mock_enable_client.assert_called_once()


async def test_disable_client_access(steps):
    # Given
    owner = await steps.add_owner()
    client_id = await steps.add_client(owner_id=owner["id"])
    with (
        patch("app.clients.services.client_access.raise_for_invalid_email"),
        patch(
            "app.clients.services.client_access.ClientAccessService._disable_client_access"
        ) as mock_disable_client,
    ):
        await steps.set_client_access(client_id=client_id, status=False)

    # Then
    mock_disable_client.assert_called_once()


async def test_client_access_for_missing_email(steps):
    # Given
    owner = await steps.add_owner()
    client_id = await steps.add_client(owner_id=owner["id"], email=None, no_email_reason_id=1)  # type: ignore

    # Then
    with pytest.raises(UnexpectedResponseError) as e:
        await steps.set_client_access(client_id=client_id, status=True)
        assert e.value.response.json() == dict(
            detail=f"Invalid email address for client ID={client_id}."
        )


async def test_client_access_for_invalid_email(steps):
    # Given
    owner = await steps.add_owner()
    client_id = await steps.add_client(owner_id=owner["id"], email="<EMAIL>")

    # Then
    with pytest.raises(UnexpectedResponseError) as e:
        await steps.set_client_access(client_id=client_id, status=True)
    assert e.value.response.json() == dict(
        detail=f"Invalid email address for client ID={client_id}."
    )


@pytest.mark.parametrize(
    "client_status",
    [
        ClientStatus.LeadLive,
        ClientStatus.LeadDormant,
        ClientStatus.Inactive,
        ClientStatus.InactiveLeaving,
        ClientStatus.InactiveDeceased,
        ClientStatus.InactiveLostLead,
    ],
)
async def test_client_access_for_inactive_status(steps, client_status):
    # Given
    owner = await steps.add_owner()
    client_id = await steps.add_client(owner_id=owner["id"], client_status=client_status)

    # Then
    with pytest.raises(UnexpectedResponseError) as e:
        await steps.set_client_access(client_id=client_id, status=True)
    assert e.value.response.json() == dict(detail=f"Client ID={client_id} is not active.")
