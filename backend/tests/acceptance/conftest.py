import hashlib
import hmac
from dataclasses import asdict
from typing import Any
from unittest.mock import AsyncMock, Mock

import boto3
import pytest
from httpx import ASGITransport, AsyncClient

from api.enums import UserRole, enums
from api.security.token_validation import TokenData
from app.forecaster.domain.enums import ContributionType, WithdrawalType
from app.forecaster.domain.models.planner_models import Cashflow
from common.config.settings import get_settings
from common.utils import jsonify
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork
from tests.acceptance.steps import Steps
from tests.acceptance.steps.steps_utils import StepsUtils
from tests.shared import TestData
from tests.unit.gateway_accessors.zignsec.conftest import (  # noqa
    get_monitoring_detail_response,
    post_monitoring_membercheck_response,
    post_validate_identity_high_response,
    post_validate_identity_low_response,
)


@pytest.fixture()
def active_campaign_contact():
    yield dict(
        id="1",
        email="<EMAIL>",
        firstName="John",
        lastName="Doe",
        phone="123456789",
    )


@pytest.fixture()
def active_campaign_contact_list():
    yield dict(
        id="1",
        status=3,
        sourceid=1,
        list="1",
    )


@pytest.fixture()
def active_campaign_subscription():
    yield dict(
        id="1",
        email="<EMAIL>",
        firstName="John",
        lastName="Doe",
        phone="123456789",
    )


@pytest.fixture()
def localstack_sqs_client():
    settings = get_settings()
    sqs_client = boto3.client(
        "sqs",
        endpoint_url=settings.localstack_endpoint_url,
        region_name=settings.localstack_aws_region,
        aws_access_key_id=settings.aws_access_key_id,
        aws_secret_access_key=settings.aws_secret_access_key,
    )
    yield sqs_client


@pytest.fixture()
def clean_up_cognito_users(cognito_proxy):
    users = cognito_proxy.list_users()
    for user in users:
        cognito_proxy.delete_cognito_user(email=user)


@pytest.fixture(scope="session")
def administrator_email(owner) -> str:
    return owner["email"]


@pytest.fixture(scope="session")
def mock_cognito_proxy():
    mock = Mock()
    mock.create_cognito_user.return_value = "bazinga cognito id meow"
    mock.disable_cognito_user = False
    return mock


@pytest.fixture(scope="session", autouse=True)
def override_container():
    from api.main import container  # type: ignore

    mock_timestream = Mock()
    mock_timestream.execute = Mock(return_value=[])
    mock_cognito = Mock()
    mock_cognito.get_cognito_user = Mock(return_value={"Enabled": False})
    mock_bedrock = AsyncMock()
    mock_bedrock.get_model = AsyncMock(return_value="test")
    mock_get_messages = AsyncMock()
    mock_get_messages.get_messages = AsyncMock(return_value=[])
    mock_new_conversation = AsyncMock()
    mock_new_conversation.new_conversation = AsyncMock(return_value=[])

    with (
        container.timestream_database.query_session.override(mock_timestream),
        container.gateway.cognito.override(mock_cognito),
        container.gateway.bedrock_gateway.override(mock_bedrock),
        container.chatbot_services.get_messages.override(mock_get_messages),
        container.chatbot_services.new_conversation.override(mock_new_conversation),
    ):
        yield


@pytest.fixture(autouse=True)
async def permissions_patch(
    request,
    uow,
    administrator_email: str,
):
    roles_marker = request.node.get_closest_marker("user_roles")
    if roles_marker is None:
        user_roles = [UserRole.SuperAdmin]
    else:
        user_roles = roles_marker.args[0]

    import api.security.token_validation as token_validation

    old_method = token_validation.ValidateToken.validate_token

    try:
        token_validation.ValidateToken.validate_token = lambda a, b: TokenData(
            {
                "cognito:groups": user_roles,
                "email": administrator_email,
                "sub": "bazinga cognito id meow",
            }
        )
        yield "bazinga cognito id meow"
    finally:
        token_validation.ValidateToken.validate_token = old_method


@pytest.fixture(autouse=True)
async def api_client(request, permissions_patch):
    from common.config.settings import get_settings
    from tests.config import get_tests_settings

    # TODO make dynamic or something
    old_db_url = get_settings().db_url
    get_settings().db_url = get_tests_settings().async_url

    from api.main import app

    try:
        async with AsyncClient(
            base_url="http://test", follow_redirects=True, transport=ASGITransport(app=app)
        ) as client:
            client.cognito_id = permissions_patch
            yield client
    finally:
        get_settings().db_url = old_db_url


@pytest.fixture(autouse=True)
def steps(api_client: AsyncClient) -> Steps:
    yield Steps(api_client)


@pytest.fixture(autouse=True)
def steps_utils(steps) -> StepsUtils:
    yield StepsUtils(steps=steps)


@pytest.fixture()
async def owner_2(steps, clean_up_cognito_users) -> dict:
    yield await steps.add_owner()


@pytest.fixture()
async def client_1_account(steps_utils, client_1) -> dict:
    new_holding = await steps_utils.add_holding_to_client(
        client_id=client_1["id"], holding_type=enums.ProductTypeType.Account
    )
    yield new_holding


@pytest.fixture()
async def client_2(steps_utils, owner) -> dict:
    yield await steps_utils.add_client(owner_id=owner["id"], first_name="James", last_name="Błąd")


@pytest.fixture()
async def client_1_account_with_fee_split_template(
    steps_utils, owner, client_1, owner_1_fee_split_template
) -> dict:
    new_holding = await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.Account,
        fee_split_template_id=owner_1_fee_split_template["id"],
        owner_id=owner["id"],
    )
    yield new_holding


@pytest.fixture()
async def client_1_property(steps_utils, client_1) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"], holding_type=enums.ProductTypeType.Property
    )


@pytest.fixture()
async def client_1_mortgage(steps_utils, owner, client_1, client_1_property) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.Mortgage,
        secured_against_address=client_1_property["id"],
        mortgage_adviser_id=owner["id"],
        adviser_id=owner["id"],
    )


@pytest.fixture()
async def client_1_credit_card(steps_utils, data, client_1) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.CreditCard,
        adviser_id=data["id"],
    )


@pytest.fixture()
async def client_1_personal_loan(steps_utils, owner, client_1) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.PersonalLoan,
        adviser_id=owner["id"],
    )


@pytest.fixture()
async def client_1_other_debt(steps_utils, owner, client_1) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.OtherDebt,
        adviser_id=owner["id"],
    )


@pytest.fixture()
async def client_1_company_shares(steps_utils, client_1) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.CompanyShares,
    )


@pytest.fixture()
async def client_1_crypto_currency(steps_utils, client_1) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.CryptoCurrency,
    )


@pytest.fixture()
async def client_1_other_asset(steps_utils, client_1) -> dict:
    yield await steps_utils.add_holding_to_client(
        client_id=client_1["id"],
        holding_type=enums.ProductTypeType.OtherAsset,
    )


@pytest.fixture()
async def owner_1_fee_split_template(steps, owner):
    template_name = "COOL JAMES TEMPLATE"
    role_id = enums.FeeRoleType.Adviser
    type_id = enums.FeeSplitType.Standard_Fee
    is_active = True
    is_payable = True
    template_id = await steps.add_fee_split_template(
        administrator_id=owner["id"],
        is_active=is_active,
        template_name=template_name,
        lines=[
            {
                "administrator_id": owner["id"],
                "role": role_id,
                "type": type_id,
                "split_initial": "0.6",
                "split_ongoing": "0.6",
                "is_payable": is_payable,
            },
            {
                "administrator_id": owner["id"],
                "role": role_id,
                "type": type_id,
                "split_initial": "0.4",
                "split_ongoing": "0.4",
                "is_payable": is_payable,
            },
        ],
    )
    template = await steps.get_fee_split_template(fee_split_template_id=template_id)
    yield template


@pytest.fixture()
async def client_1_income(steps, client_1) -> dict:
    income_name = "client 1 income 1"
    income_type = TestData.INCOME_EXPENDITURE_TYPE.BONUS
    frequency = enums.Frequency.Weekly
    amount = 13.37

    [update_response] = await steps.update_client_income(
        client_id=client_1["id"],
        income=[
            {
                "id": None,
                "amount": amount,
                "frequency": frequency,
                "description": income_name,
                "type_id": income_type,
            }
        ],
    )
    yield update_response


@pytest.fixture()
async def client_1_expenditure(steps, client_1) -> dict:
    income_name = "client 1 expenditure 1"
    income_type = TestData.INCOME_EXPENDITURE_TYPE.RENT
    frequency = enums.Frequency.Monthly
    amount = 600

    [update_response] = await steps.update_client_expenditure(
        client_id=client_1["id"],
        expenditure=[
            {
                "id": None,
                "amount": amount,
                "frequency": frequency,
                "description": income_name,
                "type_id": income_type,
            }
        ],
    )
    yield update_response


@pytest.fixture()
def forecaster_plan_request(steps):
    yield dict(
        cashflows=[
            *map(
                asdict,
                [
                    Cashflow(
                        ContributionType.MonthlyDeposit,
                        "2023-12-31",
                        "2033-12-31",
                        300,
                    ),
                    Cashflow(
                        ContributionType.QuarterlyDeposit,
                        "2023-12-31",
                        "2024-12-31",
                        2500,
                    ),
                    Cashflow(
                        WithdrawalType.LumpSumWithdrawal,
                        "2023-12-31",
                        None,
                        25000,
                    ),
                ],
            )
        ],
        recommended_risk=5,
        retirement_age=70,
        selected_risk=4,
        comments=None,
    )


@pytest.fixture(scope="session")
def uow(postgres_database) -> SQLAlchemyUnitOfWork:
    return SQLAlchemyUnitOfWork(postgres_database)


@pytest.fixture
def zignsec_webhook_relay_state() -> str:
    return "737"


@pytest.fixture
def zignsec_webhook_status() -> str:
    return "NoChanges"


@pytest.fixture
def zignsec_webhook_notification(
    zignsec_webhook_relay_state: str, zignsec_webhook_status: str
) -> dict[str, Any]:
    return {
        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "event_time": "2025-07-08T14:08:13.262Z",
        "version": 0,
        "session_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "session_created": "2025-07-08T14:08:13.262Z",
        "gdpr_user_id": "111",
        "relay_state": "string",
        "info": {"sessionId": "3fa85f64-5717-4562-b3fc-2c963f66afa6", "sessionStatus": "string"},
        "integration_id": "string",
        "workflow_key": "string",
        "workflow_session_id": "string",
        "provider_action_id": "string",
        "data": {
            "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "status": "string",
            "monitoringId": 0,
            "created": "2025-07-08T14:08:13.262Z",
            "lastUpdated": "2025-07-08T14:08:13.262Z",
            "nextInvoiceDate": "2025-07-08",
            "errors": [
                {"code": "string", "description": "string", "details": "string", "statusCode": 0}
            ],
            "result": {
                "matches": [
                    {
                        "matchId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                        "matchRate": 0,
                        "matchedFields": ["string"],
                        "match": {
                            "dataSource": "C6",
                            "lastReviewedDate": "2025-07-08",
                            "categoryCodes": ["PEP", "SIP"],
                            "categories": [
                                "Profile of Interest (POI)",
                                "Special Interest Person (SIP) - Sanctions Lists",
                                "Special Interest Person (SIP) - Financial Crime",
                                "Special Interest Person (SIP) - Bribery and Corruption",
                            ],
                            "nationality": "string",
                            "nationalities": ["string"],
                            "deceasedDate": "2025-07-08",
                            "deceasedStatus": "string",
                            "primaryLocation": {
                                "type": "string",
                                "countryCode": "string",
                                "country": "string",
                                "postalCod": "string",
                                "city": "string",
                                "street": "string",
                                "streetNumber": "string",
                                "apartment": "string",
                                "addressLine1": "string",
                                "addressLine2": "string",
                                "province": "string",
                                "municipality": "string",
                            },
                            "locations": [
                                {
                                    "type": "string",
                                    "countryCode": "string",
                                    "country": "string",
                                    "postalCod": "string",
                                    "city": "string",
                                    "street": "string",
                                    "streetNumber": "string",
                                    "apartment": "string",
                                    "addressLine1": "string",
                                    "addressLine2": "string",
                                    "province": "string",
                                    "municipality": "string",
                                }
                            ],
                            "placeOfBirth": {
                                "type": "string",
                                "countryCode": "string",
                                "country": "string",
                                "postalCod": "string",
                                "city": "string",
                                "street": "string",
                                "streetNumber": "string",
                                "apartment": "string",
                                "addressLine1": "string",
                                "addressLine2": "string",
                                "province": "string",
                                "municipality": "string",
                            },
                            "image": "string",
                            "images": ["string"],
                            "otherNames": [
                                {
                                    "firstName": "string",
                                    "lastName": "string",
                                    "middleName": "string",
                                    "fullName": "string",
                                    "type": "string",
                                }
                            ],
                            "roles": [
                                {
                                    "title": "string",
                                    "segment": "string",
                                    "type": "string",
                                    "status": "string",
                                    "country": "string",
                                    "from": "string",
                                    "to": "string",
                                }
                            ],
                            "importantDates": [{"type": "string", "date": "string"}],
                            "linkedPersons": [
                                {
                                    "firstName": "string",
                                    "lastName": "string",
                                    "middleName": "string",
                                    "fullName": "string",
                                    "recordId": 0,
                                    "category": "string",
                                    "description": "string",
                                }
                            ],
                            "sources": [
                                {"url": "string", "type": "string", "categories": ["string"]}
                            ],
                            "officialLists": [
                                {
                                    "category": "string",
                                    "description": "string",
                                    "isActive": True,
                                    "origin": "string",
                                    "types": "string",
                                    "measures": "string",
                                    "keyword": "string",
                                }
                            ],
                            "linkedCompanies": [
                                {
                                    "recordId": 0,
                                    "name": "string",
                                    "category": "string",
                                    "description": "string",
                                }
                            ],
                            "profileOfInterests": [
                                {
                                    "category": "string",
                                    "positions": [
                                        {
                                            "title": "string",
                                            "segment": "string",
                                            "type": "string",
                                            "status": "string",
                                            "country": "string",
                                            "from": "string",
                                            "to": "string",
                                        }
                                    ],
                                }
                            ],
                            "taxHavenCountryResults": [
                                {
                                    "isPrimaryLocation": True,
                                    "countryCode": "string",
                                    "comment": "string",
                                    "url": "string",
                                }
                            ],
                            "texts": [{"type": "string", "text": "string", "props": {}}],
                            "providerData": {},
                            "countryCode": "string",
                            "firstName": "string",
                            "middleName": "string",
                            "lastName": "string",
                            "fullName": "string",
                            "gender": "string",
                            "dateOfBirth": "2025-07-08",
                            "yearOfBirth": 0,
                        },
                        "old": {
                            "dataSource": "C6",
                            "lastReviewedDate": "2025-07-08",
                            "categoryCodes": ["PEP", "SIP"],
                            "categories": [
                                "Profile of Interest (POI)",
                                "Special Interest Person (SIP) - Sanctions Lists",
                                "Special Interest Person (SIP) - Financial Crime",
                                "Special Interest Person (SIP) - Bribery and Corruption",
                            ],
                            "nationality": "string",
                            "nationalities": ["string"],
                            "deceasedDate": "2025-07-08",
                            "deceasedStatus": "string",
                            "primaryLocation": {
                                "type": "string",
                                "countryCode": "string",
                                "country": "string",
                                "postalCod": "string",
                                "city": "string",
                                "street": "string",
                                "streetNumber": "string",
                                "apartment": "string",
                                "addressLine1": "string",
                                "addressLine2": "string",
                                "province": "string",
                                "municipality": "string",
                            },
                            "locations": [
                                {
                                    "type": "string",
                                    "countryCode": "string",
                                    "country": "string",
                                    "postalCod": "string",
                                    "city": "string",
                                    "street": "string",
                                    "streetNumber": "string",
                                    "apartment": "string",
                                    "addressLine1": "string",
                                    "addressLine2": "string",
                                    "province": "string",
                                    "municipality": "string",
                                }
                            ],
                            "placeOfBirth": {
                                "type": "string",
                                "countryCode": "string",
                                "country": "string",
                                "postalCod": "string",
                                "city": "string",
                                "street": "string",
                                "streetNumber": "string",
                                "apartment": "string",
                                "addressLine1": "string",
                                "addressLine2": "string",
                                "province": "string",
                                "municipality": "string",
                            },
                            "image": "string",
                            "images": ["string"],
                            "otherNames": [
                                {
                                    "firstName": "string",
                                    "lastName": "string",
                                    "middleName": "string",
                                    "fullName": "string",
                                    "type": "string",
                                }
                            ],
                            "roles": [
                                {
                                    "title": "string",
                                    "segment": "string",
                                    "type": "string",
                                    "status": "string",
                                    "country": "string",
                                    "from": "string",
                                    "to": "string",
                                }
                            ],
                            "importantDates": [{"type": "string", "date": "string"}],
                            "linkedPersons": [
                                {
                                    "firstName": "string",
                                    "lastName": "string",
                                    "middleName": "string",
                                    "fullName": "string",
                                    "recordId": 0,
                                    "category": "string",
                                    "description": "string",
                                }
                            ],
                            "sources": [
                                {"url": "string", "type": "string", "categories": ["string"]}
                            ],
                            "officialLists": [
                                {
                                    "category": "string",
                                    "description": "string",
                                    "isActive": True,
                                    "origin": "string",
                                    "types": "string",
                                    "measures": "string",
                                    "keyword": "string",
                                }
                            ],
                            "linkedCompanies": [
                                {
                                    "recordId": 0,
                                    "name": "string",
                                    "category": "string",
                                    "description": "string",
                                }
                            ],
                            "profileOfInterests": [
                                {
                                    "category": "string",
                                    "positions": [
                                        {
                                            "title": "string",
                                            "segment": "string",
                                            "type": "string",
                                            "status": "string",
                                            "country": "string",
                                            "from": "string",
                                            "to": "string",
                                        }
                                    ],
                                }
                            ],
                            "taxHavenCountryResults": [
                                {
                                    "isPrimaryLocation": True,
                                    "countryCode": "string",
                                    "comment": "string",
                                    "url": "string",
                                }
                            ],
                            "texts": [{"type": "string", "text": "string", "props": {}}],
                            "providerData": {},
                            "countryCode": "string",
                            "firstName": "string",
                            "middleName": "string",
                            "lastName": "string",
                            "fullName": "string",
                            "gender": "string",
                            "dateOfBirth": "2025-07-08",
                            "yearOfBirth": 0,
                        },
                        "status": "string",
                        "countryCode": "string",
                        "firstName": "string",
                        "middleName": "string",
                        "lastName": "string",
                        "fullName": "string",
                        "age": 0,
                        "gender": "string",
                        "dateOfBirth": "2025-07-08",
                        "yearOfBirth": 0,
                    }
                ],
                "webSearchResults": [{"title": "string", "url": "string", "previewHtml": "string"}],
                "emailBreachCheckResults": [
                    {
                        "name": "string",
                        "domain": "string",
                        "date": "2025-07-08",
                        "description": "string",
                        "companyLogo": "string",
                        "dataClasses": ["string"],
                    }
                ],
                "jurisdictionRiskResults": [
                    {
                        "jurisdiction": "string",
                        "effectivenessScore": 0,
                        "effectivenessLevel": 0,
                        "complianceScore": 0,
                        "complianceLevel": 0,
                        "comments": "string",
                        "fatfCompliance": "string",
                        "fatfComplianceNotes": "string",
                        "fatfEffectiveness": "string",
                        "fatfEffectivenessNotes": "string",
                        "fatfEffectivenessSubtitles": "string",
                        "countryCode": "string",
                    }
                ],
                "dueDiligence": {
                    "decisions": [
                        {
                            "risk": 0,
                            "date": "2025-07-08T14:08:13.263Z",
                            "comment": "string",
                            "author": "string",
                            "decision": 0,
                        }
                    ],
                    "matchesDecisionHistory": [
                        {
                            "matchId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                            "history": [
                                {
                                    "risk": 0,
                                    "date": "2025-07-08T14:08:13.263Z",
                                    "comment": "string",
                                    "author": "string",
                                    "decision": 0,
                                }
                            ],
                        }
                    ],
                },
            },
        },
    }


@pytest.fixture
def json_encoded_zignsec_webhook_notification(
    zignsec_webhook_notification: dict[str, Any],
) -> str:
    return jsonify(
        zignsec_webhook_notification,  # pylint: disable=E1101
    )


@pytest.fixture
def zignsec_webhook_timestamped_signature(
    json_encoded_zignsec_webhook_notification: str,
) -> str:
    settings = get_settings()
    return hmac.new(
        f"{settings.zignsec_webhook_secret}{settings.zignsec_gateway_id}".encode("utf-8"),
        json_encoded_zignsec_webhook_notification.encode("utf-8"),
        hashlib.sha256,
    ).hexdigest()


@pytest.fixture
def zignsec_webhook_simple_signature(
    json_encoded_zignsec_webhook_notification: str,
) -> str:
    settings = get_settings()
    return hmac.new(
        settings.zignsec_gateway_id.encode("utf-8"),
        json_encoded_zignsec_webhook_notification.encode("utf-8"),
        hashlib.sha256,
    ).hexdigest()
