from datetime import date
from typing import Any
from unittest.mock import AsyncMock, Mo<PERSON>, patch
from uuid import UUID, uuid4

import pytest
from httpx import Response
from sqlalchemy import select
from starlette.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_204_NO_CONTENT,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_500_INTERNAL_SERVER_ERROR,
    HTTP_502_BAD_GATEWAY,
)

from api.enums import ClientStatus
from app.aml.services.aml_service import AMLCommandService
from app.clients.services.add_client import AddClientService
from common.config.settings import get_settings
from database.models import (
    AMLMonitoringSessionModel,
    AMLSearchResultModel,
    ClientAddressModel,
)
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork
from tests import fakes
from tests.acceptance.steps import Steps
from tests.shared import TestData

settings = get_settings()
settings.ms_teams_url = "https://example.com"

ZIGNSEC_BASE_URL = f"https://{settings.zignsec_env}.zignsec.com/"
ZIGNSEC_GATEWAY_BASE_URL = f"https://{settings.zignsec_gateway_env}.zignsec.com/"


@pytest.fixture
def test_uow(postgres_database) -> SQLAlchemyUnitOfWork:
    return SQLAlchemyUnitOfWork(postgres_database)


@pytest.fixture
def add_client_service(test_uow) -> AddClientService:
    return AddClientService(uow=test_uow, logger=Mock())


async def _create_client(service: AddClientService) -> int:
    client_data = fakes.fake_client_data(client_status=ClientStatus.ActiveOngoing)
    with patch("app.clients.services.add_client.add_onboarding_case"):
        client_id = await service.add_client(**client_data)
    return client_id


async def _create_client_with_address(service: AddClientService) -> int:
    client_id = await _create_client(service)
    async with service.uow:
        address = ClientAddressModel(
            client_id=client_id,
            address_line_one="1 Somewhere",
            city="Somewhere",
            post_code="S12 3AB",
            country_id=TestData.COUNTRIES.GB,
            is_primary=True,
        )
        service.uow.session.add(address)
        await service.uow.commit()
    return client_id


async def _create_aml_search_result(
    client_id: int, found: bool, match_level: str, uow: SQLAlchemyUnitOfWork
) -> None:
    async with uow:
        result = AMLSearchResultModel(
            client_id=client_id,
            search_criteria={
                "FirstName": "John",
                "LastName": "Doe",
                "Address": "1 Somewhere",
                "City": "Somewhere",
                "CountryCode": "GB",
                "PostalCode": "S12 3AB",
                "DateOfBirth": date(1990, 1, 1),
            },
            found=found,
            match_level=match_level,
            full_results={"validation_details": {"Codes": ""}},
        )
        uow.session.add(result)
        await uow.commit()


async def _create_aml_monitoring_session(client_id: int, uow: SQLAlchemyUnitOfWork) -> UUID:
    session_id = uuid4()
    async with uow:
        session = AMLMonitoringSessionModel(client_id=client_id, zignsec_session_id=session_id)
        uow.session.add(session)
        await uow.commit()
    return session_id


@pytest.mark.asyncio
class TestPostValidateIdentity:
    zignsec_identity_validation_url = "v2/ekyc/validateIdentity"
    zignsec_monitoring_url = "api/v5/sessions/watchlist/person/monitoring"

    async def test_returns_404_when_the_client_id_does_not_exist(self, steps: Steps) -> None:
        unknown_client_id = 999
        response = await steps.aml.post_validate_identity(unknown_client_id)
        assert response.status_code == HTTP_404_NOT_FOUND

    async def test_returns_400_when_the_client_has_no_address(
        self, add_client_service: AddClientService, steps: Steps
    ) -> None:
        # Seed a client with no addresses
        client_id = await _create_client(add_client_service)

        response = await steps.aml.post_validate_identity(client_id)
        assert response.status_code == HTTP_400_BAD_REQUEST

    @pytest.mark.respx(base_url=ZIGNSEC_BASE_URL)
    async def test_returns_502_when_there_is_an_error_from_external_api(
        self, add_client_service: AddClientService, steps: Steps, respx_mock
    ) -> None:
        """Tests that an error response returned by ZignSec is converted into a
        bad gateway error in Jarvis.

        This test mocks the HTTP layer to avoid hitting the ZignSec API for real.

        """
        client_id = await _create_client_with_address(add_client_service)

        endpoint = respx_mock.post(self.zignsec_identity_validation_url).mock(
            return_value=Response(HTTP_500_INTERNAL_SERVER_ERROR)
        )
        response = await steps.aml.post_validate_identity(client_id)
        assert response.status_code == HTTP_502_BAD_GATEWAY
        endpoint.calls.assert_called_once()

    @pytest.mark.respx()
    async def test_verification_pass_starts_monitoring_session(
        self,
        test_uow: SQLAlchemyUnitOfWork,
        add_client_service: AddClientService,
        steps: Steps,
        respx_mock,
        post_validate_identity_high_response,
        post_monitoring_membercheck_response,
        time_machine,
    ) -> None:
        """Tests that a successful identity response from ZignSec is saved in the DB,
        and that a monitoring session is initialised for the client.

        This test mocks the HTTP layer to avoid hitting the ZignSec API for real.

        """
        expected_date = "2023-04-17"
        time_machine.move_to(expected_date, tick=False)
        client_id = await _create_client_with_address(add_client_service)
        expected_session_id = UUID(
            "f2cf28c2-9513-4d35-9cbc-c1c86082fd43"
        )  # From `post_monitoring_membercheck_response` fixture

        # Mock the HTTP layer
        identity_endpoint = respx_mock.post(
            f"{ZIGNSEC_BASE_URL}{self.zignsec_identity_validation_url}"
        ).mock(return_value=Response(HTTP_200_OK, json=post_validate_identity_high_response))
        monitoring_endpoint = respx_mock.post(
            f"{ZIGNSEC_GATEWAY_BASE_URL}{self.zignsec_monitoring_url}"
        ).mock(return_value=Response(HTTP_200_OK, json=post_monitoring_membercheck_response))

        response = await steps.aml.post_validate_identity(client_id)
        assert response.status_code == HTTP_201_CREATED
        assert response.json() == True
        identity_endpoint.calls.assert_called_once()
        monitoring_endpoint.calls.assert_called_once()

        # Check that the database has an AML result and session details
        async with test_uow:
            query = await test_uow.session.execute(select(AMLSearchResultModel))
            rows = query.scalars().fetchall()
            assert len(rows) == 1
            assert rows[0].client_id == client_id
            assert rows[0].found is True
            assert rows[0].match_level == "HIGH"

            query = await test_uow.session.execute(select(AMLMonitoringSessionModel))
            rows = query.scalars().fetchall()
            assert len(rows) == 1
            assert rows[0].client_id == client_id
            assert rows[0].zignsec_session_id == expected_session_id

    @pytest.mark.respx(base_url=ZIGNSEC_BASE_URL)
    async def test_verification_fail_does_not_start_monitoring_session(
        self,
        test_uow: SQLAlchemyUnitOfWork,
        add_client_service: AddClientService,
        steps: Steps,
        respx_mock,
        post_validate_identity_low_response,
        time_machine,
    ) -> None:
        """Tests that an identity validation failure does not result in the client
        being enrolled for AML monitoring.

        This test mocks the HTTP layer to avoid hitting the ZignSec API for real.

        """
        expected_date = "2023-04-17"
        time_machine.move_to(expected_date, tick=False)
        client_id = await _create_client_with_address(add_client_service)
        respx_mock.post(self.zignsec_identity_validation_url).mock(
            return_value=Response(HTTP_200_OK, json=post_validate_identity_low_response)
        )

        response = await steps.aml.post_validate_identity(client_id)
        assert response.status_code == HTTP_201_CREATED
        assert response.json() == True

        # Check that the database has an AML result but no session details
        async with test_uow:
            query = await test_uow.session.execute(select(AMLSearchResultModel))
            rows = query.scalars().fetchall()
            assert len(rows) == 1

            query = await test_uow.session.execute(select(AMLMonitoringSessionModel))
            rows = query.scalars().fetchall()
            assert len(rows) == 0


@pytest.mark.asyncio
class TestGetMonitoringDetail:
    @pytest.fixture(autouse=True)
    async def setup_client(self, add_client_service: AddClientService) -> None:
        client_id = await _create_client(add_client_service)
        self.client_id = client_id

    async def test_returns_400_when_the_client_is_not_registered_for_monitoring(
        self,
        steps: Steps,
    ) -> None:
        response = await steps.aml.get_monitoring_detail(self.client_id)
        assert response.status_code == HTTP_400_BAD_REQUEST

    @pytest.mark.respx(base_url=ZIGNSEC_GATEWAY_BASE_URL)
    async def test_returns_200_and_the_monitoring_detail_when_the_client_is_registered(
        self,
        steps: Steps,
        test_uow: SQLAlchemyUnitOfWork,
        respx_mock,
        get_monitoring_detail_response: dict[str, Any],
    ) -> None:
        """Tests that a successful response from ZignSec is returned.

        This test mocks the HTTP layer to avoid hitting the ZignSec API for real.

        """
        session_id = await _create_aml_monitoring_session(self.client_id, test_uow)
        endpoint = respx_mock.get(f"api/v5/sessions/watchlist/person/monitoring/{session_id}").mock(
            return_value=Response(HTTP_200_OK, json=get_monitoring_detail_response)
        )

        response = await steps.aml.get_monitoring_detail(self.client_id)
        assert response.status_code == HTTP_200_OK
        response_json = response.json()
        assert response_json["id"] == "f2cf28c2-9513-4d35-9cbc-c1c86082fd43"
        endpoint.calls.assert_called_once()

    @pytest.mark.respx(base_url=ZIGNSEC_GATEWAY_BASE_URL)
    async def test_returns_404_when_no_session_id(
        self,
        steps: Steps,
        test_uow: SQLAlchemyUnitOfWork,
        respx_mock,
        get_monitoring_detail_response: dict[str, Any],
    ) -> None:
        """Tests that a successful response from ZignSec is returned.

        This test mocks the HTTP layer to avoid hitting the ZignSec API for real.

        """
        session_id = await _create_aml_monitoring_session(self.client_id, test_uow)
        endpoint = respx_mock.get(f"api/v5/sessions/watchlist/person/monitoring/{session_id}").mock(
            side_effect=Response(404)
        )

        response = await steps.aml.get_monitoring_detail(self.client_id)
        assert response.status_code == HTTP_404_NOT_FOUND
        endpoint.calls.assert_called_once()


@pytest.mark.asyncio
class TestGetIdentityVerificationResult:
    @pytest.fixture(autouse=True)
    async def setup_client(self, add_client_service: AddClientService) -> None:
        client_id = await _create_client(add_client_service)
        self.client_id = client_id

    async def test_returns_404_when_the_client_has_no_identity_verification_results(
        self,
        steps: Steps,
    ) -> None:
        response = await steps.aml.get_identity_verification_result(self.client_id)
        assert response.status_code == HTTP_404_NOT_FOUND

    async def test_returns_200_and_the_expected_result(
        self, steps: Steps, test_uow: SQLAlchemyUnitOfWork
    ) -> None:
        # Seed an identity verification result for the client
        await _create_aml_search_result(
            self.client_id, found=True, match_level="HIGH", uow=test_uow
        )

        response = await steps.aml.get_identity_verification_result(self.client_id)
        assert response.status_code == HTTP_200_OK
        response_data = response.json()
        assert response_data["result"] == "Pass"
        assert response_data["date"] == str(date.today())

    async def test_returns_most_recent_result_when_client_has_multiple_results(
        self, steps: Steps, test_uow: SQLAlchemyUnitOfWork
    ) -> None:
        # Seed results - the newest result is a fail
        await _create_aml_search_result(
            self.client_id, found=True, match_level="HIGH", uow=test_uow
        )
        await _create_aml_search_result(
            self.client_id, found=False, match_level="LOW", uow=test_uow
        )

        response = await steps.aml.get_identity_verification_result(self.client_id)
        assert response.json()["result"] == "Fail"


class TestPostZignSecMonitoringUpdate:
    async def test_returns_400_when_webhook_signature_check_fails(
        self, steps: Steps, zignsec_webhook_notification: dict[str, Any]
    ) -> None:
        response = await steps.aml.post_zignsec_monitoring_update(
            body=zignsec_webhook_notification,
            zignsec_signature="Some invalid signature",
        )
        assert response.status_code == HTTP_400_BAD_REQUEST

    @pytest.mark.parametrize(
        "notification_fixture, signature_fixture",
        (
            ("zignsec_webhook_notification", "zignsec_webhook_timestamped_signature"),
            ("zignsec_webhook_notification", "zignsec_webhook_simple_signature"),
        ),
    )
    async def test_returns_204_and_process_update_in_background(
        self,
        add_client_service,
        steps: Steps,
        notification_fixture: str,
        signature_fixture: str,
        zignsec_webhook_relay_state: int,
        zignsec_webhook_status: str,
        request,
    ) -> None:
        update = request.getfixturevalue(notification_fixture)
        # Seed a client with no addresses
        client_id = await _create_client(add_client_service)

        update["gdpr_user_id"] = str(client_id)

        response = await steps.aml.post_zignsec_monitoring_update(
            body=update,
            zignsec_signature=request.getfixturevalue(signature_fixture),
        )
        assert response.status_code == HTTP_204_NO_CONTENT


@pytest.mark.asyncio
class TestUpdateMissingAMLCheck:
    @patch(
        "app.aml.services.aml_service.AMLCommandService.client_identity_check",
        new_callable=AsyncMock,
    )
    async def test_update_aml_where_missing(
        self,
        mock_validate: AsyncMock,
        test_uow: SQLAlchemyUnitOfWork,
        add_client_service: AddClientService,
        remove_seed_data,
    ) -> None:
        # Seed a client
        await _create_client_with_address(add_client_service)
        await _create_client_with_address(add_client_service)

        service = AMLCommandService(uow=test_uow, logger=Mock(), zignsec_accessor=Mock())

        await service.bulk_update_aml_monitoring()

        assert mock_validate.call_count == 2
