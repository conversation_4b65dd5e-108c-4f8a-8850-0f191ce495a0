import pytest
from httpx import ASGITransport, AsyncClient

from api.enums import UserRole
from api.security.token_validation import TokenData
from tests.acceptance.steps import Steps
from tests.acceptance.steps.steps_utils import StepsUtils


@pytest.fixture(scope="session")
def user_id() -> int:
    return 1


@pytest.fixture(scope="session")
def jwt_token() -> str:
    return "jwt_token"


@pytest.fixture
def client_email(client_1) -> str:
    return client_1["email"]


@pytest.fixture
async def client_permissions_patch(
    request,
    uow,
    client_email: str,
):
    user_roles = [UserRole.Client, UserRole.SuperAdmin]

    async with uow:
        client = await uow.users.get_by_email(client_email)
        cognito_id = client.cognito_id

    import api.security.token_validation as token_validation

    old_method = token_validation.ValidateToken.validate_token

    try:
        token_validation.ValidateToken.validate_token = lambda a, b: TokenData(
            {
                "cognito:groups": user_roles,
                "email": client_email,
                "sub": cognito_id,
            }
        )
        yield cognito_id
    finally:
        token_validation.ValidateToken.validate_token = old_method


@pytest.fixture
async def client_api_client(request, client_permissions_patch):
    from common.config.settings import get_settings
    from tests.config import get_tests_settings

    old_db_url = get_settings().db_url
    get_settings().db_url = get_tests_settings().async_url

    from api.main import app

    try:
        async with AsyncClient(
            base_url="http://test", follow_redirects=True, transport=ASGITransport(app=app)
        ) as client:
            client.cognito_id = client_permissions_patch
            yield client
    finally:
        get_settings().db_url = old_db_url


@pytest.fixture
def client_steps(client_api_client: AsyncClient) -> Steps:  # type: ignore
    yield Steps(client_api_client)


@pytest.fixture
def client_steps_utils(client_steps) -> StepsUtils:  # type: ignore
    yield StepsUtils(steps=client_steps)
