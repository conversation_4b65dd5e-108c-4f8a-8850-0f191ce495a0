import json
from datetime import datetime
from typing import Any
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from api.enums import User<PERSON><PERSON>
from app.chatbot.dtos.requests import UserTextDTO
from app.chatbot.interface.conversation_history_interface import (
    ConversationHistoryStore,
    RedisConversationStore,
    S3ConversationStore,
)
from app.chatbot.services.get_messages import GetMessages
from app.chatbot.services.new_conversation import NewConversation
from app.chatbot.services.post_message.post_message import PostMessage
from common.config.settings import get_settings
from gateway_accessors.aws.bedrock.accessor import BedrockGateway
from gateway_accessors.aws.s3.accessor import S3Gateway
from gateway_accessors.redis.accessor import RedisGateway

settings = get_settings()


@pytest.fixture(scope="session")
def s3_gateway():
    """Return an S3 client where the bucket `settings.aws_bucket` exists."""
    s3_gateway = S3Gateway(
        localstack_endpoint_url=settings.localstack_endpoint_url,
        aws_access_key_id=str(settings.aws_access_key_id),
        aws_secret_access_key=str(settings.aws_secret_access_key),
        is_local=True,
        aws_bucket_name=str(settings.aws_bucket),
        aws_region="eu-west-2",
    )

    return s3_gateway


@pytest.fixture(scope="session")
def mock_bedrock_gateway() -> AsyncMock:
    mock = AsyncMock(spec=BedrockGateway)
    mock.get_model.return_value = "test"
    return mock


@pytest.fixture
def redis_store(redis_gateway: RedisGateway) -> RedisConversationStore:
    return RedisConversationStore(redis_gateway=redis_gateway)


@pytest.fixture
def s3_store(s3_gateway: S3Gateway) -> S3ConversationStore:
    return S3ConversationStore(s3_gateway=s3_gateway)


@pytest.fixture
def conversation_history_store(
    redis_store: RedisConversationStore, s3_store: S3ConversationStore
) -> ConversationHistoryStore:
    return ConversationHistoryStore(logger=Mock(), cache=redis_store, storage=s3_store)


@pytest.fixture
def get_messages(conversation_history_store: ConversationHistoryStore) -> GetMessages:
    return GetMessages(logger=Mock(), conversation_history_store=conversation_history_store)


@pytest.fixture
def mock_chatbot_get_messages() -> Mock:
    return Mock(spec=GetMessages)


@pytest.fixture
def override_di_get_messages(mock_chatbot_get_messages: Mock):
    from api.main import container  # type: ignore

    with container.chatbot_services.get_messages.override(mock_chatbot_get_messages):
        yield


@pytest.mark.usefixtures("override_di_get_messages")
@pytest.mark.user_roles([UserRole.ChatbotUser])
async def test_get_messages_endpoint_makes_expected_service_call(
    steps,
    get_messages: GetMessages,
    mock_chatbot_get_messages: Mock,
    user_id: int,
):
    mock_chatbot_get_messages.get_messages.return_value = await get_messages.get_messages(user_id)

    messages = await steps.chatbot.get_messages()

    assistantTextDTO = messages[0]
    assert int(assistantTextDTO["id"]) > 0
    assert assistantTextDTO["data_layout"] == "assistant_text_layout"
    assert datetime.fromisoformat(assistantTextDTO["timestamp"])
    mock_chatbot_get_messages.get_messages.assert_awaited_once()


@pytest.fixture
def new_conversation(
    mock_bedrock_gateway: AsyncMock, conversation_history_store: ConversationHistoryStore
) -> NewConversation:
    return NewConversation(
        logger=Mock(),
        llm_gateway=mock_bedrock_gateway,
        conversation_history_store=conversation_history_store,
    )


@pytest.fixture
def mock_chatbot_new_conversation() -> Mock:
    return Mock(spec=NewConversation)


@pytest.fixture
def override_di_new_conversation(mock_chatbot_new_conversation: Mock):
    from api.main import container  # type: ignore

    with container.chatbot_services.new_conversation.override(mock_chatbot_new_conversation):
        yield


@pytest.mark.usefixtures("override_di_new_conversation")
async def test_new_conversation_endpoint_makes_expected_service_call(
    steps,
    new_conversation: NewConversation,
    mock_chatbot_new_conversation: Mock,
    user_id: int,
):
    mock_chatbot_new_conversation.new_conversation.return_value = (
        await new_conversation.new_conversation(user_id)
    )

    messages = await steps.chatbot.new_conversation()

    assistantTextDTO = messages[0]
    assert int(assistantTextDTO["id"]) > 0
    assert assistantTextDTO["data_layout"] == "assistant_text_layout"
    assert datetime.fromisoformat(assistantTextDTO["timestamp"])
    mock_chatbot_new_conversation.new_conversation.assert_awaited_once()


@pytest.fixture
def post_message(
    mock_bedrock_gateway: AsyncMock, conversation_history_store: ConversationHistoryStore
) -> PostMessage:
    return PostMessage(
        logger=Mock(),
        internal_client=AsyncMock(),
        llm_gateway=mock_bedrock_gateway,
        conversation_history_store=conversation_history_store,
    )


@pytest.fixture
def mock_chatbot_post_message() -> Mock:
    return Mock(spec=PostMessage)


@pytest.fixture
def override_di_post_message(mock_chatbot_post_message: Mock):
    from api.main import container  # type: ignore

    with container.chatbot_services.post_message.override(mock_chatbot_post_message):
        yield


@pytest.mark.usefixtures("override_di_post_message")
@pytest.mark.user_roles([UserRole.ChatbotUser])
async def test_post_message_endpoint_makes_expected_service_call(
    steps,
    post_message: PostMessage,
    mock_chatbot_post_message: Mock,
    user_id: int,
    jwt_token,
):
    message = "Hello."

    userTextDTO = UserTextDTO(
        message=message,
    )

    mock_chatbot_post_message.process_message.return_value = post_message.process_message(
        userTextDTO,
        user_id,
        jwt_token,
    )

    messages: list[Any] = []

    delimiter = "\n---\n"
    buffer = ""

    async with await steps.chatbot.post_user_text(message=message) as stream:
        async for chunk in stream.aiter_bytes():
            buffer += chunk.decode()
            while delimiter in buffer:
                part, buffer = buffer.split(delimiter, 1)
                if part.strip():
                    messages.append(json.loads(part))

    if buffer.strip():
        messages.append(json.loads(buffer))

    newIdDTO = messages[0]
    assert int(newIdDTO["id"]) > 0
    assert newIdDTO["data_layout"] == "new_id_layout"
    assert datetime.fromisoformat(newIdDTO["timestamp"])

    assistantTextDTO = messages[1]
    assert int(assistantTextDTO["id"]) > 0
    assert assistantTextDTO["data_layout"] == "assistant_text_layout"
    assert datetime.fromisoformat(assistantTextDTO["timestamp"])

    mock_chatbot_post_message.process_message.assert_called_once()
