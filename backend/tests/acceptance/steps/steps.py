import json
from datetime import date
from decimal import Decimal
from typing import Binary<PERSON>, Callable

from faker import Faker
from httpx import AsyncClient
from pydash import omit_by
from starlette import status
from typing_extensions import TypedDict

from api.enums import ClientType, PersonalTitle, UserRole, UserStatus
from app.shared_kernel.enums.queries import OrderDirection
from app.shared_kernel.unset import Unset, unset
from common.utils import jsonify
from tests.acceptance.steps.steps_aml import StepsAML
from tests.acceptance.steps.steps_cases import StepsCases
from tests.acceptance.steps.steps_chatbot import StepsChatbot
from tests.acceptance.steps.steps_common import (
    BaseSteps,
    assert_response,
    dict_without_unset_values,
)
from tests.acceptance.steps.steps_document_generation import StepsDocumentGeneration
from tests.acceptance.steps.steps_documents import StepsDocuments
from tests.acceptance.steps.steps_fees import StepsFees
from tests.acceptance.steps.steps_signup import StepsSignup

faker = Faker()


class StatementLineCreate(TypedDict):
    id: int | None
    amount: str
    client_account_id: int
    fee_type_id: int


class FeeSplitTemplateLine(TypedDict):
    administrator_id: int
    role: str
    type: str
    split_initial: str
    split_ongoing: str
    is_payable: bool


class Steps(BaseSteps):
    def __init__(self, client: AsyncClient(follow_redirects=True)):
        super().__init__(client=client)
        self.client.headers = dict(Authorization="Bearer dummy_bearer")
        self.aml = StepsAML(client)
        self.cases = StepsCases(client)
        self.fees = StepsFees(client)
        self.documents = StepsDocuments(client)
        self.document_generation = StepsDocumentGeneration(client)
        self.chatbot = StepsChatbot(client)
        self.client_signup = StepsSignup(client)

    # TODO AV-101 rename to add_administrator
    @assert_response(status_code=status.HTTP_201_CREATED, jsonable=True)
    async def add_owner(
        self,
        email: str | Callable[[], str] = faker.email,
        first_name: str | Callable[[], str] = faker.first_name,
        last_name: str | Callable[[], str] = faker.last_name,
        roles: list[str] | Callable[[], str] = lambda: ["superadmin"],
        status: UserStatus = UserStatus.Active,
        reached_competent_adviser_date: str | None = None,
        reached_competent_adviser_status: bool = False,
    ):
        email = email() if callable(email) else email
        first_name = first_name() if callable(first_name) else first_name
        last_name = last_name() if callable(last_name) else last_name
        roles = roles() if callable(roles) else roles

        return await self.client.post(
            "/api/v1/advisor",
            json={
                "first_name": first_name,
                "last_name": last_name,
                "email": email,
                "reached_competent_adviser_date": reached_competent_adviser_date,
                "reached_competent_adviser_status": reached_competent_adviser_status,
                "roles": roles,
                "status": status,
            },
        )

    @assert_response()
    async def update_administrator(
        self,
        *,
        administrator_id: int,
        first_name: str,
        last_name: str,
        status: UserStatus | str,
        reached_competent_adviser_date: str | None = None,
        reached_competent_adviser_status: bool = False,
        roles: list[UserRole],
    ):
        data = dict(
            first_name=first_name,
            last_name=last_name,
            status=status,
            roles=roles,
            reached_competent_adviser_date=reached_competent_adviser_date,
            reached_competent_adviser_status=reached_competent_adviser_status,
        )
        return await self.client.put(
            f"/api/v1/advisor/{administrator_id}",
            json=data,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_administrators(
        self,
        order_direction: OrderDirection | None = None,
        status: UserStatus | None = None,
        page: int | None = None,
        per_page: int | None = None,
    ):
        get_params = dict(
            order_direction=order_direction, status=status, page=page, per_page=per_page
        )
        get_params = {k: v for k, v in get_params.items() if v is not None}
        get_params = json.loads(jsonify(get_params))
        return await self.client.get("api/v1/advisor", params=get_params)

    @assert_response(status_code=status.HTTP_201_CREATED, jsonable=True)
    async def add_client(
        self,
        *,
        client_type_id: int = ClientType.Individual,
        title_id: int = PersonalTitle.Miss,
        email: str | Callable[[], str] = faker.email,
        no_email_reason_id: int | None = None,
        first_name: str | Callable[[], str] = faker.first_name,
        last_name: str | Callable[[], str] = faker.last_name,
        client_status: int = 5,
        client_source: int = 1,
        owner_id: int,
        date_of_birth: date | Callable[[], date] = lambda: faker.date_of_birth(minimum_age=18),
        phone_number: str | Callable[[], str] = faker.phone_number,
        mobile_number: str | Callable[[], str] = faker.phone_number,
        links: list | None = None,
    ):
        email = email() if callable(email) else email
        first_name = first_name() if callable(first_name) else first_name
        last_name = last_name() if callable(last_name) else last_name
        date_of_birth = date_of_birth() if callable(date_of_birth) else date_of_birth
        phone_number = phone_number() if callable(phone_number) else phone_number
        mobile_number = mobile_number() if callable(mobile_number) else mobile_number
        return await self.client.post(
            "/api/internal/v1/clients",
            json={
                "client_type_id": client_type_id,
                "title_id": title_id,
                "email": email,
                "no_email_reason_id": no_email_reason_id,
                "first_name": first_name,
                "last_name": last_name,
                "client_status": client_status,
                "client_source": client_source,
                "date_of_birth": str(date_of_birth),
                "owner_id": owner_id,
                "phone_number": phone_number,
                "mobile_number": mobile_number,
                "relations": [],
                "links": links or [],
            },
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def update_client(
        self,
        client_id: int,
        *,
        client_type_id: int = ClientType.Individual,
        title_id: int = PersonalTitle.Miss,
        email: str | Callable[[], str] = faker.email,
        no_email_reason_id: int | None = None,
        first_name: str | Callable[[], str] = faker.first_name,
        last_name: str | Callable[[], str] = faker.last_name,
        client_status: int = 5,
        client_source: int = 1,
        owner_id: int,
        date_of_birth: date | Callable[[], date] = lambda: faker.date_of_birth(minimum_age=18),
        phone_number: str | Callable[[], str] = faker.phone_number,
        mobile_number: str | Callable[[], str] = faker.phone_number,
        relations: list | None = None,
        links: list | None = None,
    ):
        email = email() if callable(email) else email
        first_name = first_name() if callable(first_name) else first_name
        last_name = last_name() if callable(last_name) else last_name
        date_of_birth = date_of_birth() if callable(date_of_birth) else date_of_birth
        phone_number = phone_number() if callable(phone_number) else phone_number
        mobile_number = mobile_number() if callable(mobile_number) else mobile_number
        return await self.client.put(
            f"/api/internal/v1/clients/{client_id}",
            json={
                "client_type_id": client_type_id,
                "title_id": title_id,
                "email": email,
                "no_email_reason_id": no_email_reason_id,
                "first_name": first_name,
                "last_name": last_name,
                "client_status": client_status,
                "client_source": client_source,
                "date_of_birth": str(date_of_birth),
                "owner_id": owner_id,
                "phone_number": phone_number,
                "mobile_number": mobile_number,
                "relations": relations or [],
                "links": links or [],
            },
        )

    @assert_response(status_code=status.HTTP_201_CREATED)
    async def update_review_group(
        self,
        *,
        client_ids: list[int],
        review_frequency: str,
        review_month: int,
    ):
        return await self.client.post(
            "/api/v1/review-group/",
            json={
                "client_ids": client_ids,
                "review_frequency": review_frequency,
                "review_month": review_month,
            },
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_client(self, client_id: int):
        return await self.client.get(f"/api/internal/v1/clients/{client_id}")

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_clients(self, page: int | None = None, page_size: int | None = None):
        params = {}
        if page is not None:
            params["page"] = page
        if page_size is not None:
            params["page_size"] = page_size
        return await self.client.get("/api/internal/v1/clients", params=params)

    @assert_response()
    async def get_active_clients(self):
        return await self.client.get("/api/internal/v1/clients/active-clients")

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_factfind(self, *, client_id: int):
        return await self.client.get(
            f"/api/v1/clients/{client_id}/factfind/primary-details",
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_profile_completeness(self, *, client_id: int, attributes: list[str] = None):
        params = {}
        if attributes:
            params["attributes"] = attributes
        return await self.client.get(
            f"/api/v2/clients/{client_id}/profile-completeness",
            params=params,
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def update_factfind(
        self,
        *,
        client_id: int,
        date_of_birth: date | None = None,
        first_name: str,
        last_name: str,
        gender_id: int | None = None,
        marital_status_id: int | None = None,
        nationality_id: int | None = None,
        birth_country_id: int | None = None,
        primary_country_id: int | None = None,
        secondary_country_id: int | None = None,
        title_id: int | None = None,
        email_address: str | Callable[[], str] = faker.email,
        phone_number: str | None = "",
        mobile_number: str | None = "",
        addresses: list | None = None,
        relations: list | None = None,
        has_experienced_financial_advice_before: bool | None = None,
        previous_investment_experience: str | None = None,
        wish_to_consider_ethical_investments: bool | None = None,
        religious_restrictions: bool | None = None,
        ni_number: str | None = None,
        has_will_in_place: str | None = None,
        has_power_of_attorney_in_place: str | None = None,
        credit_history: str | None = None,
        employment_status: str | None = None,
        retirement_age: int | None = None,
        monthly_retirement_income_required: Decimal | None = None,
        state_pension: bool | None = None,
        already_retired: bool | None = None,
        vulnerable_person: bool | None = None,
    ):
        email_address = email_address() if callable(email_address) else email_address
        addresses = [] if addresses is None else addresses
        relations = [] if relations is None else relations
        date_of_birth = str(date_of_birth) if isinstance(date_of_birth, date) else date_of_birth
        birth_country_id = birth_country_id if birth_country_id else 1  # GB
        primary_country_id = primary_country_id if primary_country_id else 1  # GB

        return await self.client.post(
            f"/api/v1/clients/{client_id}/factfind/primary-details",
            json={
                "date_of_birth": date_of_birth,
                "first_name": first_name,
                "last_name": last_name,
                "gender_id": gender_id,
                "marital_status_id": marital_status_id,
                "title_id": title_id,
                "email_address": email_address,
                "phone_number": phone_number,
                "mobile_number": mobile_number,
                "nationality_id": nationality_id,
                "birth_country_id": birth_country_id,
                "primary_country_id": primary_country_id,
                "secondary_country_id": secondary_country_id,
                "addresses": addresses,
                "relations": relations,
                "factfind": {
                    "has_experienced_financial_advice_before": has_experienced_financial_advice_before,
                    "previous_investment_experience": previous_investment_experience,
                    "wish_to_consider_ethical_investments": wish_to_consider_ethical_investments,
                    "religious_restrictions": religious_restrictions,
                    "ni_number": ni_number,
                    "has_will_in_place": has_will_in_place,
                    "has_power_of_attorney_in_place": has_power_of_attorney_in_place,
                    "credit_history": credit_history,
                    "employment_status": employment_status,
                    "retirement_age": retirement_age,
                    "monthly_retirement_income_required": monthly_retirement_income_required,
                    "state_pension": state_pension,
                    "already_retired": already_retired,
                    "vulnerable_person": vulnerable_person,
                },
            },
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_assets_and_debts(self, *, client_id: int):
        return await self.client.get(
            f"/api/v1/clients/{client_id}/factfind/assets-and-debts",
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def update_assets_and_debts(
        self,
        *,
        client_id: int,
        assets: list[dict],
        debts: list[dict],
    ):
        return await self.client.post(
            f"/api/v1/clients/{client_id}/factfind/assets-and-debts",
            json={
                "assets": assets,
                "debts": debts,
            },
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_product_types(self, product_variant: str | Unset = unset):
        params = dict_without_unset_values(product_variant=product_variant)
        return await self.client.get(
            "/api/v1/refdata/product_types",
            params=params,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_client_goals(self, *, client_id: int):
        return await self.client.get(
            f"/api/v1/clients/{client_id}/factfind/goals",
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def create_client_goal(
        self,
        *,
        client_id: int,
        data: dict,
    ):
        return await self.client.post(
            f"/api/v1/clients/{client_id}/factfind/goals",
            json=data,
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def update_client_goal(
        self,
        *,
        client_id: int,
        goal_id: int,
        data: dict,
    ):
        return await self.client.patch(
            f"/api/v1/clients/{client_id}/factfind/goals/{goal_id}",
            json=data,
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def remove_client_goal(
        self,
        *,
        client_id: int,
        goal_id: int,
    ):
        return await self.client.delete(
            f"/api/v1/clients/{client_id}/factfind/goals/{goal_id}",
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def update_client_goal_objectives(self, *, client_id: int, goal_id: int, objectives: str):
        return await self.client.patch(
            f"/api/v1/clients/{client_id}/factfind/goals/{goal_id}/objectives",
            json={"goal_objectives": objectives},
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def update_client_goal_holdings(
        self, *, client_id: int, goal_id: int, linked_holding_ids: list[int]
    ):
        return await self.client.patch(
            f"/api/v1/clients/{client_id}/factfind/goals/{goal_id}/holdings",
            json={"holding_ids": linked_holding_ids},
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_account(self, *, account_id: int):
        return await self.client.get(
            f"/api/internal/v1/accounts/{account_id}",
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_holding_fees(self, *, account_id: int, page: int = None, page_size: int = None):
        params = dict(page=page, page_size=page_size)
        params = {k: v for k, v in params.items() if v is not None}
        return await self.client.get(
            f"/api/internal/v1/accounts/{account_id}/fees",
            params=params,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_accounts(
        self,
        *,
        page: int,
        per_page: int,
        provider_id: int | None = None,
        type_id: int | None = None,
        status_id: int | None = None,
        text: str | None = None,
    ):
        return await self.client.get(
            "/api/internal/v1/accounts",
            params=omit_by(
                dict(
                    page=page,
                    per_page=per_page,
                    provider_id=provider_id,
                    type_id=type_id,
                    status_id=status_id,
                    text=text,
                ),
                lambda x: not x,
            ),
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_client_accounts(self, *, client_id: int):
        return await self.client.get(f"/api/v1/clients/{client_id}/accounts")

    @assert_response()
    async def get_client_active_accounts(self, *, client_id: int):
        return await self.client.get(f"/api/internal/v1/clients/{client_id}/holdings-active")

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def update_account(
        self,
        *,
        account_id: int,
        clients: list[int] | Unset = unset,
        account_number: str | None | Unset = unset,
        sub_account_number: str | None | Unset = unset,
        account_status_id: int | None | Unset = unset,
        adviser_id: int | None | Unset = unset,
        fee_split_template_id: int | None | Unset = unset,
        fee_model: int | None | Unset = unset,
        portfolio_model_id: int | None | Unset = unset,
        provider_id: int | None | Unset = unset,
        additional_info: str | None | Unset = unset,
    ):
        data = dict_without_unset_values(
            account_number=account_number,
            sub_account_number=sub_account_number,
            account_status_id=account_status_id,
            adviser_id=adviser_id,
            fee_split_template_id=fee_split_template_id,
            fee_model=fee_model,
            portfolio_model_id=portfolio_model_id,
            provider_id=provider_id,
            clients=clients,
            additional_info=additional_info,
        )
        return await self.client.patch(
            f"/api/internal/v1/accounts/{account_id}",
            json=data,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_fee_split_template(self, *, fee_split_template_id: int):
        return await self.client.get(
            f"/api/internal/v1/fee-split-templates/{fee_split_template_id}"
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def add_fee_split_template(
        self,
        *,
        administrator_id: int,
        is_active: bool,
        template_name: str,
        lines: list[FeeSplitTemplateLine],
    ):
        return await self.client.post(
            "/api/internal/v1/fee-split-templates",
            json={
                "administrator_id": administrator_id,
                "is_active": is_active,
                "template_name": template_name,
                "lines": lines,
            },
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=False)
    async def update_fee_split_template(
        self,
        *,
        template_id: int,
        administrator_id: int,
        is_active: bool,
        template_name: str,
        lines: list[dict],
    ):
        return await self.client.put(
            f"/api/internal/v1/fee-split-templates/{template_id}",
            json={
                "administrator_id": administrator_id,
                "is_active": is_active,
                "template_name": template_name,
                "lines": lines,
            },
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def import_statements_from_excel(self, *, file: bytes | BinaryIO, provider_id: int):
        return await self.client.request(
            "POST",
            "/api/internal/v1/fees/statements/excel-import",
            files={"file": file},
            data={"provider_id": provider_id},
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_client_income(self, *, client_id: int):
        return await self.client.get(
            f"/api/v1/clients/{client_id}/factfind/income",
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_client_expenditure(self, *, client_id: int):
        return await self.client.get(
            f"/api/v1/clients/{client_id}/factfind/expenditure",
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def update_client_income(
        self,
        *,
        client_id: int,
        income: list[dict],
    ):
        return await self.client.put(
            f"/api/v1/clients/{client_id}/factfind/income",
            json=income,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def update_client_expenditure(
        self,
        *,
        client_id: int,
        expenditure: list[dict],
    ):
        return await self.client.put(
            f"/api/v1/clients/{client_id}/factfind/expenditure",
            json=expenditure,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_ref_data(self):
        return await self.client.get(
            "/api/v2/refdata",
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_clients_related_to_holding(self, provider_id: int):
        return await self.client.get(
            "/api/internal/v1/query/get-clients-related-to-holding",
            params=dict(provider_id=provider_id),
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_holding_statuses(self, category: str | Unset = unset):
        params = dict(category=category)
        params = {k: v for k, v in params.items() if v is not unset}
        return await self.client.get("/api/v1/refdata/statuses", params=params)

    @assert_response()
    async def get_clients_active_holdings(self, *, client_ids: list[int]):
        params = [("client_ids", client_id) for client_id in client_ids]
        return await self.client.get(
            "/api/internal/v1/active-holdings",
            params=params,
        )

    @assert_response()
    async def get_client_notes(self, *, client_id: int):
        return await self.client.get(
            f"/api/internal/v1/clients/{client_id}/notes",
        )

    @assert_response()
    async def add_client_note(self, *, client_id: int, note: str):
        return await self.client.post(
            f"/api/internal/v1/clients/{client_id}/notes",
            json=dict(content=note),
        )

    @assert_response()
    async def get_current_user(self):
        return await self.client.get("/api/v1/user/me")

    @assert_response()
    async def get_tasks(
        self,
        assignee_id: int = unset,
        assignee_group: UserRole = unset,
        slugs: list[str] = unset,
        page: int = unset,
        page_size: int = unset,
    ):
        params = dict_without_unset_values(
            assignee_id=assignee_id,
            assignee_group=assignee_group.value if assignee_group is not unset else unset,
            page=page,
            page_size=page_size,
        )

        if slugs is not unset:
            params["slugs[]"] = ",".join(slugs)

        return await self.client.get("/api/v1/task", params=params)

    @assert_response()
    async def get_holding_valuations(self, *, holding_id: int):
        return await self.client.get(f"/api/v1/holdings/{holding_id}/valuation")

    @assert_response()
    async def add_holding_valuations(
        self, *, amount: float, date_: date | str, is_actual: bool, holding_id: int
    ):
        data = dict(
            amount=amount,
            date=date_,
            is_actual=is_actual,
        )
        return await self.client.post(f"/api/v1/holdings/{holding_id}/valuation", json=data)

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def delete_holding_valuation(self, holding_id: int, valuation_date: date) -> None:
        return await self.client.delete(
            f"/api/v1/holdings/{holding_id}/valuation?valuation_date={valuation_date.isoformat()}"
        )

    @assert_response()
    async def get_holding_plan_info(
        self,
        *,
        holding_id: int,
    ):
        return await self.client.get(f"/api/v2/holdings/{holding_id}/plan_information")

    @assert_response(status_code=status.HTTP_201_CREATED)
    async def add_holding_plan_info(
        self,
        *,
        holding_id: int,
        portfolio_model: int,
        data_layout: str,
        date_of_information: date,
        notes: str,
        fund_value: Decimal,
        transfer_value: Decimal,
        charges_figures: dict,
        investment_info: dict,
        constituents: list,
    ):
        data = dict(
            date_of_information=date_of_information,
            portfolio_model=portfolio_model,
            data_layout=data_layout,
            fund_value=fund_value,
            transfer_value=transfer_value,
            charges_figures=charges_figures,
            investment_info=investment_info,
            constituents=constituents,
            notes=notes,
        )
        return await self.client.post(f"/api/v2/holdings/{holding_id}/plan_information", json=data)

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def update_holding_plan_info(
        self,
        *,
        plan_id: int,
        holding_id: int,
        portfolio_model: int,
        data_layout: str,
        date_of_information: date,
        notes: str,
        fund_value: Decimal,
        transfer_value: Decimal,
        charges_figures: dict,
        investment_info: dict,
        constituents: list,
    ):
        data = dict(
            date_of_information=date_of_information,
            portfolio_model=portfolio_model,
            data_layout=data_layout,
            fund_value=fund_value,
            transfer_value=transfer_value,
            charges_figures=charges_figures,
            investment_info=investment_info,
            constituents=constituents,
            notes=notes,
        )
        return await self.client.put(
            f"/api/v2/holdings/{holding_id}/plan_information/{plan_id}", json=data
        )

    @assert_response()
    async def get_holding_plan_info_constituents(self, *, holding_id: int, plan_id: int):
        return await self.client.get(
            f"/api/v1/holdings/{holding_id}/plan_information/{plan_id}/constituents",
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def update_holding_plan_info_constituents(
        self, *, holding_id: int, plan_id: int, constituents: list
    ):
        data = dict(constituents=constituents)
        return await self.client.post(
            f"/api/v1/holdings/{holding_id}/plan_information/{plan_id}/constituents",
            json=data,
        )

    @assert_response()
    async def calculate_annual_overall_charge_using_figures(
        self,
        *,
        charges_plan_amc: Decimal | None,
        charges_fund_amc: Decimal | None,
        charges_dfm_fee: Decimal | None,
        additional_fee_amount: Decimal | None,
        fund_value: Decimal | None,
    ):
        data = dict(
            charges_plan_amc=charges_plan_amc,
            charges_fund_amc=charges_fund_amc,
            charges_dfm_fee=charges_dfm_fee,
            additional_fee_amount=additional_fee_amount,
            fund_value=fund_value,
        )
        return await self.client.post(
            "/api/v2/holdings/plan_information/calculate_annual_overall_charge_using_figures",
            json=data,
        )

    @assert_response()
    async def calculate_annual_overall_charge_using_projections(
        self,
        *,
        retirement_date_on_projections: date | None,
        date_of_projection: date | None,
        projection_figure_pounds: Decimal | None,
        fund_value: Decimal | None,
        rate_of_projection: Decimal | None,
    ):
        data = dict(
            retirement_date_on_projections=retirement_date_on_projections,
            date_of_projection=date_of_projection,
            projection_figure_pounds=projection_figure_pounds,
            fund_value=fund_value,
            rate_of_projection=rate_of_projection,
        )
        return await self.client.post(
            "/api/v2/holdings/plan_information/calculate_annual_overall_charge_using_projections",
            json=data,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def get_client_marketing_details(self, *, client_id: int):
        return await self.client.get(
            f"/api/internal/v1/clients/{client_id}/marketing/contact",
            follow_redirects=True,
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def subscribe_client_to_mailing_list(
        self, *, client_id: int, contact_id: str = "1", list_id: str
    ):
        return await self.client.post(
            f"/api/internal/v1/clients/{client_id}/marketing/subscribe",
            json=dict(contact_id=contact_id, list_id=list_id),
        )

    @assert_response(status_code=status.HTTP_200_OK, jsonable=True)
    async def unsubscribe_client_to_mailing_list(
        self, *, client_id: int, contact_id: str = "1", list_id: str
    ):
        return await self.client.post(
            f"/api/internal/v1/clients/{client_id}/marketing/unsubscribe",
            json=dict(contact_id=contact_id, list_id=list_id),
        )

    @assert_response(status_code=status.HTTP_204_NO_CONTENT, jsonable=False)
    async def set_client_access(self, client_id: int, status: bool):
        return await self.client.put(
            f"/api/internal/v1/clients/{client_id}/client-access",
            json=dict(enabled=status),
            follow_redirects=True,
        )

    async def post_upload_holding_valuations_spreadsheet(
        self, provider_id: int, valuation_date: date, file_content: bytes
    ):
        return await self.client.post(
            f"/api/v1/holdings/upload-valuations?provider_id={provider_id}&valuation_date={valuation_date.isoformat()}",
            files={"upload": ("test.xlsx", file_content)},
        )
