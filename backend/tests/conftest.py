# pylint: disable=E1137
from datetime import date
from pathlib import Path

import alembic
import asyncpg
import pytest
import sqlalchemy as sa
from alembic.config import Config
from sqlalchemy import delete
from sqlalchemy.ext.asyncio import (
    AsyncConnection,
    AsyncEngine,
    AsyncSession,
    create_async_engine,
)

from api.enums import UserRole
from common.config.settings import get_settings
from database.models import (
    BaseDBModel,
    ClientHoldingModel,
    ClientModel,
    FeeSplitTemplateLinesModel,
    FeeSplitTemplateModel,
)
from database.models.factfind_models import ClientFactfindModel
from database.models.user_models import OwnerModel
from database.session.Session import PostgresSessionMaker
from gateway_accessors.cognito.accessor import DevCognitoGateway
from gateway_accessors.redis.accessor import RedisDataKeys, RedisGateway

from .config import get_tests_settings

BACKEND_PATH = Path(__file__).parents[1]
ENV_PATH = BACKEND_PATH / "alembic.ini"
MIGRATIONS_PATH = BACKEND_PATH / "migrations"
DB_TEMPLATE_NAME_SEEDED = "test_db_template"
DB_TEMPLATE_NAME_CLEAN = "test_db_template_empty"


def __run_migrations(connection):
    alembic_cfg = Config(ENV_PATH)
    alembic_cfg.attributes["connection"] = connection
    alembic_cfg.set_main_option("script_location", str(MIGRATIONS_PATH))

    alembic.command.upgrade(alembic_cfg, "head")


@pytest.fixture(scope="session")
def settings():
    return get_tests_settings()


@pytest.fixture(scope="session")
def postgres_database(settings) -> PostgresSessionMaker:
    return PostgresSessionMaker(db_url=settings.async_url)


@pytest.fixture(scope="session")
async def async_session_factory(postgres_database):
    yield postgres_database._get_session_maker()


@pytest.fixture()
async def async_session(async_session_factory):
    async with async_session_factory() as session:
        session: AsyncSession
        yield session


@pytest.fixture(scope="session")
def owner():
    return {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Agnes",
        "last_name": "Martin",
        "is_active": True,
        "roles": [UserRole.SuperAdmin],
        "reached_competent_adviser_status": True,
        "reached_competent_adviser_date": date.today(),
        "cognito_id": "bazinga cognito id meow",
    }


@pytest.fixture(scope="session")
def client_1():
    return {
        "id": 2,
        "email": "<EMAIL>",
        "first_name": "Barry",
        "last_name": "Taylor",
        "is_active": True,
        "owner_id": 1,
        "client_status_id": 5,
        "cognito_id": "barry's cognito id",
    }


@pytest.fixture()
async def remove_seed_client_data(uow):
    async with uow:
        await uow.session.execute(delete(ClientFactfindModel))
        await uow.session.execute(delete(ClientModel))
        await uow.commit()


@pytest.fixture()
async def remove_seed_data(uow):
    async with uow:
        await uow.session.execute(delete(ClientHoldingModel))
        await uow.session.execute(delete(ClientFactfindModel))
        await uow.session.execute(delete(ClientModel))
        await uow.session.execute(delete(FeeSplitTemplateLinesModel))
        await uow.session.execute(delete(FeeSplitTemplateModel))
        await uow.session.execute(delete(OwnerModel))
        await uow.commit()


@pytest.fixture(autouse=True, scope="session")
async def db_template_setup(owner, client_1, settings):
    con = await asyncpg.connect(settings.postgres_url)
    await con.execute(f"DROP DATABASE IF EXISTS {DB_TEMPLATE_NAME_SEEDED}")
    await con.execute(f"CREATE DATABASE {DB_TEMPLATE_NAME_SEEDED}")

    engine: AsyncEngine = create_async_engine(settings.template_url(DB_TEMPLATE_NAME_SEEDED))
    try:
        async with engine.begin() as conn:
            conn: AsyncConnection
            await conn.run_sync(BaseDBModel.metadata.drop_all)
            await conn.run_sync(__run_migrations)
            async with AsyncSession(conn) as session:
                session.add(OwnerModel(**owner))
                session.add(ClientModel(**client_1, factfind=ClientFactfindModel()))
                await session.commit()
                await session.execute(
                    sa.text(
                        "SELECT pg_catalog.setval(pg_get_serial_sequence('users', 'id'), MAX(id)) FROM users;"
                    )
                )
            await conn.commit()
        await engine.dispose()
        yield
    finally:
        async with engine.begin() as conn:
            await conn.run_sync(BaseDBModel.metadata.drop_all)
        await engine.dispose()


@pytest.fixture(autouse=True, scope="session")
async def db_template_setup_empty(settings):
    con = await asyncpg.connect(settings.postgres_url)
    try:
        connection_records = await con.fetch(
            """
            SELECT pid
            FROM pg_stat_activity
            WHERE datname = $1
            AND pid != pg_backend_pid()
            """,
            DB_TEMPLATE_NAME_CLEAN,
        )
        for connection_record in connection_records:
            print(connection_record)
            await con.execute("SELECT pg_terminate_backend($1);", connection_record["pid"])
    except Exception as e:
        print(f"Error terminating: {e}")
    await con.execute(f"DROP DATABASE IF EXISTS {DB_TEMPLATE_NAME_CLEAN}")
    await con.execute(f"CREATE DATABASE {DB_TEMPLATE_NAME_CLEAN}")

    engine: AsyncEngine = create_async_engine(settings.template_url(DB_TEMPLATE_NAME_CLEAN))
    async with engine.begin() as conn:
        await conn.run_sync(BaseDBModel.metadata.drop_all)
        await conn.run_sync(BaseDBModel.metadata.create_all)


@pytest.fixture(autouse=True)
async def db_for_tests(request, settings):
    con = await asyncpg.connect(settings.postgres_url)
    await con.execute(
        """
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = $1;
    """,
        settings.db_database_name,
    )
    await con.execute(f"DROP DATABASE IF EXISTS {settings.db_database_name}")
    await con.execute(
        f"CREATE DATABASE {settings.db_database_name} TEMPLATE {settings.db_database_template_name}"
    )
    await con.close()


@pytest.fixture()
def cognito_proxy() -> DevCognitoGateway:
    # TODO AV-451 create a pool when setting a docker
    #  maybe by copying files

    settings = get_settings()
    cognito_proxy = DevCognitoGateway(
        aws_region=settings.aws_region,
        aws_access_key_id=settings.aws_access_key_id,
        aws_secret_access_key=settings.aws_secret_access_key,
        aws_user_pool_id=settings.aws_user_pool_id,
        aws_endpoint_url=settings.aws_cognito_endpoint_url,
        aws_send_email_on_user_creation=settings.aws_send_email_on_user_creation,
    )
    if not cognito_proxy.aws_user_pool_id.startswith("cognito_local"):
        raise ValueError(
            "AWS_USER_POOL_ID must be set to `cognito_local[.test]` for tests"
            " (I'm trying to prevent someone accidentally deleting prod user pool)."
        )
    users = cognito_proxy.list_users()
    for user in users:
        cognito_proxy.delete_cognito_user(email=user)
    for role in list(UserRole):
        cognito_proxy.aws_client.create_group(
            GroupName=role.value, UserPoolId=cognito_proxy.aws_user_pool_id
        )
    yield cognito_proxy


@pytest.fixture
async def redis_gateway(settings) -> RedisGateway:
    gateway = RedisGateway(redis_host=settings.redis_host, redis_port=settings.redis_port)
    await gateway._client.flushall()
    yield gateway
    await gateway.connection_pool.aclose()


@pytest.fixture()
def clear_redis_cache(redis_gateway):
    redis_gateway.delete(RedisDataKeys.REF_DATA_ALL)
