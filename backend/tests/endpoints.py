from api.security.permission_roles import permission_roles

ENDPOINTS = {
    "/api/v2/chatbot/messages": {
        "get": permission_roles["chatbot_user"],
    },
    "/api/v2/chatbot/message": {
        "post": permission_roles["chatbot_user"],
    },
    "/api/v2/chatbot/new-conversation": {
        "get": permission_roles["authenticated"],
    },
    "/api/v2/holdings/plan_information/calculate_annual_overall_charge_using_figures": {
        "post": permission_roles["default"],
    },
    "/api/v2/holdings/plan_information/calculate_annual_overall_charge_using_projections": {
        "post": permission_roles["default"],
    },
    "/api/v1/holdings/{holding_id}/valuation": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
        "delete": permission_roles["default"],
    },
    "/api/v1/holdings/upload-valuations": {
        "post": permission_roles["default"],
    },
    "/api/v2/holdings/{holding_id}/plan_information": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
    },
    "/api/v2/holdings/{holding_id}/plan_information/{plan_id}": {
        "put": permission_roles["default"],
    },
    "/api/v1/holdings/{holding_id}/investment-constituents": {
        "get": permission_roles["default"],
    },
    "/api/v1/user": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
    },
    "/api/v1/user/me": {
        "get": permission_roles["authenticated"],
    },
    "/api/v1/advisor": {
        "get": permission_roles["authenticated"],
        "post": permission_roles["superadmin"],
    },
    "/api/v1/advisor/{administrator_id}": {
        "put": permission_roles["superadmin"],
    },
    "/api/internal/v1/clients/{client_id}/cashflows": {
        "get": permission_roles["default"],
        "put": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/cashflows/{id}": {
        "delete": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/case": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/case/available-review-slots": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/marketing/contact": {
        "get": permission_roles["authenticated"],
        "post": permission_roles["authenticated"],
        "put": permission_roles["authenticated"],
    },
    "/api/internal/v1/clients/{client_id}/marketing/subscribe": {
        "post": permission_roles["authenticated"],
    },
    "/api/internal/v1/clients/{client_id}/marketing/unsubscribe": {
        "post": permission_roles["authenticated"],
    },
    "/api/v1/goals/{goal_id}/risk-profile": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
    },
    "/api/v1/goals/{goal_id}/cash-forecast": {
        "get": permission_roles["default"],
    },
    "/api/v1/goals/cash-forecast": {
        "post": permission_roles["default"],
    },
    "/api/v2/refdata": {
        "get": permission_roles["authenticated"],
    },
    "/api/v1/refdata/product_types": {
        "get": permission_roles["authenticated"],
    },
    "/api/v1/refdata/statuses": {
        "get": permission_roles["authenticated"],
    },
    "/api/v1/products": {
        "get": permission_roles["default"],
    },
    "/api/v1/products/{product_id}": {
        "put": permission_roles["default"],
    },
    "/api/v1/case/upcoming-reviews": {
        "get": permission_roles["default"],
    },
    "/api/v1/statistics/client-count": {
        "get": permission_roles["default"],
    },
    "/api/v1/statistics/active-account-count": {
        "get": permission_roles["default"],
    },
    "/api/v1/statistics/open-cases-count": {
        "get": permission_roles["default"],
    },
    "/api/v1/statistics/my-tasks-count": {
        "get": permission_roles["default"],
    },
    "/api/v1/statistics/adviser-fees-forecast": {
        "get": permission_roles["default"],
    },
    "/api/v1/task": {
        "get": permission_roles["default"],
    },
    "/api/v1/clients/{client_id}/accounts": {
        "get": permission_roles["default"],
    },
    "/api/v1/clients/{client_id}/factfind/primary-details": {
        "get": permission_roles["authenticated"],
        "post": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/factfind/assets-and-debts": {
        "get": permission_roles["authenticated"],
        "post": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/factfind/goals": {
        "get": permission_roles["authenticated"],
        "post": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/factfind/goals/{goal_id}": {
        "delete": permission_roles["authenticated"],
        "patch": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/factfind/goals/{goal_id}/objectives": {
        "patch": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/factfind/goals/{goal_id}/holdings": {
        "patch": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/factfind/income": {
        "get": permission_roles["authenticated"],
        "put": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/factfind/expenditure": {
        "get": permission_roles["authenticated"],
        "put": permission_roles["authenticated"],
    },
    "/api/v1/clients/{client_id}/portfolio-valuations": {
        "get": permission_roles["authenticated"],
    },
    "/api/internal/v1/clients/{client_id}/is-aml-ready": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/clients": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
    },
    "/api/internal/v1/clients/active-clients": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}": {
        "get": permission_roles["authenticated"],
        "put": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/notes": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/client-access": {
        "put": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/aml/validate-identity": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/aml/monitoring": {
        "get": permission_roles["default"],
        "post": permission_roles["default"],
    },
    "/api/internal/v1/query/get-clients-related-to-holding": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fee-split-templates": {
        "get": permission_roles["default"],
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fee-split-templates/{template_id}": {
        "get": permission_roles["default"],
        "put": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/fees": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fees/get-matches": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fees/set-matches": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/add-client-payment": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/add-provider-payment": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/remove-payment": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/update-client-payment": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/update-provider-payment": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/payments": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fees/client-payments/{payment_id}": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fees/provider-payments/{payment_id}": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fees/add-client-statement": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/add-provider-statement": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/remove-statement": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/update-client-statement": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/update-provider-statement": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/statements/excel-import": {
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/fees/statements": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fees/client-statements/{statement_id}": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/fees/provider-statements/{statement_id}": {
        "get": permission_roles["default"]
    },
    "/api/internal/v1/fees/pay-run": {
        "get": permission_roles["default"],
        "post": permission_roles["superadmin"],
    },
    "/api/internal/v1/accounts/{account_id}": {
        "get": permission_roles["default"],
        "patch": permission_roles["default"],
    },
    "/api/internal/v1/accounts/{holding_id}/fees": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/holdings": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/holdings-active": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/active-holdings": {
        "get": permission_roles["authenticated"],
    },
    "/api/internal/v1/accounts": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/marketing/contacts": {
        "get": permission_roles["default"],
    },
    "/api/internal/v1/marketing/subscription-lists": {
        "get": permission_roles["authenticated"],
    },
    "/api/internal/v1/marketing/templates/{template_id}": {
        "get": permission_roles["default"],
    },
    "/api/v1/document-generation/templates": {
        "post": permission_roles["default"],
    },
    "/api/v1/document-generation/render-template": {
        "post": permission_roles["default"],
    },
    "/api/internal/v1/clients/{client_id}/review-group": {"get": permission_roles["default"]},
    "/api/internal/v1/clients/{client_id}/review-slots": {"get": permission_roles["default"]},
    "/api/v1/review-group": {
        "post": permission_roles["default"],
    },
    "/api/v1/review-group/available-review-slots": {"get": permission_roles["default"]},
}

OPEN_ENDPOINTS = {"/api/v1/webhooks/aml/monitoring/zignsec"}
