from datetime import date

import pytest

from api import enums
from app.cases.domain.events.task_events import ClientEmailSendRequested
from app.cases.domain.models.case_models import Case, CaseGoal, GoalRef, ReviewSlot
from app.cases.domain.models.enums import TaskType
from app.cases.domain.models.user_models import TransactionActor
from app.cases.domain.task_entities.client_message import (
    AnnualReviewMessageTask,
    ClientMessageTask,
    WelcomeEmailMessageTask,
)


@pytest.fixture
def case_goal(joint_clients) -> CaseGoal:
    return CaseGoal(id=2, goal_ref=GoalRef(code="PY"), clients=joint_clients)


@pytest.fixture
def annual_review_message_task(case_goal: CaseGoal) -> AnnualReviewMessageTask:
    return AnnualReviewMessageTask(
        id=1,
        order=1,
        description="",
        case_goal_id=case_goal.id,
        task_slug="slug",
        task_type=TaskType.GOAL,
        status=enums.TaskStatus.InProgress,
    )


@pytest.fixture
def welcome_message_task(case_goal: CaseGoal) -> WelcomeEmailMessageTask:
    return WelcomeEmailMessageTask(
        id=2,
        order=2,
        description="",
        case_goal_id=case_goal.id,
        task_slug="slug",
        task_type=TaskType.GOAL,
        status=enums.TaskStatus.InProgress,
    )


@pytest.fixture
def case(
    case_goal: CaseGoal,
    annual_review_message_task: AnnualReviewMessageTask,
    welcome_message_task: WelcomeEmailMessageTask,
    joint_clients,
    adviser_actor,
    competent_adviser,
) -> Case:
    case_instance = Case(
        id=1,
        case_type=enums.CaseType.AnnualReview,
        status=enums.CaseStatus.Open,
        goals={case_goal.id: case_goal},
        adviser=competent_adviser,
        actor=adviser_actor,
        clients=joint_clients,
        created_date=date.today(),
        review_slot=ReviewSlot(id=99),
    )
    case_instance.add_task(annual_review_message_task)
    case_instance.add_task(welcome_message_task)
    return case_instance


@pytest.mark.usefixtures("freeze_event_uuid", "freeze_time")
@pytest.mark.parametrize(
    "task_class, task_id", ((AnnualReviewMessageTask, 1), (WelcomeEmailMessageTask, 2))
)
def test_request_document_queues_expected_event(
    task_class: ClientMessageTask,
    task_id: int,
    case: Case,
    case_goal: CaseGoal,
    adviser_actor: TransactionActor,
) -> None:
    assert len(case._events) == 0

    task: ClientMessageTask = case.get_task_by_id(task_id)
    task.send_email(cc_transaction_actor=True)

    assert len(case._events) == 1
    assert case._events[0] == ClientEmailSendRequested(
        case_id=case.id,
        case_goal_id=case_goal.id,
        task_slug=task.task_slug,
        task_id=task.id,
        actor_first_name=adviser_actor.first_name,
        actor_last_name=adviser_actor.last_name,
        actor_email=adviser_actor.email,
        to=[client.email for client in case.clients],
        cc=adviser_actor.email,
        subject=task.subject,
        body=task.body,
        template_alias=task.template_alias,
        template_model=task.template_model,
    )
