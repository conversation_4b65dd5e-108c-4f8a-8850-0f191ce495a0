# pylint: disable=E1101
from datetime import date
from unittest.mock import Magic<PERSON>ock

import pytest

from api.exceptions.client_exceptions import ClientDataIncompleteError
from app.aml.services.aml_service import AMLCommandService
from app.clients.services.add_client import AddClientService
from app.shared_kernel.dtos.client_dtos import AddressDTO, ClientDetailDTO, CountryDTO
from tests.unit.services.fake_repo_and_uow import FakeUnitOfWork


def _create_address():
    return AddressDTO(
        address_line_one="1 Somewhere",
        city="Somewhere",
        post_code="S12 3AB",
        country=CountryDTO(id=1, name="United Kingdom", iso_alpha2="GB", iso_alpha3="GBR"),
        country_id=1,
        is_primary=True,
    )


@pytest.fixture
def aml_command_service(uow: FakeUnitOfWork) -> AMLCommandService:
    return AMLCommandService(uow=uow, logger=MagicMock(), zignsec_accessor=MagicMock())


@pytest.mark.asyncio
class TestValidateClientProfile:
    async def test_valid_profile(
        self,
        uow: FakeUnitOfWork,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        address = _create_address()
        async with uow:
            uow.clients2.get_by_id.return_value = ClientDetailDTO(
                id=1,
                title_id=1,
                first_name="first",
                last_name="last",
                email="email",
                type="client",
                addresses=[address],
                links=[],
                date_of_birth=date(year=2000, month=2, day=2),
            )

        assert await aml_command_service.is_aml_ready(1)

    async def test_profile_with_address_missing_country(
        self,
        uow: FakeUnitOfWork,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        address = _create_address()
        address.country = None
        async with uow:
            uow.clients2.get_by_id.return_value = ClientDetailDTO(
                id=1,
                title_id=1,
                first_name="first",
                last_name="last",
                email="email",
                type="client",
                addresses=[address],
                links=[],
                date_of_birth=date(year=2000, month=2, day=2),
            )

        with pytest.raises(ClientDataIncompleteError) as e:
            await aml_command_service.is_aml_ready(1)
        assert e.value.error_message == f"Incomplete profile or address details (Client ID: {1})"

    async def test_profile_without_address(
        self,
        uow: FakeUnitOfWork,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        async with uow:
            uow.clients2.get_by_id.return_value = ClientDetailDTO(
                id=1,
                title_id=1,
                first_name="first",
                last_name="last",
                email="email",
                type="client",
                addresses=[],
                links=[],
                date_of_birth=date(year=2000, month=2, day=2),
            )

        with pytest.raises(ClientDataIncompleteError) as e:
            await aml_command_service.is_aml_ready(1)
        assert e.value.error_message == f"Incomplete profile or address details (Client ID: {1})"

    async def test_profile_without_dob(
        self,
        uow: FakeUnitOfWork,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        async with uow:
            uow.clients2.get_by_id.return_value = ClientDetailDTO(
                id=1,
                title_id=1,
                first_name="first",
                last_name="last",
                email="email",
                type="client",
                addresses=[_create_address()],
                links=[],
                date_of_birth=None,
            )

        with pytest.raises(ClientDataIncompleteError) as e:
            await aml_command_service.is_aml_ready(1)
        assert e.value.error_message == f"Incomplete profile or address details (Client ID: {1})"
