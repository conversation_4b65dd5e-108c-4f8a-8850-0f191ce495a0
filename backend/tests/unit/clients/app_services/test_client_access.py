from unittest.mock import AsyncMock, MagicMock, patch

import faker
import pytest

from api.enums import ClientStatus, User<PERSON><PERSON>
from app.clients.exceptions import InactiveClient, InvalidEmail
from app.clients.services.client_access import ClientAccessService
from app.shared_kernel.dtos.client_dtos import ClientD<PERSON>ilD<PERSON>
from tests import fakes
from tests.unit.services.fake_repo_and_uow import FakeUnitOfWork

fake = faker.Faker()


@pytest.mark.asyncio
class TestClientAccess:
    @pytest.mark.parametrize(
        "status",
        [
            ClientStatus.LeadLive,
            ClientStatus.LeadDormant,
            ClientStatus.Inactive,
            ClientStatus.InactiveLeaving,
            ClientStatus.InactiveDeceased,
            ClientStatus.InactiveLostLead,
        ],
    )
    async def test_should_raise_for_inactive_status(
        self,
        status,
        uow: FakeUnitOfWork,
        cognito_proxy,
        add_client_service,
    ) -> None:
        set_client_access = ClientAccessService(
            uow=uow,
            cognito=cognito_proxy,
            logger=MagicMock(),
            sync_cognito_command=AsyncMock(),
            mailer=MagicMock(),
        )

        # Given
        client_data = fakes.fake_client_data(email=fake.email(), client_status=status)
        client_id = await add_client_service.add_client(**client_data)

        # Then
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        with pytest.raises(InactiveClient):
            await set_client_access(client_data=client_details, status=True)

    async def test_should_raise_for_missing_email(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        add_client_service,
    ) -> None:
        set_client_access = ClientAccessService(
            uow=uow,
            cognito=cognito_proxy,
            logger=MagicMock(),
            sync_cognito_command=AsyncMock(),
            mailer=MagicMock(),
        )

        # Given
        client_data = fakes.fake_client_data(
            client_status=ClientStatus.ActiveOngoing,
            email=None,
        )
        client_id = await add_client_service.add_client(**client_data)

        # Then
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        with pytest.raises(InvalidEmail):
            await set_client_access(client_data=client_details, status=True)

    async def test_should_raise_for_invalid_email(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        add_client_service,
    ) -> None:
        set_client_access = ClientAccessService(
            uow=uow,
            cognito=cognito_proxy,
            logger=MagicMock(),
            sync_cognito_command=AsyncMock(),
            mailer=MagicMock(),
        )

        # Given
        client_data = fakes.fake_client_data(
            client_status=ClientStatus.ActiveOngoing,
            email="noemail",
        )
        client_id = await add_client_service.add_client(**client_data)

        # Then
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        with pytest.raises(InvalidEmail):
            await set_client_access(client_data=client_details, status=True)

    async def test_should_enable_access_for_active_clients(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        monkeypatch,
        add_client_service,
    ) -> None:
        set_client_access = ClientAccessService(
            uow=uow,
            cognito=cognito_proxy,
            logger=MagicMock(),
            sync_cognito_command=AsyncMock(),
            mailer=MagicMock(),
        )

        # Given
        client_data = fakes.fake_client_data(
            email=fake.email(),
            client_status=ClientStatus.ActiveOngoing,
        )
        client_id = await add_client_service.add_client(**client_data)
        async with uow:
            await uow.users.add(
                MagicMock(
                    email=client_data["email"],
                    cognito_id=None,
                )
            )

        # Then
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await set_client_access(client_data=client_details, status=True)
            assert cognito_proxy.get_cognito_user(client_data["email"])

    async def test_should_disable_access_for_active_clients_without_access(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        monkeypatch,
        add_client_service,
    ) -> None:
        set_client_access = ClientAccessService(
            uow=uow,
            cognito=cognito_proxy,
            logger=MagicMock(),
            sync_cognito_command=AsyncMock(),
            mailer=MagicMock(),
        )

        # Given
        client_data = fakes.fake_client_data(
            email=fake.email(),
            client_status=ClientStatus.ActiveOngoing,
        )
        client_id = await add_client_service.add_client(**client_data)
        cognito_id = cognito_proxy.create_cognito_user(client_data["email"], [UserRole.Client])
        async with uow:
            await uow.users.add(
                MagicMock(
                    email=client_data["email"],
                    cognito_id=cognito_id,
                )
            )

        # Then
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        await set_client_access(client_data=client_details, status=False)
        cognito_user = cognito_proxy.get_cognito_user(client_data["email"])
        assert not cognito_user["Enabled"]

    async def test_should_enable_access_for_active_clients_with_disabled_access(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        monkeypatch,
        add_client_service,
    ) -> None:
        set_client_access = ClientAccessService(
            uow=uow,
            cognito=cognito_proxy,
            logger=MagicMock(),
            sync_cognito_command=AsyncMock(),
            mailer=MagicMock(),
        )

        # Given
        client_data = fakes.fake_client_data(
            email=fake.email(),
            client_status=ClientStatus.ActiveOngoing,
        )
        client_id = await add_client_service.add_client(**client_data)
        cognito_id = cognito_proxy.create_cognito_user(client_data["email"], [UserRole.Client])
        async with uow:
            await uow.users.add(
                MagicMock(
                    email=client_data["email"],
                    cognito_id=cognito_id,
                )
            )

        # When
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        await set_client_access(client_data=client_details, status=False)

        # Then
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await set_client_access(client_data=client_details, status=True)
            cognito_user = cognito_proxy.get_cognito_user(client_data["email"])
            assert cognito_user["Enabled"]

    async def test_email_change_with_enabled_access(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        monkeypatch,
        add_client_service,
        update_client_service,
    ) -> None:
        # Given
        client_data = fakes.fake_client_data(
            email=fake.email(),
            client_status=ClientStatus.ActiveOngoing,
        )
        user = MagicMock(
            email=client_data["email"],
            cognito_id=None,
        )
        client_id = await add_client_service.add_client(**client_data)
        async with uow:
            await uow.users.add(user)
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.client_access(client_data=client_details, status=True)

        # When
        new_email = fake.email()
        user.email = new_email
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.update_client(
                client_id=client_id,
                email=new_email,
                client_status=ClientStatus.ActiveOngoing,
            )

        # Then
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            cognito_user = cognito_proxy.get_cognito_user(new_email)
            assert cognito_user is not None
            assert cognito_user["Enabled"]

    async def test_email_change_with_disabled_access(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        monkeypatch,
        add_client_service,
        update_client_service,
    ) -> None:
        # Given
        client_data = fakes.fake_client_data(
            email=fake.email(),
            client_status=ClientStatus.ActiveOngoing,
        )
        user = MagicMock(
            email=client_data["email"],
            cognito_id=None,
        )
        client_id = await add_client_service.add_client(**client_data)
        async with uow:
            await uow.users.add(user)
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.client_access(client_data=client_details, status=True)

        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.client_access(client_data=client_details, status=False)

        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            cognito_user = cognito_proxy.get_cognito_user(client_data["email"])
        assert cognito_user is not None
        assert not cognito_user["Enabled"]

        # When
        new_email = fake.email()
        user.email = new_email  # simulate database email change
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.update_client(
                client_id=client_id,
                email=new_email,
                client_status=ClientStatus.ActiveOngoing,
            )

        # Then
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            cognito_user = cognito_proxy.get_cognito_user(new_email)
            assert cognito_user is not None
            assert cognito_user.get("Enabled") is False

    async def test_should_delete_cognito_user_when_client_email_is_removed(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        monkeypatch,
        add_client_service,
        update_client_service,
    ) -> None:
        # Given
        client_data = fakes.fake_client_data(
            email=fake.email(),
            client_status=ClientStatus.ActiveOngoing,
        )
        user = MagicMock(
            email=client_data["email"],
            cognito_id=None,
        )
        client_id = await add_client_service.add_client(**client_data)
        async with uow:
            await uow.users.add(user)
        client_details = ClientDetailDTO(
            id=client_id,
            email=client_data["email"],
            client_status_id=client_data["client_status"],
            type="client",
            addresses=[],
            links=[],
        )
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.client_access(client_data=client_details, status=True)

        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            cognito_user = cognito_proxy.get_cognito_user(client_data["email"])
        assert cognito_user is not None
        assert cognito_user["Enabled"]

        # When
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.update_client(
                client_id=client_id,
                email=None,
            )

        # Then
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            cognito_user = cognito_proxy.get_cognito_user(client_data["email"])
            assert cognito_user is None

    async def test_should_not_update_cognito_pool_when_client_email_is_set(
        self,
        uow: FakeUnitOfWork,
        cognito_proxy,
        monkeypatch,
        add_client_service,
        update_client_service,
    ) -> None:
        # Given
        client_data = fakes.fake_client_data(
            email=None,
            client_status=ClientStatus.ActiveOngoing,
        )
        user = MagicMock(
            email=client_data["email"],
            cognito_id=None,
        )
        client_id = await add_client_service.add_client(**client_data)
        async with uow:
            await uow.users.add(user)

        # When
        new_email = fake.email()
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            await update_client_service.update_client(
                client_id=client_id,
                email=new_email,
            )

        # Then
        with patch("app.clients.services.client_access.raise_for_invalid_email"):
            cognito_user = cognito_proxy.get_cognito_user(new_email)
            assert cognito_user is None
