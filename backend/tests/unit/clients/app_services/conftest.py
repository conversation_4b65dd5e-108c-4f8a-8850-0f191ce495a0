from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from app.clients.services.add_client import AddClientService
from app.clients.services.client_access import ClientAccessService
from app.clients.services.update_client import UpdateClientService
from database import models
from gateway_accessors.cognito.accessor import DevCognitoGateway
from tests.unit.services.fake_repo_and_uow import FakeUnitOfWork


@pytest.fixture
async def uow():
    uow = FakeUnitOfWork()

    async with uow:
        await uow.personal_titles.add(models.PersonalTitleModel(name="TITLE"))

    return uow


@pytest.fixture
async def client_access_service(
    uow: FakeUnitOfWork, cognito_proxy: DevCognitoGateway
) -> ClientAccessService:
    return ClientAccessService(
        uow=uow,
        cognito=cognito_proxy,
        logger=Mock(),
        sync_cognito_command=AsyncMock(),
        mailer=AsyncMock(),
    )


@pytest.fixture
def add_client_service(uow: FakeUnitOfWork) -> AddClientService:
    return AddClientService(uow=uow, logger=Mock())


@pytest.fixture
def update_client_service(
    uow: FakeUnitOfWork,
    client_access_service: ClientAccessService,
) -> UpdateClientService:
    return UpdateClientService(
        uow=uow,
        logger=Mock(),
        client_access=client_access_service,
    )
