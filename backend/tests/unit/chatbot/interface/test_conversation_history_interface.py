import uuid
from datetime import datetime
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>
from uuid import uuid4

import pytest
from pydantic_ai.messages import (
    ModelRequest,
    ModelResponse,
    SystemPromptPart,
    TextPart,
    ToolCallPart,
    UserPromptPart,
)

from app.chatbot.dtos.conversation import Conversation
from app.chatbot.dtos.responses import AssistantTextDTO, OutboundDTO, UserTextDTO
from app.chatbot.exceptions import (
    EmptyMessageHistoryException,
    NoConversationFoundException,
)
from app.chatbot.interface.conversation_history_interface import (
    ConversationHistoryStore,
    ConversationStateManager,
    RedisConversationStore,
    S3ConversationStore,
)
from app.chatbot.services.exceptions import ConversationStateError
from gateway_accessors.aws.s3.accessor import S3Gateway
from gateway_accessors.aws.s3.exceptions import S3Exception
from gateway_accessors.redis.accessor import RedisGateway


@pytest.fixture
def s3_gateway():
    return AsyncMock(spec=S3Gateway)


@pytest.fixture
def redis_gateway():
    return AsyncMock(spec=RedisGateway)


@pytest.fixture
def s3_conversation_store(s3_gateway) -> S3ConversationStore:
    return S3ConversationStore(s3_gateway)


@pytest.fixture
def redis_conversation_store(redis_gateway) -> RedisConversationStore:
    return RedisConversationStore(redis_gateway)


@pytest.fixture
def conversation_history_store() -> ConversationHistoryStore:
    return ConversationHistoryStore(
        logger=Mock(),
        cache=AsyncMock(spec=RedisConversationStore),
        storage=AsyncMock(spec=S3ConversationStore),
    )


@pytest.fixture(scope="session")
def user_id() -> int:
    return 12345


@pytest.fixture(scope="session")
def conversation_id() -> str:
    return f"{datetime.now().isoformat()}_{str(uuid4())}"


@pytest.fixture
def conversation(conversation_id: str) -> Conversation:
    return Conversation(
        id=conversation_id,
        created_on=datetime.now(),
        message_history=[],
    )


class TestRedisConversationStore:
    async def test__get_latest_id(
        self,
        monkeypatch,
        redis_conversation_store: RedisConversationStore,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
        conversation_id: str,
    ):
        async def gets_id(key):
            return (
                conversation_id
                if key == conversation_history_store._create_latest_conversation_id_key(user_id)
                else None
            )

        monkeypatch.setattr(redis_conversation_store.redis, "get", gets_id)

        id = await redis_conversation_store.get_latest_id(
            conversation_history_store._create_latest_conversation_id_key(user_id)
        )
        assert id is not None

        id = await redis_conversation_store.get_latest_id("incorrect key")
        assert id is None

    async def test__set_latest_id(
        self, redis_gateway: RedisGateway, redis_conversation_store: RedisConversationStore
    ):
        await redis_conversation_store.set_latest_id("key", "id")
        redis_gateway.set.assert_awaited_once()

    async def test_get(
        self,
        monkeypatch,
        redis_conversation_store: RedisConversationStore,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
        conversation_id: str,
        conversation: Conversation,
    ):
        conversation_key = conversation_history_store._create_conversation_key(
            user_id, conversation_id
        )

        async def get(key):
            return conversation.model_dump_json() if key == conversation_key else None

        monkeypatch.setattr(redis_conversation_store.redis, "get", get)

        conv = await redis_conversation_store.get(conversation_key)
        assert isinstance(conv, Conversation)

        conv = await redis_conversation_store.get("incorrect key")
        assert conv is None

    async def test_set(
        self,
        redis_gateway: RedisGateway,
        redis_conversation_store: RedisConversationStore,
        conversation: Conversation,
    ):
        await redis_conversation_store.set("key", conversation)
        redis_gateway.set.assert_awaited_once()

    async def test_delete(
        self, redis_gateway: RedisGateway, redis_conversation_store: RedisConversationStore
    ):
        await redis_conversation_store.delete("key")
        redis_gateway.delete.assert_awaited_once()


class TestS3ConversationStore:
    async def test__get_latest_id(
        self,
        monkeypatch,
        s3_conversation_store: S3ConversationStore,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
        conversation_id: str,
    ):
        id_key = conversation_history_store._create_latest_conversation_id_key(user_id)

        def gets_id(key):
            return conversation_id.encode() if key == id_key else b""

        monkeypatch.setattr(s3_conversation_store.s3, "download_bytes", gets_id)

        id = await s3_conversation_store.get_latest_id(id_key)
        assert id is not None

        id = await s3_conversation_store.get_latest_id("incorrect key")
        assert id is None

        s3_conversation_store.s3.download_bytes = Mock(side_effect=S3Exception())

        id = await s3_conversation_store.get_latest_id(id_key)
        assert id is None

    async def test__set_latest_id(
        self, s3_gateway: S3Gateway, s3_conversation_store: S3ConversationStore
    ):
        await s3_conversation_store.set_latest_id("key", "id")
        s3_gateway.upload_bytes.assert_called_once()

    async def test_get(
        self,
        monkeypatch,
        s3_conversation_store: S3ConversationStore,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
        conversation_id: str,
        conversation: Conversation,
    ):
        conversation_key = conversation_history_store._create_conversation_key(
            user_id, conversation_id
        )

        def get(key):
            return conversation.model_dump_json().encode() if key == conversation_key else b""

        monkeypatch.setattr(s3_conversation_store.s3, "download_bytes", get)

        conv = await s3_conversation_store.get(conversation_key)
        assert isinstance(conv, Conversation)

        conv = await s3_conversation_store.get("incorrect key")
        assert conv is None

        s3_conversation_store.s3.download_bytes = Mock(side_effect=S3Exception())

        conv = await s3_conversation_store.get(conversation_key)
        assert conv is None

    async def test_set(
        self,
        s3_gateway: S3Gateway,
        s3_conversation_store: S3ConversationStore,
        conversation: Conversation,
    ):
        await s3_conversation_store.set("key", conversation)
        s3_gateway.upload_bytes.assert_called_once()

    async def test_delete(self, s3_gateway: S3Gateway, s3_conversation_store: S3ConversationStore):
        await s3_conversation_store.delete("key")
        s3_gateway.delete_file.assert_called_once()


class TestCreateIds:
    def test__create_latest_conversation_id_key(
        self, conversation_history_store: ConversationHistoryStore, user_id: int
    ):
        key = conversation_history_store._create_latest_conversation_id_key(user_id)

        assert key == "chatbot/user:12345/conversations/conversation:latest.txt"

    def test__create_conversation_key(
        self, conversation_history_store: ConversationHistoryStore, user_id: int
    ):
        key = conversation_history_store._create_conversation_key(user_id, "6789")

        assert key == "chatbot/user:12345/conversations/conversation:6789.json"


class TestGetLatestId:
    async def test_latest_id_none(
        self,
        conversation_history_store: ConversationHistoryStore,
    ):
        conversation_history_store.cache.get_latest_id = AsyncMock(return_value=None)
        conversation_history_store.storage.get_latest_id = AsyncMock(return_value=None)
        conversation_history_store.cache.set_latest_id = AsyncMock()

        id = await conversation_history_store._get_latest_id("key")
        assert id is None
        conversation_history_store.cache.get_latest_id.assert_awaited_once()
        conversation_history_store.storage.get_latest_id.assert_awaited_once()
        conversation_history_store.cache.set_latest_id.assert_not_awaited()

    async def test_latest_id_in_cache(
        self,
        conversation_history_store: ConversationHistoryStore,
        conversation_id: str,
    ):
        conversation_history_store.cache.get_latest_id = AsyncMock(return_value=conversation_id)
        conversation_history_store.storage.get_latest_id = AsyncMock(return_value=None)
        conversation_history_store.cache.set_latest_id = AsyncMock()

        id = await conversation_history_store._get_latest_id("key")

        assert id == conversation_id
        conversation_history_store.cache.get_latest_id.assert_awaited_once()
        conversation_history_store.storage.get_latest_id.assert_not_awaited()
        conversation_history_store.cache.set_latest_id.assert_not_awaited()

    async def test_latest_id_in_storage(
        self,
        conversation_history_store: ConversationHistoryStore,
        conversation_id: str,
    ):
        conversation_history_store.cache.get_latest_id = AsyncMock(return_value=None)
        conversation_history_store.storage.get_latest_id = AsyncMock(return_value=conversation_id)
        conversation_history_store.cache.set_latest_id = AsyncMock()

        id = await conversation_history_store._get_latest_id("key")

        assert id == conversation_id
        conversation_history_store.cache.get_latest_id.assert_awaited_once()
        conversation_history_store.storage.get_latest_id.assert_awaited_once()
        conversation_history_store.cache.set_latest_id.assert_called_once_with(
            "key", conversation_id
        )


class TestGet:
    async def test_get_none(
        self,
        conversation_history_store: ConversationHistoryStore,
    ):
        conversation_history_store.cache.get = AsyncMock(return_value=None)
        conversation_history_store.storage.get = AsyncMock(return_value=None)
        conversation_history_store.cache.set = AsyncMock()

        conversation = await conversation_history_store.get("key")
        assert conversation is None
        conversation_history_store.cache.get.assert_awaited_once()
        conversation_history_store.storage.get.assert_awaited_once()
        conversation_history_store.cache.set.assert_not_awaited()

    async def test_conversation_in_cache(
        self,
        conversation_history_store: ConversationHistoryStore,
        conversation: Conversation,
    ):
        conversation_history_store.cache.get = AsyncMock(return_value=conversation)
        conversation_history_store.storage.get = AsyncMock(return_value=None)
        conversation_history_store.cache.set = AsyncMock()

        conv = await conversation_history_store.get("key")

        assert isinstance(conv, Conversation)
        conversation_history_store.cache.get.assert_awaited_once()
        conversation_history_store.storage.get.assert_not_awaited()
        conversation_history_store.cache.set.assert_not_awaited()

    async def test_conversation_in_storage(
        self,
        conversation_history_store: ConversationHistoryStore,
        conversation: Conversation,
    ):
        conversation_history_store.cache.get = AsyncMock(return_value=None)
        conversation_history_store.storage.get = AsyncMock(return_value=conversation)
        conversation_history_store.cache.set = AsyncMock()

        conv = await conversation_history_store.get("key")

        assert isinstance(conv, Conversation)
        conversation_history_store.cache.get.assert_awaited_once()
        conversation_history_store.storage.get.assert_awaited_once()
        conversation_history_store.cache.set.assert_called_once_with("key", conversation)


class TestCreateNewConversation:
    async def test_create_new_conversation(
        self,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
    ):
        conversation_history_store.set_latest_conversation = AsyncMock()

        conv = await conversation_history_store.create_new_conversation(user_id)

        conversation_history_store.set_latest_conversation.assert_awaited_once_with(user_id, conv)

        parts = conv.id.split("_")
        assert datetime.fromisoformat(parts[0])
        assert uuid.UUID(parts[1], version=4)

        assert isinstance(conv.message_history[0].parts[0], SystemPromptPart)
        assert "identity" in conv.message_history[0].parts[0].content
        assert "company_information" in conv.message_history[0].parts[0].content
        assert isinstance(conv.message_history[1].parts[0], TextPart)


async def test_set_latest_conversation(
    conversation_history_store: ConversationHistoryStore, user_id: int, conversation: Conversation
):
    await conversation_history_store.set_latest_conversation(user_id, conversation)


class TestGetLatestConversation:
    async def test_latest_id_none(
        self,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
    ):
        conversation_history_store._get_latest_id = AsyncMock(return_value=None)
        conversation_history_store._create_conversation_key = AsyncMock()
        conversation_history_store.get = AsyncMock()

        conv = await conversation_history_store.get_latest_conversation(user_id)

        assert conv is None
        conversation_history_store._create_conversation_key.assert_not_called()
        conversation_history_store.get.assert_not_awaited()

    async def test_latest_conversation_none(
        self,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
        conversation_id: str,
    ):
        conversation_history_store._get_latest_id = AsyncMock(return_value=conversation_id)
        conversation_history_store.get = AsyncMock(return_value=None)

        conv = await conversation_history_store.get_latest_conversation(user_id)

        assert conv is None
        conversation_history_store._get_latest_id.assert_awaited_once()
        conversation_history_store.get.assert_awaited_once()

    async def test_latest_conversation_retrieved(
        self,
        conversation_history_store: ConversationHistoryStore,
        user_id: int,
        conversation: Conversation,
        conversation_id: str,
    ):
        conversation_history_store._get_latest_id = AsyncMock(return_value=conversation_id)
        conversation_history_store.get = AsyncMock(return_value=conversation)

        conv = await conversation_history_store.get_latest_conversation(user_id)

        assert isinstance(conv, Conversation)
        conversation_history_store._get_latest_id.assert_awaited_once()
        conversation_history_store.get.assert_awaited_once()


class TestFormatConversationUserFacing:
    async def test_no_message_history(
        self,
        user_id,
        conversation_history_store: ConversationHistoryStore,
    ):
        with pytest.raises(EmptyMessageHistoryException):
            await conversation_history_store.format_conversation_user_facing(
                user_id, Conversation(id="", created_on=datetime.now(), message_history=[])
            )

    async def test_format_successful(
        self,
        user_id,
        conversation_history_store: ConversationHistoryStore,
    ):
        conv = await conversation_history_store.format_conversation_user_facing(
            user_id,
            Conversation(
                id="",
                created_on=datetime.now(),
                message_history=[
                    ModelRequest(
                        parts=[
                            SystemPromptPart(
                                content="sys prompt",
                            ),
                            UserPromptPart(
                                content="hi",
                            ),
                        ]
                    ),
                    ModelResponse(
                        parts=[
                            TextPart(
                                content="hi",
                            ),
                            ToolCallPart(
                                tool_name="name",
                                args="args",
                            ),
                        ]
                    ),
                ],
            ),
        )

        assert len(conv) == 2
        assert isinstance(conv[0], UserTextDTO)
        assert isinstance(conv[1], AssistantTextDTO)


async def test_set_conversation_to_storage(
    conversation_history_store: ConversationHistoryStore, user_id: int, conversation: Conversation
):
    conversation_history_store.storage.set = AsyncMock()

    await conversation_history_store.set_conversation_to_storage(user_id, conversation)

    conversation_history_store.storage.set.assert_awaited_once()


async def test_delete(conversation_history_store: ConversationHistoryStore):
    await conversation_history_store.delete("key")


async def test_set(
    conversation_history_store: ConversationHistoryStore, conversation: Conversation
):
    await conversation_history_store.set("key", conversation)


@pytest.fixture
def store():
    return MagicMock()


@pytest.fixture
def logger():
    return MagicMock()


@pytest.fixture
def manager(store, logger):
    return ConversationStateManager(store=store, user_id=123, logger=logger)


async def test_get_or_create_conversation_existing(manager, store):
    # store.get_latest_conversation returns an existing conversation
    existing = MagicMock(name="Conversation")
    store.get_latest_conversation = AsyncMock(return_value=existing)
    # create_new should not even be called
    store.create_new_conversation = AsyncMock()

    result = await manager.get_or_create_conversation()

    assert result is existing
    assert manager.conversation is existing
    store.get_latest_conversation.assert_awaited_once_with(123)
    store.create_new_conversation.assert_not_called()


async def test_get_or_create_conversation_creates_new(manager, store):
    # no existing conversation → create a new one
    store.get_latest_conversation = AsyncMock(return_value=None)
    created = MagicMock(name="NewConversation")
    store.create_new_conversation = AsyncMock(return_value=created)

    result = await manager.get_or_create_conversation()

    assert result is created
    assert manager.conversation is created
    store.get_latest_conversation.assert_awaited_once_with(123)
    store.create_new_conversation.assert_awaited_once_with(123)


async def test_get_or_create_conversation_propagates_error(manager, store, logger):
    # get_latest_conversation throws → log & wrap
    store.get_latest_conversation = AsyncMock(side_effect=Exception("db down"))

    with pytest.raises(ConversationStateError) as excinfo:
        await manager.get_or_create_conversation()

    # logger.error called with context
    logger.error.assert_called_once()
    err_msg = logger.error.call_args[0][0]
    assert "Failed to get or create conversation" in err_msg
    assert "db down" in err_msg

    # exception message wraps original
    assert "Conversation management failed: db down" in str(excinfo.value)


async def test_save_conversation_without_active(manager):
    # manager.conversation is None by default
    with pytest.raises(ConversationStateError) as excinfo:
        await manager.save_conversation(["x"])
    assert "No active conversation to save" in str(excinfo.value)


async def test_save_conversation_success(manager, store):
    # set up an active conversation
    conv = MagicMock()
    conv.message_history = ["hello"]
    manager.conversation = conv

    store.set_latest_conversation = AsyncMock()

    new_msgs = ["how", "are", "you"]
    await manager.save_conversation(new_msgs)

    # history is extended in place
    assert conv.message_history == ["hello", "how", "are", "you"]
    store.set_latest_conversation.assert_awaited_once_with(123, conv)


async def test_save_conversation_store_error(manager, store, logger):
    # active conv exists
    conv = MagicMock()
    conv.message_history = []
    manager.conversation = conv

    # store.set_latest_conversation fails
    store.set_latest_conversation = AsyncMock(side_effect=Exception("write fail"))

    with pytest.raises(ConversationStateError) as excinfo:
        await manager.save_conversation(["msg"])

    logger.error.assert_called_once()
    err_msg = logger.error.call_args[0][0]
    assert "Failed to save conversation" in err_msg
    assert "write fail" in str(excinfo.value)


async def test_format_for_agent_without_active(user_id, manager):
    # conversation is still None
    with pytest.raises(ConversationStateError) as excinfo:
        await manager.format_for_agent(user_id)
    assert "No active conversation to format" in str(excinfo.value)


async def test_format_for_agent_success(user_id, manager, store):
    conv = MagicMock(name="Conversation")
    manager.conversation = conv

    formatted = [MagicMock(), MagicMock()]
    store.format_conversation_agent_facing = AsyncMock(return_value=formatted)

    result = await manager.format_for_agent(user_id)
    assert result is formatted
    store.format_conversation_agent_facing.assert_awaited_once_with(user_id, conv)


dt = datetime.now()

test_format_conversation_agent_facing_test_cases = [
    pytest.param(
        Conversation(
            id="",
            created_on=dt,
            message_history=[
                ModelRequest(parts=[SystemPromptPart(content="one", timestamp=dt)]),
                OutboundDTO(data={"A": "A"}),
                ModelRequest(parts=[UserPromptPart(content="two", timestamp=dt)]),
                OutboundDTO(data=[{"B": "B"}]),
                ModelResponse(parts=[TextPart(content="three")], timestamp=dt),
                OutboundDTO(data={"C": "C"}),
                ModelRequest(parts=[UserPromptPart(content="four", timestamp=dt)]),
                OutboundDTO(data=[{"D": "D"}]),
                ModelResponse(parts=[TextPart(content="five")], timestamp=dt),
            ],
        ),
        [
            ModelRequest(parts=[SystemPromptPart(content="one", timestamp=dt)]),
            ModelRequest(parts=[UserPromptPart(content="two", timestamp=dt)]),
            ModelResponse(parts=[TextPart(content="three")], timestamp=dt),
            ModelRequest(parts=[UserPromptPart(content="four", timestamp=dt)]),
            ModelResponse(parts=[TextPart(content="five")], timestamp=dt),
        ],
        None,
        id="success",
    ),
    pytest.param(
        Conversation(
            id="",
            created_on=dt,
            message_history=[],
        ),
        None,
        EmptyMessageHistoryException,
        id="failure, empty message history",
    ),
]


@pytest.mark.parametrize(
    "input_conversation, expected_output, expected_error",
    test_format_conversation_agent_facing_test_cases,
)
async def test_format_conversation_agent_facing(
    user_id, conversation_history_store, input_conversation, expected_output, expected_error
):
    if expected_error is not None:
        with pytest.raises(expected_error):
            agent_facing_conversation = (
                await conversation_history_store.format_conversation_agent_facing(
                    user_id, input_conversation
                )
            )
    else:
        agent_facing_conversation = (
            await conversation_history_store.format_conversation_agent_facing(
                user_id, input_conversation
            )
        )

        assert agent_facing_conversation == expected_output


test_overwrite_message_in_conversation_test_cases = [
    pytest.param(
        Conversation(
            id="1",
            created_on=dt,
            message_history=[
                OutboundDTO(
                    id=9,
                    timestamp=dt,
                    available_fields=None,
                    data_layout="data_layout",
                    data={"one": "1"},
                ),
                ModelRequest(parts=[SystemPromptPart(content="", timestamp=dt)]),
                OutboundDTO(
                    id=9,
                    timestamp=dt,
                    available_fields=None,
                    data_layout="data_layout",
                    data={"one": "1"},
                ),
                ModelResponse(parts=[TextPart(content="")], timestamp=dt),
                OutboundDTO(
                    id=5,
                    timestamp=dt,
                    available_fields=None,
                    data_layout="data_layout",
                    data={"one": "1"},
                ),
            ],
        ),
        9,
        True,
        {"two": "2"},
        Conversation(
            id="1",
            created_on=dt,
            message_history=[
                OutboundDTO(
                    id=9,
                    timestamp=dt,
                    available_fields=None,
                    data_layout="data_layout",
                    data={"one": "1"},
                ),
                ModelRequest(parts=[SystemPromptPart(content="", timestamp=dt)]),
                OutboundDTO(
                    id=9,
                    timestamp=dt,
                    available_fields=None,
                    completed=True,
                    data_layout="data_layout",
                    data={
                        "two": "2"
                    },  # the most recent OutboundDTO with a matching id should have it's data replaced with the new value.
                ),
                ModelResponse(parts=[TextPart(content="")], timestamp=dt),
                OutboundDTO(
                    id=5,
                    timestamp=dt,
                    available_fields=None,
                    data_layout="data_layout",
                    data={"one": "1"},
                ),
            ],
        ),
        None,
        id="success, most recent OutboundDTO with matching id has its data overwritten",
    ),
    pytest.param(
        None,  # no existing convo
        9,
        False,
        {"two": "2"},
        None,
        NoConversationFoundException,
        id="failure, no existing convo",
    ),
]


@pytest.mark.parametrize(
    "initial_data, input_message_id, input_completed_status, input_data, expected_output, expected_error",
    test_overwrite_message_in_conversation_test_cases,
)
async def test_overwrite_message_in_conversation(
    user_id,
    conversation_history_store: ConversationHistoryStore,
    initial_data,
    input_message_id,
    input_completed_status,
    input_data,
    expected_output,
    expected_error,
):
    conversation_history_store.get_latest_conversation = AsyncMock(return_value=initial_data)
    conversation_history_store.set_latest_conversation = AsyncMock()

    if expected_error is not None:
        with pytest.raises(expected_error):
            await conversation_history_store.overwrite_message_in_conversation(
                user_id, input_message_id, input_completed_status, input_data
            )
    else:
        await conversation_history_store.overwrite_message_in_conversation(
            user_id, input_message_id, input_completed_status, input_data
        )

        conversation_history_store.get_latest_conversation.assert_awaited_once_with(user_id)
        conversation_history_store.set_latest_conversation.assert_awaited_once_with(
            user_id, expected_output
        )
