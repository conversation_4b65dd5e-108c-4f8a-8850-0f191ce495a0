import pytest
from pydantic_ai import Agent

from app.chatbot.agents.models import AgentDependencies
from app.chatbot.agents.user_text_input.agent import create_text_input_agent


@pytest.fixture
async def text_input_agent(mock_llm_gateway) -> Agent[AgentDependencies, str]:
    """Fixture to create an instance of the text_input_agent."""
    return await create_text_input_agent(
        llm_gateway=mock_llm_gateway,
    )


class TestAgentCreation:
    """Tests for the agent creation process."""

    async def test_create_text_input_agent_returns_agent(self, text_input_agent):
        """Verify that the factory function returns a pydantic_ai.Agent instance."""
        assert isinstance(text_input_agent, Agent)

    async def test_agent_has_correct_number_of_tools(self, text_input_agent: Agent):
        """Verify that all tools are registered with the agent."""
        assert len(text_input_agent._function_toolset.tools) == 11

    async def test_agent_tools_are_correctly_named(self, text_input_agent: Agent):
        """Verify the names of the registered tools."""
        tool_names = text_input_agent._function_toolset.tools.keys()
        expected_names = [
            "get_aventur_company_data",
            "get_user_personal_info",
            "get_user_health_score",
            "update_user_personal_info",
            "post_user_addresses",
            "get_user_addresses",
            "put_user_addresses",
            "post_user_relations",
            "get_user_relations",
            "put_user_relations",
            "prompt_for_chatbot_consent_withdrawal",
        ]
        assert sorted(tool_names) == sorted(expected_names)
