import asyncio
from types import SimpleNamespace
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from pydantic_ai import models

from app.chatbot.agents.tools.helpers import AgentDependencies

models.ALLOW_MODEL_REQUESTS = False


@pytest.fixture
def mock_logger():
    return MagicMock()


@pytest.fixture(scope="session")
def user_id() -> int:
    return 12345


@pytest.fixture
def mock_yielded_client():
    mock_client = AsyncMock()
    mock_client.get = AsyncMock()
    return mock_client


@pytest.fixture
def mock_internal_client(mock_yielded_client):
    """Provides a mock internal client with a mock async context manager."""
    # The async context manager should return the mock client.
    async_context_manager = AsyncMock()
    async_context_manager.__aenter__.return_value = mock_yielded_client

    internal_client = MagicMock()
    internal_client.get_internal_client.return_value = async_context_manager
    return internal_client


@pytest.fixture
def mock_tool_output_queue():
    """Fixture for a mock asyncio.Queue."""
    queue = MagicMock(spec=asyncio.Queue)
    queue.put = AsyncMock()
    return queue


@pytest.fixture
def mock_llm_gateway():
    """Fixture for a mock BedrockGateway."""
    mock_gateway = MagicMock()
    mock_gateway.get_model = AsyncMock(return_value="test")
    return mock_gateway


@pytest.fixture
def jwt_token():
    return "jwt_token"


@pytest.fixture
def agent_dependencies(
    user_id,
    jwt_token,
    mock_logger,
    mock_internal_client,
    mock_tool_output_queue,
    mock_llm_gateway,
) -> AgentDependencies:
    return AgentDependencies(
        logger=mock_logger,
        llm_gateway=mock_llm_gateway,
        user_id=user_id,
        jwt_token=jwt_token,
        internal_client=mock_internal_client,
        tool_output_queue=mock_tool_output_queue,
    )


@pytest.fixture
def mock_run_context(agent_dependencies):
    """Fixture for a mock RunContext."""
    ctx = MagicMock()
    ctx.deps = agent_dependencies
    return ctx


class FakeLogger:
    def __init__(self):
        self.errors = []
        self.debugs = []

    def error(self, msg: str):
        self.errors.append(msg)

    def debug(self, msg: str):
        self.debugs.append(msg)


class DummyIterableData:
    """Simulates AddressData or RelationData with iterable (field, value) pairs and attributes."""

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    def __iter__(self):
        return iter(self.__dict__.items())

    def model_dump(self, mode=None):
        # For post_* functions
        return self.__dict__.copy()


@pytest_asyncio.fixture
async def fake_ctx(user_id, mock_internal_client):
    logger = FakeLogger()
    queue: asyncio.Queue = asyncio.Queue()
    deps = SimpleNamespace(
        logger=logger,
        user_id=user_id,
        internal_client=mock_internal_client,  # Will be unused when we monkeypatch get_data_with_readable_result
        jwt_token="jwt_token",
        tool_output_queue=queue,
    )
    return SimpleNamespace(deps=deps)
