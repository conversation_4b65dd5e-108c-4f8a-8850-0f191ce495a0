from typing import Any
from unittest.mock import AsyncMock, MagicMock, patch

import httpx
import pytest
from pydantic_ai import Run<PERSON>ontext
from pydantic_ai.models.test import TestModel
from pydantic_ai.usage import Usage

from app.chatbot.agents.tools.helpers import (
    get_data,
    get_data_with_readable_result,
)
from app.chatbot.constants import (
    OperationResultText,
)
from app.chatbot.dtos.responses import OutboundDTO


@pytest.fixture
def run_context(agent_dependencies):
    return RunContext(deps=agent_dependencies, model=TestModel(), usage=Usage(), prompt=None)


async def test_get_data_success(run_context, mock_internal_client, agent_dependencies):
    """
    Tests the successful execution of get_data.
    It verifies that the internal client is called with the correct URL and parameters.
    """
    # Arrange
    endpoint = "/operations/test-endpoint"
    params = {"param1": "value1"}
    expected_response = httpx.Response(200, json={"data": "success"})

    # Configure the mock to return a specific response
    mock_client = mock_internal_client.get_internal_client().__aenter__.return_value
    mock_client.get.return_value = expected_response

    # Act
    response = await get_data(run_context, endpoint, params)

    # Assert
    assert response == expected_response
    mock_client.get.assert_called_once_with(
        f"/api/v2{endpoint}",
        params={"param1": "value1", "client_id": 12345},
        headers={"Authorization": agent_dependencies.jwt_token},
    )


async def test_get_data_http_error(run_context, mock_internal_client):
    """
    Tests how get_data handles an httpx exception.
    It ensures that the function raises the exception as expected.
    """
    # Arrange
    endpoint = "error-endpoint"
    params = {}

    # Configure the mock to raise an exception
    mock_client = mock_internal_client.get_internal_client().__aenter__.return_value
    mock_client.get.side_effect = httpx.RequestError("Network error")

    # Act & Assert
    with pytest.raises(httpx.RequestError):
        await get_data(run_context, endpoint, params)


async def test_user_id_added_to_params(run_context, mock_internal_client):
    """
    Verifies that the user_id from the context is correctly added to the request parameters.
    """
    # Arrange
    endpoint = "user-id-test"
    initial_params = {"existing_param": "value"}
    user_id = run_context.deps.user_id

    # Configure the mock
    mock_client = mock_internal_client.get_internal_client().__aenter__.return_value
    mock_client.get.return_value = httpx.Response(200, json={})

    # Act
    await get_data(run_context, endpoint, initial_params)

    # Assert
    mock_client.get.assert_called_once()
    called_args, called_kwargs = mock_client.get.call_args
    assert "params" in called_kwargs
    assert called_kwargs["params"]["client_id"] == user_id
    assert called_kwargs["params"]["existing_param"] == "value"


test_cases = [
    pytest.param(
        {"initial_param": "value"},
        ["first_name", "last_name"],
        {"initial_param": "value", "fields": ["first_name", "last_name"]},
        200,
        {"first_name": "John", "last_name": "Doe"},
        OperationResultText.SUCCESS,
        OutboundDTO(
            available_fields=["first_name", "last_name"],
            data={"first_name": "John", "last_name": "Doe"},
        ),
        id="success_with_requested_fields",
    ),
    pytest.param(
        {"initial_param": "value"},
        None,
        {"initial_param": "value"},
        200,
        {"data": "some payload"},
        OperationResultText.SUCCESS,
        OutboundDTO(
            available_fields=None,
            data={"data": "some payload"},
        ),
        id="success_no_requested_fields",
    ),
    pytest.param(
        None,
        ["first_name", "last_name"],
        {"fields": ["first_name", "last_name"]},
        200,
        {"first_name": "John", "last_name": "Doe"},
        OperationResultText.SUCCESS,
        OutboundDTO(
            available_fields=["first_name", "last_name"],
            data={"first_name": "John", "last_name": "Doe"},
        ),
        id="success_no_initial_params",
    ),
    pytest.param(
        None,
        ["first_name", "last_name"],
        {"fields": ["first_name", "last_name"]},
        200,
        None,
        OperationResultText.NO_CONTENT,
        None,
        id="success_but_data_is_None",
    ),
    pytest.param(
        None,
        ["first_name", "last_name"],
        {"fields": ["first_name", "last_name"]},
        200,
        [],
        OperationResultText.NO_CONTENT,
        None,
        id="success_but_data_list_len_0",
    ),
    pytest.param(
        None,
        ["first_name", "last_name"],
        {"fields": ["first_name", "last_name"]},
        200,
        {},
        OperationResultText.NO_CONTENT,
        None,
        id="success_but_data_dict_empty",
    ),
    pytest.param(
        {"initial_param": "value"},
        ["first_name", "last_name"],
        {"initial_param": "value", "fields": ["first_name", "last_name"]},
        400,
        None,
        OperationResultText.SERVER_ERROR,
        None,
        id="failure_400_bad_request",
    ),
    pytest.param(
        {"initial_param": "value"},
        None,
        {"initial_param": "value"},
        500,
        None,
        OperationResultText.SERVER_ERROR,
        None,
        id="failure_500_server_error",
    ),
    pytest.param(
        {"initial_param": "value"},
        None,
        {"initial_param": "value"},
        200,
        {"data": "some payload"},
        OperationResultText.SUCCESS,
        OutboundDTO(
            available_fields=None,
            data={"data": "some payload"},
        ),
        id="success_with_empty_requested_fields",
    ),
]


@pytest.mark.parametrize(
    "input_params, input_requested_fields, expected_merged_params_for_http_get, dummy_status_code, dummy_response_json, expected_result_text, expected_outbound_schema",
    test_cases,
)
@patch("app.chatbot.agents.tools.helpers.get_data", new_callable=AsyncMock)
async def test_get_data_with_readable_result(
    mock_get_data,
    run_context,
    input_params: dict[str, Any] | None,
    input_requested_fields: list[str] | None,
    expected_merged_params_for_http_get,
    dummy_status_code: int,
    dummy_response_json,
    expected_result_text: OperationResultText,
    expected_outbound_schema: OutboundDTO,
):
    # Arrange 🧪
    # Mock the response from the inner get_data call
    mock_response = MagicMock(spec=httpx.Response)
    mock_response.status_code = dummy_status_code
    mock_response.json.return_value = dummy_response_json
    mock_get_data.return_value = mock_response

    # Act 🚀
    result_text, model = await get_data_with_readable_result(
        ctx=run_context,
        endpoint="/operations/test-endpoint",
        params=input_params,
        requested_fields=input_requested_fields,
    )

    # Assert 🔎
    # Verify that get_data was called correctly
    mock_get_data.assert_called_once_with(
        run_context, "/operations/test-endpoint", expected_merged_params_for_http_get
    )

    # Verify the returned readable text
    assert result_text == expected_result_text

    # Verify the returned model based on the outcome
    if expected_result_text == OperationResultText.SUCCESS:
        assert model is not None
        assert model == expected_outbound_schema
    else:
        assert model is None
