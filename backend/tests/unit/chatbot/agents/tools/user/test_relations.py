import pytest

from api.enums.enums import Relationship
from app.chatbot.agents.tools.user import relations
from app.chatbot.agents.tools.user.relations import (
    get_user_relations,
    post_user_relations,
    put_user_relations,
)
from app.chatbot.agents.user_text_input.input_schemas import (
    RelationData,
    RelationFilter,
)
from app.chatbot.constants import OperationGroup, OperationResultText
from app.chatbot.dtos.responses import OutboundDTO

post_user_relations_test_cases = [
    pytest.param(
        [
            RelationData(),
        ],
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[RelationData().model_dump(mode="json")],
        ),
        id="success, one empty address",
    ),
    pytest.param(
        [
            RelationData(),
            RelationData(),
        ],
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[RelationData().model_dump(mode="json"), RelationData().model_dump(mode="json")],
        ),
        id="success, multiple empty addresses",
    ),
    pytest.param(
        [
            RelationData(first_name="<PERSON>"),
            RelationData(last_name="Doe"),
        ],
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[
                RelationData(first_name="John").model_dump(mode="json"),
                RelationData(last_name="Doe").model_dump(mode="json"),
            ],
        ),
        id="success, multiple partial addresses",
    ),
]


@pytest.mark.parametrize(
    "input_relations, expected_result_text, expected_outbound_schema",
    post_user_relations_test_cases,
)
async def test_post_user_relations(
    monkeypatch,
    fake_ctx,
    input_relations,
    expected_result_text,
    expected_outbound_schema,
):
    result_text = await post_user_relations(fake_ctx, input_relations)

    assert result_text == expected_result_text.value
    assert fake_ctx.deps.tool_output_queue.empty() is False
    queued = await fake_ctx.deps.tool_output_queue.get()
    assert queued == expected_outbound_schema


get_user_relations_test_cases = [
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[{"id": 1}],
        ),
        RelationFilter(
            relationship_type=None,
        ),
        {},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[{"id": 1}],
        ),
        id="success, without filter",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[{"id": 1, "relationship_type": "Spouse"}],
        ),
        RelationFilter(
            relationship_type=Relationship.Spouse,
        ),
        {"relationship_type": "Spouse"},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[{"id": 1, "relationship_type": "Spouse"}],
        ),
        id="success, with filter",
    ),
    pytest.param(
        None,
        RelationFilter(relationship_type=None),
        {},
        OperationResultText.SERVER_ERROR,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[{"id": 1}],
        ),
        id="fail, data is None",
    ),
]


@pytest.mark.parametrize(
    "seed_data, input_filter, expected_params, expected_result_text, expected_outbound_schema",
    get_user_relations_test_cases,
)
async def test_get_user_relations(
    monkeypatch,
    fake_ctx,
    seed_data: OutboundDTO | None,
    input_filter: RelationFilter,
    expected_params: dict,
    expected_result_text: OperationResultText,
    expected_outbound_schema: OutboundDTO | None,
):
    async def fake_get_data_with_readable_result(ctx, endpoint, params):
        assert endpoint == f"/operations/client/{ctx.deps.user_id}/relations"
        assert params == expected_params
        return expected_result_text, seed_data

    monkeypatch.setattr(
        relations, "get_data_with_readable_result", fake_get_data_with_readable_result
    )

    result = await get_user_relations(fake_ctx, filter_config=input_filter)

    assert result.__contains__(expected_result_text.value)

    if seed_data is not None:
        assert fake_ctx.deps.tool_output_queue.empty() is False
        queued = await fake_ctx.deps.tool_output_queue.get()
        assert queued == expected_outbound_schema
        assert queued.data_layout == f"{OperationGroup.User_Relations.value}_layout"
    else:
        assert fake_ctx.deps.tool_output_queue.empty() is True


put_user_relations_test_cases = [
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                }
            ],
        ),
        RelationFilter(
            relationship_type=None,
        ),
        None,
        {},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                }
            ],
        ),
        id="success, one relation without new_values",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": None,
                    "relationship_type": "Spouse",
                }
            ],
        ),
        RelationFilter(
            relationship_type=None,
        ),
        [RelationData(last_name="Doe")],
        {},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                }
            ],
        ),
        id="success, one relation with new_values",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                }
            ],
        ),
        RelationFilter(
            relationship_type=None,
        ),
        [RelationData(first_name="Steve")],
        {},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "Steve",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                },
            ],
        ),
        id="success, one relation with new values overwrite old",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                },
                {
                    "id": 2,
                    "first_name": "Jane",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                },
            ],
        ),
        RelationFilter(
            relationship_type=None,
        ),
        [
            RelationData(first_name="Steve"),
        ],
        {},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                },
                {
                    "id": 2,
                    "first_name": "Jane",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                },
            ],
        ),
        id="success, if multiple relations no new values applied",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                },
            ],
        ),
        RelationFilter(
            relationship_type=None,
        ),
        [
            RelationData(first_name="Steve"),
            RelationData(last_name="Doe"),
        ],
        {},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                },
            ],
        ),
        id="success, if multiple new values objects no new values applied",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "John",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                }
            ],
        ),
        RelationFilter(
            relationship_type=Relationship.Spouse,
        ),
        [RelationData(first_name="Steve")],
        {"relationship_type": "Spouse"},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Relations.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "first_name": "Steve",
                    "last_name": "Doe",
                    "relationship_type": "Spouse",
                }
            ],
        ),
        id="success, filter processed",
    ),
    pytest.param(
        None,
        RelationFilter(
            relationship_type=None,
        ),
        None,
        {},
        OperationResultText.NO_CONTENT,
        None,
        id="fail, data is None",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data={
                "id": 1,
                "first_name": "John",
                "last_name": "Doe",
                "relationship_type": "Spouse",
            },
        ),
        RelationFilter(
            relationship_type=None,
        ),
        None,
        {},
        OperationResultText.SERVER_ERROR,
        None,
        id="fail, data is a dict",
    ),
]


@pytest.mark.parametrize(
    "seed_data, input_filter, input_new_values, expected_params, expected_result_text, expected_outbound_schema",
    put_user_relations_test_cases,
)
async def test_put_user_relations(
    monkeypatch,
    fake_ctx,
    seed_data: OutboundDTO | None,
    input_filter: RelationFilter,
    input_new_values: list[RelationData] | None,
    expected_params: dict,
    expected_result_text: OperationResultText,
    expected_outbound_schema: OutboundDTO | None,
):
    async def fake_get_data_with_readable_result(ctx, endpoint, params):
        assert endpoint == f"/operations/client/{ctx.deps.user_id}/relations"
        assert params == expected_params
        return expected_result_text, seed_data

    monkeypatch.setattr(
        relations, "get_data_with_readable_result", fake_get_data_with_readable_result
    )

    result = await put_user_relations(
        fake_ctx, filter_config=input_filter, new_values=input_new_values
    )

    if isinstance(expected_result_text, OperationResultText):
        assert result.__contains__(expected_result_text.value)

    if seed_data is not None and isinstance(seed_data.data, list):
        assert fake_ctx.deps.tool_output_queue.empty() is False
        queued = await fake_ctx.deps.tool_output_queue.get()
        assert queued == expected_outbound_schema
        assert queued.data_layout == f"{OperationGroup.User_Relations.value}_layout"
    else:
        assert fake_ctx.deps.tool_output_queue.empty() is True
