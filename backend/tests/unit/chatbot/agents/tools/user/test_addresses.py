from unittest.mock import patch

import pytest

from api.schemas.requests.client_requests import AddressData as AddressDataUsingIds
from app.chatbot.agents.tools.user import addresses
from app.chatbot.agents.tools.user.addresses import (
    convert_address_country_names_to_ids,
    convert_address_filter_country_name_to_id,
    get_country_lookup_data,
    get_user_addresses,
    post_user_addresses,
    put_user_addresses,
)
from app.chatbot.agents.user_text_input.input_schemas import (
    AddressData,
    AddressFilter,
    AddressFilterUsingIds,
)
from app.chatbot.constants import OperationGroup, OperationResultText
from app.chatbot.dtos.responses import OutboundDTO
from app.ref_data.ref_data_dtos import CountryDTO


async def test_get_country_lookup_data(mock_run_context):
    with patch("app.chatbot.agents.tools.user.addresses.get_ref_data") as mock_get_ref_data:
        mock_get_ref_data.return_value = {
            "countries": [
                {"id": 1, "name": "England", "iso_alpha2": "UK", "iso_alpha3": "UK1"},
                {"id": 2, "name": "France", "iso_alpha2": "FR", "iso_alpha3": "FR1"},
            ]
        }

        lookup_data = await get_country_lookup_data(mock_run_context)

        mock_get_ref_data.assert_called_once_with(mock_run_context)
        assert lookup_data == [
            CountryDTO(id=1, name="England", iso_alpha2="UK", iso_alpha3="UK1"),
            CountryDTO(id=2, name="France", iso_alpha2="FR", iso_alpha3="FR1"),
        ]


convert_address_country_names_to_ids_test_cases = [
    pytest.param(
        [
            AddressData(country_name="england"),
            AddressData(country_name="france"),
        ],
        [5, 2],
        [
            AddressDataUsingIds(country_id=5),
            AddressDataUsingIds(country_id=2),
        ],
        id="success",
    ),
    pytest.param(
        [
            AddressData(country_name="england"),
            AddressData(country_name="ambiguous country name"),
        ],
        [5, 0],  # ambiguous country id returned (0)
        [
            AddressDataUsingIds(country_id=5),
            AddressDataUsingIds(country_id=1),  # ambiguous result (0) changed to default (1)
        ],
        id="success, with ambiguous country so default is used",
    ),
    pytest.param(
        [
            AddressData(country_name="england"),
            AddressData(country_name="france"),
        ],
        None,  # lookup failed
        [
            AddressDataUsingIds(country_id=1),
            AddressDataUsingIds(country_id=1),
        ],
        id="failure, lookup returns None, so default country list returned",
    ),
    pytest.param(
        [
            AddressData(country_name="england"),
            AddressData(country_name="france"),
        ],
        [5],  # lookup returns wrong no. of ids
        [
            AddressDataUsingIds(country_id=1),
            AddressDataUsingIds(country_id=1),
        ],
        id="failure, lookup returns wrong no. of ids, so default country list returned",
    ),
]


@pytest.mark.parametrize(
    "addresses_input, looked_up_ids, expected_output",
    convert_address_country_names_to_ids_test_cases,
)
async def test_convert_address_country_names_to_ids(
    mock_run_context,
    addresses_input: list[AddressData],
    looked_up_ids,
    expected_output: list[AddressDataUsingIds],
):
    with (
        patch(
            "app.chatbot.agents.tools.user.addresses.get_country_lookup_data"
        ) as mock_get_country_lookup_data,
        patch("app.chatbot.agents.tools.user.addresses.perform_lookup") as mock_perform_lookup,
    ):
        mock_get_country_lookup_data.return_value = []
        mock_perform_lookup.return_value = looked_up_ids

        addresses = await convert_address_country_names_to_ids(mock_run_context, addresses_input)

        mock_get_country_lookup_data.assert_awaited_once_with(mock_run_context)
        mock_perform_lookup.assert_awaited_once_with(
            mock_run_context, "Country", [], [address.country_name for address in addresses_input]
        )
        assert addresses == expected_output


convert_address_filter_country_name_to_id_test_cases = [
    pytest.param(
        AddressFilter(
            country_name="england",
        ),  # type: ignore
        [5],
        AddressFilterUsingIds(
            address_line_one=None,
            address_line_two=None,
            address_line_three=None,
            address_line_four=None,
            city=None,
            post_code=None,
            country_id=5,
            moved_in_date_from=None,
            moved_in_date_to=None,
            moved_out_date_from=None,
            moved_out_date_to=None,
            is_primary=None,
            exact_match=False,
        ),
        id="success",
    ),
    pytest.param(
        AddressFilter(
            country_name="ambiguous",
        ),  # type: ignore
        [0],
        AddressFilterUsingIds(
            address_line_one=None,
            address_line_two=None,
            address_line_three=None,
            address_line_four=None,
            city=None,
            post_code=None,
            country_id=None,  # None
            moved_in_date_from=None,
            moved_in_date_to=None,
            moved_out_date_from=None,
            moved_out_date_to=None,
            is_primary=None,
            exact_match=False,
        ),
        id="success, with ambiguous country so country filter is None",
    ),
    pytest.param(
        AddressFilter(
            country_name="england",
        ),  # type: ignore
        None,
        AddressFilterUsingIds(
            address_line_one=None,
            address_line_two=None,
            address_line_three=None,
            address_line_four=None,
            city=None,
            post_code=None,
            country_id=None,  # None
            moved_in_date_from=None,
            moved_in_date_to=None,
            moved_out_date_from=None,
            moved_out_date_to=None,
            is_primary=None,
            exact_match=False,
        ),
        id="failure, lookup returns None, so filter set to None",
    ),
    pytest.param(
        AddressFilter(
            country_name="england",
        ),  # type: ignore
        [5, 8],
        AddressFilterUsingIds(
            address_line_one=None,
            address_line_two=None,
            address_line_three=None,
            address_line_four=None,
            city=None,
            post_code=None,
            country_id=None,  # None
            moved_in_date_from=None,
            moved_in_date_to=None,
            moved_out_date_from=None,
            moved_out_date_to=None,
            is_primary=None,
            exact_match=False,
        ),
        id="failure, lookup returns wrong no. of ids, so filter set to None",
    ),
]


@pytest.mark.parametrize(
    "filter_input, looked_up_ids, expected_output",
    convert_address_filter_country_name_to_id_test_cases,
)
async def test_convert_address_filter_country_name_to_id(
    mock_run_context,
    filter_input: AddressFilter,
    looked_up_ids,
    expected_output: AddressFilterUsingIds,
):
    with (
        patch(
            "app.chatbot.agents.tools.user.addresses.get_country_lookup_data"
        ) as mock_get_country_lookup_data,
        patch("app.chatbot.agents.tools.user.addresses.perform_lookup") as mock_perform_lookup,
    ):
        mock_get_country_lookup_data.return_value = []
        mock_perform_lookup.return_value = looked_up_ids

        addresses = await convert_address_filter_country_name_to_id(mock_run_context, filter_input)

        mock_get_country_lookup_data.assert_awaited_once_with(mock_run_context)
        mock_perform_lookup.assert_awaited_once_with(
            mock_run_context, "Country", [], [filter_input.country_name]
        )
        assert addresses == expected_output


post_user_addresses_test_cases = [
    pytest.param(
        [
            AddressData(),
        ],
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[AddressDataUsingIds().model_dump(mode="json")],
        ),
        id="success, one empty address",
    ),
    pytest.param(
        [
            AddressData(),
            AddressData(),
        ],
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                AddressDataUsingIds().model_dump(mode="json"),
                AddressDataUsingIds().model_dump(mode="json"),
            ],
        ),
        id="success, multiple empty addresses",
    ),
    pytest.param(
        [
            AddressData(address_line_one="line one", country_name="United Kingdom"),
            AddressData(post_code="N2OSE", country_name="France"),
        ],
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                AddressDataUsingIds(address_line_one="line one", country_id=1).model_dump(
                    mode="json"
                ),
                AddressDataUsingIds(post_code="N2OSE", country_id=2).model_dump(mode="json"),
            ],
        ),
        id="success, multiple partial addresses",
    ),
]


@pytest.mark.parametrize(
    "input_addresses, expected_result_text, expected_outbound_schema",
    post_user_addresses_test_cases,
)
async def test_post_user_addresses(
    fake_ctx,
    input_addresses: list[AddressData],
    expected_result_text,
    expected_outbound_schema,
):
    with patch(
        "app.chatbot.agents.tools.user.addresses.convert_address_country_names_to_ids"
    ) as convert:
        addresses_with_ids = []

        for address in input_addresses:
            dict = address.model_dump()
            dict.pop("country_name")
            if address.country_name == "United Kingdom":
                dict["country_id"] = 1
            elif address.country_name == "France":
                dict["country_id"] = 2
            addresses_with_ids.append(AddressDataUsingIds(**dict))

        convert.return_value = addresses_with_ids

        result_text = await post_user_addresses(fake_ctx, input_addresses)

        assert result_text == expected_result_text.value
        assert fake_ctx.deps.tool_output_queue.empty() is False
        queued = await fake_ctx.deps.tool_output_queue.get()
        assert queued == expected_outbound_schema


get_user_addresses_test_cases = [
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[{"id": 1}],
        ),
        AddressFilter(),  # type: ignore
        {"exact_match": False},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[{"id": 1}],
        ),
        id="success, without filter",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[{"id": 1, "address_line_two": "two", "country_id": 1}],
        ),
        AddressFilter(address_line_two="two", country_name="United Kingdom"),  # type: ignore
        {"address_line_two": "two", "country_id": 1, "exact_match": False},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[{"id": 1, "address_line_two": "two", "country_id": 1}],
        ),
        id="success, with filter",
    ),
    pytest.param(
        None,
        AddressFilter(),  # type: ignore
        {"exact_match": False},
        OperationResultText.SERVER_ERROR,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[{"id": 1}],
        ),
        id="fail, data is None",
    ),
]


@pytest.mark.parametrize(
    "seed_data, input_filter, expected_params, expected_result_text, expected_outbound_schema",
    get_user_addresses_test_cases,
)
async def test_get_user_addresses(
    monkeypatch,
    fake_ctx,
    seed_data: OutboundDTO | None,
    input_filter: AddressFilter,
    expected_params: dict,
    expected_result_text: OperationResultText,
    expected_outbound_schema: OutboundDTO | None,
):
    with patch(
        "app.chatbot.agents.tools.user.addresses.convert_address_filter_country_name_to_id"
    ) as convert:
        dict = input_filter.model_dump()
        dict.pop("country_name")
        if input_filter.country_name == "United Kingdom":
            dict["country_id"] = 1
        elif input_filter.country_name == "France":
            dict["country_id"] = 2
        else:
            dict["country_id"] = None
        convert.return_value = AddressFilterUsingIds(**dict)

        async def fake_get_data_with_readable_result(ctx, endpoint, params):
            assert endpoint == f"/operations/client/{ctx.deps.user_id}/addresses"
            assert params == expected_params
            return expected_result_text, seed_data

        monkeypatch.setattr(
            addresses, "get_data_with_readable_result", fake_get_data_with_readable_result
        )

        result = await get_user_addresses(fake_ctx, filter_config=input_filter)

        assert result.__contains__(expected_result_text.value)

        if seed_data is not None:
            assert fake_ctx.deps.tool_output_queue.empty() is False
            queued = await fake_ctx.deps.tool_output_queue.get()
            assert queued == expected_outbound_schema
            assert queued.data_layout == f"{OperationGroup.User_Addresses.value}_layout"
        else:
            assert fake_ctx.deps.tool_output_queue.empty() is True


put_user_addresses_test_cases = [
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": None,
                    "address_line_three": None,
                }
            ],
        ),
        AddressFilter(),  # type: ignore
        None,
        {"exact_match": False},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": None,
                    "address_line_three": None,
                }
            ],
        ),
        id="success, one address without new_values",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": None,
                    "address_line_three": None,
                }
            ],
        ),
        AddressFilter(),  # type: ignore
        [AddressData(address_line_two="line two")],
        {"exact_match": False},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                }
            ],
        ),
        id="success, one address with new_values",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                }
            ],
        ),
        AddressFilter(),  # type: ignore
        [
            AddressData(address_line_one="line one again"),
        ],
        {"exact_match": False},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one again",
                    "address_line_two": "line two",
                    "address_line_three": None,
                },
            ],
        ),
        id="success, one address with new values overwrite old",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                },
                {
                    "id": 2,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                },
            ],
        ),
        AddressFilter(),  # type: ignore
        [
            AddressData(address_line_one="line one again"),
        ],
        {"exact_match": False},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                },
                {
                    "id": 2,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                },
            ],
        ),
        id="success, if multiple addresses no new values applied",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                },
            ],
        ),
        AddressFilter(),  # type: ignore
        [
            AddressData(address_line_one="line one again"),
            AddressData(address_line_two="line two again"),
        ],
        {"exact_match": False},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": "line two",
                    "address_line_three": None,
                },
            ],
        ),
        id="success, if multiple new values objects no new values applied",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": None,
                    "address_line_three": None,
                    "country_id": 1,
                }
            ],
        ),
        AddressFilter(
            address_line_two="thing",
            country_name="United Kingdom",
            exact_match=True,
            is_primary=True,
        ),  # type: ignore
        None,
        {"address_line_two": "thing", "country_id": 1, "exact_match": True, "is_primary": True},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": None,
                    "address_line_three": None,
                    "country_id": 1,
                }
            ],
        ),
        id="success, filter processed",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": None,
                    "address_line_three": None,
                    "country_id": 1,
                }
            ],
        ),
        AddressFilter(
            address_line_two="thing",
            country_name="United Kingdom",
            exact_match=True,
            is_primary=True,
        ),  # type: ignore
        [
            AddressData(country_name="France"),  # change country from UK (1) to France (2)
        ],
        {"address_line_two": "thing", "country_id": 1, "exact_match": True, "is_primary": True},
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_Addresses.value}_layout",
            available_fields=None,
            data=[
                {
                    "id": 1,
                    "address_line_one": "line one",
                    "address_line_two": None,
                    "address_line_three": None,
                    "country_id": 2,  # therefore expect output to have been changed to France (2)
                }
            ],
        ),
        id="success, filter processed and new value for country_name",
    ),
    pytest.param(
        None,
        AddressFilter(),  # type: ignore
        None,
        {"exact_match": False},
        OperationResultText.NO_CONTENT,
        None,
        id="fail, data is None",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=None,
            data={
                "id": 1,
                "address_line_one": "line one",
                "address_line_two": None,
                "address_line_three": None,
            },
        ),
        AddressFilter(),  # type: ignore
        None,
        {"exact_match": False},
        OperationResultText.SERVER_ERROR,
        None,
        id="fail, data is a dict",
    ),
]


@pytest.mark.parametrize(
    "seed_data, input_filter, input_new_values, expected_params, expected_result_text, expected_outbound_schema",
    put_user_addresses_test_cases,
)
async def test_put_user_addresses(
    monkeypatch,
    fake_ctx,
    seed_data: OutboundDTO | None,
    input_filter: AddressFilter,
    input_new_values: list[AddressData] | None,
    expected_params: dict,
    expected_result_text: OperationResultText,
    expected_outbound_schema: OutboundDTO | None,
):
    with (
        patch(
            "app.chatbot.agents.tools.user.addresses.convert_address_filter_country_name_to_id"
        ) as mock_convert_address_filter_country_name_to_id,
        patch(
            "app.chatbot.agents.tools.user.addresses.convert_address_country_names_to_ids"
        ) as mock_convert_address_country_names_to_ids,
    ):
        dict = input_filter.model_dump()
        dict.pop("country_name")
        if input_filter.country_name == "United Kingdom":
            dict["country_id"] = 1
        elif input_filter.country_name == "France":
            dict["country_id"] = 2
        else:
            dict["country_id"] = None
        mock_convert_address_filter_country_name_to_id.return_value = AddressFilterUsingIds(**dict)

        if input_new_values is not None:
            dict = input_new_values[0].model_dump()
            dict.pop("country_name")
            if input_new_values[0].country_name == "United Kingdom":
                dict["country_id"] = 1
            elif input_new_values[0].country_name == "France":
                dict["country_id"] = 2

            mock_convert_address_country_names_to_ids.return_value = [AddressDataUsingIds(**dict)]

        async def fake_get_data_with_readable_result(ctx, endpoint, params):
            assert endpoint == f"/operations/client/{ctx.deps.user_id}/addresses"
            assert params == expected_params
            return expected_result_text, seed_data

        monkeypatch.setattr(
            addresses, "get_data_with_readable_result", fake_get_data_with_readable_result
        )

        result = await put_user_addresses(
            fake_ctx, filter_config=input_filter, new_values=input_new_values
        )

        if isinstance(expected_result_text, OperationResultText):
            assert result.__contains__(expected_result_text.value)

        if seed_data is not None and isinstance(seed_data.data, list):
            assert fake_ctx.deps.tool_output_queue.empty() is False
            queued = await fake_ctx.deps.tool_output_queue.get()
            assert queued == expected_outbound_schema
            assert queued.data_layout == f"{OperationGroup.User_Addresses.value}_layout"
        else:
            assert fake_ctx.deps.tool_output_queue.empty() is True
