import pytest

from app.chatbot.agents.exceptions import (
    AgentFeedbackException,
    NoFieldsRequestedException,
)
from app.chatbot.agents.tools.user import personal_details
from app.chatbot.agents.tools.user.personal_details import (
    get_user_personal_info,
    update_user_personal_info,
)
from app.chatbot.agents.user_text_input.input_schemas import (
    FieldsRequested,
)
from app.chatbot.constants import OperationGroup, OperationResultText
from app.chatbot.dtos.responses import OutboundDTO
from app.chatbot_operations.dtos.operations_responses import User

get_user_personal_info_test_cases = [
    pytest.param(
        OutboundDTO(
            available_fields=["first_name"],
            data={"first_name": "<PERSON>"},
        ),
        FieldsRequested.User.PersonalDetails(
            first_name=True,
        ),
        OperationResultText.SUCCESS,
        id="success, data",
    ),
    pytest.param(
        None,
        FieldsRequested.User.PersonalDetails(
            first_name=True,
        ),
        OperationResultText.SERVER_ERROR,
        id="server error",
    ),
    pytest.param(
        None,
        FieldsRequested.User.PersonalDetails(),
        NoFieldsRequestedException,
        id="fail, no requested fields",
    ),
]


@pytest.mark.parametrize(
    "seed_data, input_requested_fields, expected_result_text", get_user_personal_info_test_cases
)
async def test_get_user_personal_info(
    monkeypatch,
    fake_ctx,
    seed_data: OutboundDTO | None,
    input_requested_fields: FieldsRequested.User.PersonalDetails,
    expected_result_text: OperationResultText | AgentFeedbackException,
):
    async def fake_get_data_with_readable_result(ctx, endpoint, requested_fields):
        assert endpoint == f"/operations/client/{fake_ctx.deps.user_id}"
        if seed_data is not None:
            assert seed_data.available_fields == requested_fields
        return expected_result_text, seed_data

    monkeypatch.setattr(
        personal_details, "get_data_with_readable_result", fake_get_data_with_readable_result
    )

    result = await get_user_personal_info(fake_ctx, requested_fields=input_requested_fields)
    if isinstance(expected_result_text, AgentFeedbackException):
        assert result == expected_result_text.message
    elif isinstance(expected_result_text, OperationResultText):
        assert result == expected_result_text.value

    if seed_data is not None:
        assert fake_ctx.deps.tool_output_queue.empty() is False
        queued = await fake_ctx.deps.tool_output_queue.get()
        assert queued == seed_data
        assert queued.data_layout == f"{OperationGroup.User_PersonalDetails.value}_layout"
    else:
        assert fake_ctx.deps.tool_output_queue.empty() is True


update_user_personal_info_test_cases = [
    pytest.param(
        OutboundDTO(
            available_fields=["first_name"],
            data={
                "first_name": "John",
            },
        ),
        FieldsRequested.User.PersonalDetails(
            first_name=True,
        ),
        None,
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_PersonalDetails.value}_layout",
            available_fields=["first_name"],
            data={
                "first_name": "John",
            },
        ),
        id="success, without new_values",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=["first_name"],
            data={"first_name": "John"},
        ),
        FieldsRequested.User.PersonalDetails(
            first_name=True,
        ),
        User.PersonalDetails(first_name="Steve"),
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_PersonalDetails.value}_layout",
            available_fields=["first_name"],
            data={"first_name": "Steve"},
        ),
        id="success, with new_values",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=["first_name", "last_name"],
            data={"first_name": "John", "last_name": "Doe"},
        ),
        FieldsRequested.User.PersonalDetails(
            first_name=True,
            last_name=True,
        ),
        User.PersonalDetails(first_name="Steve"),
        OperationResultText.SUCCESS,
        OutboundDTO(
            data_layout=f"{OperationGroup.User_PersonalDetails.value}_layout",
            available_fields=["first_name", "last_name"],
            data={"first_name": "Steve", "last_name": "Doe"},
        ),
        id="success, with new_values subset of fields_for_edit",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=[],
            data={},
        ),
        FieldsRequested.User.PersonalDetails(),
        None,
        NoFieldsRequestedException,
        None,
        id="fail, no fields_for_edit selected",
    ),
    pytest.param(
        None,
        FieldsRequested.User.PersonalDetails(
            first_name=True,
        ),
        User.PersonalDetails(first_name="Steve"),
        OperationResultText.SERVER_ERROR,
        None,
        id="fail, outbound schema None",
    ),
    pytest.param(
        OutboundDTO(
            available_fields=["first_name"],
            data=[],
        ),
        FieldsRequested.User.PersonalDetails(
            first_name=True,
        ),
        User.PersonalDetails(first_name="Steve"),
        OperationResultText.SERVER_ERROR,
        None,
        id="fail, data is list",
    ),
]


@pytest.mark.parametrize(
    "seed_data, input_requested_fields, input_new_values, expected_result_text, expected_outbound_schema",
    update_user_personal_info_test_cases,
)
async def test_update_user_personal_info(
    monkeypatch,
    fake_ctx,
    seed_data: OutboundDTO | None,
    input_requested_fields: FieldsRequested.User.PersonalDetails,
    input_new_values: User.PersonalDetails | None,
    expected_result_text: OperationResultText | AgentFeedbackException,
    expected_outbound_schema: OutboundDTO | None,
):
    async def fake_get_data_with_readable_result(ctx, endpoint, requested_fields):
        assert endpoint == f"/operations/client/{fake_ctx.deps.user_id}"
        if seed_data is not None:
            assert seed_data.available_fields == requested_fields
        return expected_result_text, seed_data

    monkeypatch.setattr(
        personal_details, "get_data_with_readable_result", fake_get_data_with_readable_result
    )

    result = await update_user_personal_info(
        fake_ctx, fields_for_edit=input_requested_fields, new_values=input_new_values
    )
    if isinstance(expected_result_text, AgentFeedbackException):
        assert result.__contains__(expected_result_text.message)
    elif isinstance(expected_result_text, OperationResultText):
        assert result.__contains__(expected_result_text.value)

    if (
        seed_data is not None
        and seed_data.data is not None
        and isinstance(seed_data.data, dict)
        and len([f for f, v in input_requested_fields if v]) > 0
    ):
        assert fake_ctx.deps.tool_output_queue.empty() is False
        queued = await fake_ctx.deps.tool_output_queue.get()
        assert queued == expected_outbound_schema
        assert queued.data_layout == f"{OperationGroup.User_PersonalDetails.value}_layout"
    else:
        assert fake_ctx.deps.tool_output_queue.empty() is True
