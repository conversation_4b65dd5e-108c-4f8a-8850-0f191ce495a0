import pytest

from app.chatbot.agents.exceptions import (
    AgentFeedbackException,
    NoFieldsRequestedException,
)
from app.chatbot.agents.tools.company import company
from app.chatbot.agents.tools.company.company import get_aventur_company_data
from app.chatbot.agents.user_text_input.input_schemas import (
    FieldsRequested,
)
from app.chatbot.constants import OperationGroup, OperationResultText
from app.chatbot.dtos.responses import OutboundDTO

get_company_test_cases = [
    pytest.param(
        OutboundDTO(
            available_fields=["email", "phone_number"],
            data={
                "email": "email",
                "phone_number": "phone",
            },
        ),
        FieldsRequested.Company(
            email=True,
            phone_number=True,
        ),
        OperationResultText.SUCCESS,
        id="success, data",
    ),
    pytest.param(
        None,
        FieldsRequested.Company(
            email=True,
            phone_number=True,
        ),
        OperationResultText.SERVER_ERROR,
        id="fail, server error",
    ),
    pytest.param(
        None,
        FieldsRequested.Company(),
        NoFieldsRequestedException,
        id="fail, no requested fields",
    ),
]


@pytest.mark.parametrize(
    "seed_data, input_requested_fields, expected_result_text", get_company_test_cases
)
async def test_get_aventur_company_data(
    monkeypatch,
    fake_ctx,
    seed_data: OutboundDTO | None,
    input_requested_fields: FieldsRequested.Company,
    expected_result_text: OperationResultText | AgentFeedbackException,
):
    async def fake_get_data_with_readable_result(ctx, endpoint, requested_fields):
        assert endpoint == "/operations/company"
        if seed_data is not None:
            assert seed_data.available_fields == requested_fields
        return expected_result_text, seed_data

    monkeypatch.setattr(
        company, "get_data_with_readable_result", fake_get_data_with_readable_result
    )

    result = await get_aventur_company_data(fake_ctx, requested_fields=input_requested_fields)
    if isinstance(expected_result_text, AgentFeedbackException):
        assert result == expected_result_text.message
    elif isinstance(expected_result_text, OperationResultText):
        assert result == expected_result_text.value

    if seed_data is not None:
        assert fake_ctx.deps.tool_output_queue.empty() is False
        queued = await fake_ctx.deps.tool_output_queue.get()
        assert queued.data == seed_data.data
        assert queued.available_fields == seed_data.available_fields
        assert queued.data_layout == f"{OperationGroup.Company.value}_layout"
    else:
        assert fake_ctx.deps.tool_output_queue.empty() is True
