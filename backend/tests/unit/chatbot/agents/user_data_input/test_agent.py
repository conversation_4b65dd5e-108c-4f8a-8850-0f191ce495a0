import pytest
from pydantic_ai import Agent, models

from app.chatbot.agents.user_data_input.agent import (
    create_data_input_agent,
    generate_readable_result_list,
)
from app.chatbot.constants import OperationGroup, OperationResultText
from app.chatbot.exceptions import NoOperationGroupSetException, NoResultsProvidedException
from app.chatbot.services.post_message.operations import OperationResults, OperationType

models.ALLOW_MODEL_REQUESTS = False


async def test_create_data_input_agent(user_id, mock_llm_gateway, mock_logger):
    input_results = OperationResults(
        operation_type=OperationType.POST,
        results=[
            OperationResultText.SUCCESS,
            OperationResultText.SUCCESS,
        ],
        operation_group=OperationGroup.User_Addresses,
    )

    agent = await create_data_input_agent(user_id, mock_llm_gateway, input_results)

    assert isinstance(agent, Agent)
    assert agent._instructions is not None
    assert len(agent._function_toolset.tools) == 1
    tool_names = agent._function_toolset.tools.keys()
    expected_names = [
        "null_tool",
    ]
    assert sorted(tool_names) == sorted(expected_names)


generate_readable_result_list_test_cases = [
    pytest.param(
        OperationResults(
            operation_type=OperationType.POST,
            results=[
                OperationResultText.SUCCESS,
                OperationResultText.SUCCESS,
            ],
            operation_group=OperationGroup.User_Addresses,
        ),
        "POST: user__addresses\n1: SUCCESS\n2: SUCCESS\n",
        None,
        id="success",
    ),
    pytest.param(
        OperationResults(
            operation_type=OperationType.POST,
            results=[
                OperationResultText.SUCCESS,
                OperationResultText.SUCCESS,
            ],
            operation_group=None,
        ),
        None,
        NoOperationGroupSetException,
        id="fail, no operation group",
    ),
    pytest.param(
        OperationResults(
            operation_type=OperationType.POST,
            results=[],
            operation_group=OperationGroup.User_Addresses,
        ),
        None,
        NoResultsProvidedException,
        id="fail, results list is empty",
    ),
]


@pytest.mark.parametrize(
    "input_results, expected_output, expected_error", generate_readable_result_list_test_cases
)
async def test_generate_readable_results_list(
    user_id, input_results, expected_output, expected_error
):
    if expected_error is not None:
        with pytest.raises(expected_error):
            readable_list = generate_readable_result_list(user_id, input_results)
    else:
        readable_list = generate_readable_result_list(user_id, input_results)
        assert readable_list == expected_output
