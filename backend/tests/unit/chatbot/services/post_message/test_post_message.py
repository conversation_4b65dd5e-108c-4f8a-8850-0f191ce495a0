import asyncio
from datetime import datetime
from types import SimpleNamespace
from unittest.mock import Mock

import pytest
from pydantic_ai.messages import (
    ModelResponse,
    PartDeltaEvent,
    PartStartEvent,
    TextPart,
    TextPartDelta,
)

import app.chatbot.services.post_message.post_message as pm_module
from app.chatbot.constants import OperationResultText
from app.chatbot.dtos.requests import HasCannedResponseProtocol, User_WithdrawConsentDTO
from app.chatbot.dtos.responses import AssistantTextDTO, SetIncompleteDTO
from app.chatbot.services.exceptions import AgentStreamError, ToolQueueError
from app.chatbot.services.post_message.operations import OperationResults, OperationType
from app.chatbot.services.post_message.post_message import PostMessage, ProcessingConfig


@pytest.fixture(autouse=True)
def stub_generate_id(monkeypatch):
    # Always return a fixed message ID
    monkeypatch.setattr(
        "app.chatbot.services.post_message.post_message.generate_message_id", lambda ts: "fixed-id"
    )


@pytest.fixture
def fake_logger():
    return SimpleNamespace(error=lambda *args, **kw: None, warn=lambda *args, **kw: None)


@pytest.fixture
def pm(fake_logger):
    # Internal deps can be None for these unit tests
    return PostMessage(
        logger=fake_logger,
        internal_client=None,
        llm_gateway=None,
        conversation_history_store=None,
    )


class FakeNode:
    def __init__(self, events, raise_on_enter=False):
        self.events = events
        self.raise_on_enter = raise_on_enter

    def stream(self, ctx):
        return self

    async def __aenter__(self):
        if self.raise_on_enter:
            raise RuntimeError("stream failed")
        self._iter = iter(self.events)
        return self

    async def __aexit__(self, exc_type, exc, tb):
        return False

    def __aiter__(self):
        return self

    async def __anext__(self):
        try:
            return next(self._iter)
        except StopIteration:
            raise StopAsyncIteration


async def test_handle_agent_node_events_emits_start_and_delta(pm):
    # given a start and a delta event
    events = [
        PartStartEvent(index=0, part=TextPart(content="Hello")),
        PartDeltaEvent(index=1, delta=TextPartDelta(content_delta=" world")),
    ]
    node = FakeNode(events)
    timestamp = datetime(2021, 1, 1, 12, 0, 0)

    out = []
    async for dto in pm._handle_agent_node_events(node, run_context_ctx=None, timestamp=timestamp):
        out.append(dto)

    # should yield two AssistantTextDTOs with incremental messages
    assert len(out) == 2
    assert isinstance(out[0], AssistantTextDTO)
    assert out[0].id == "fixed-id"
    assert out[0].timestamp == timestamp
    assert out[0].message == "Hello"

    assert out[1].message == "Hello world"


async def test_handle_agent_node_events_raises_agent_stream_error(pm):
    # node that fails on enter
    node = FakeNode(events=[], raise_on_enter=True)
    with pytest.raises(AgentStreamError):
        async for _ in pm._handle_agent_node_events(
            node, run_context_ctx=None, timestamp=datetime.now()
        ):
            pass


async def test_process_tool_queue_item_success_and_overflow(pm):
    # prepare a fake tool result
    tool_data = SimpleNamespace()
    task = asyncio.get_event_loop().create_future()
    task.set_result(tool_data)

    # normal append
    outbound = []
    result = await pm._process_tool_queue_item(task, outbound)
    assert result is tool_data
    assert result.id == "fixed-id"
    assert isinstance(result.timestamp, datetime)
    assert outbound == [tool_data]

    # now overflow: force max size =1
    pm.config = ProcessingConfig(
        MAX_QUEUE_SIZE=100, MAX_OUTBOUND_SCHEMAS=1, MAX_RESPONSE_TIMESTAMPS=50
    )
    outbound2 = [SimpleNamespace(id="old", timestamp=datetime(2000, 1, 1))]
    task2 = asyncio.get_event_loop().create_future()
    task2.set_result(tool_data)
    _ = await pm._process_tool_queue_item(task2, outbound2)
    # old one dropped, new appended
    assert len(outbound2) == 1
    assert outbound2[0] is tool_data


async def test_process_tool_queue_item_error(pm):
    # task that raises
    fut = asyncio.get_event_loop().create_future()
    fut.set_exception(RuntimeError("boom"))
    with pytest.raises(ToolQueueError):
        await pm._process_tool_queue_item(fut, [])


def test_should_terminate_processing(fake_logger):
    # Only true when the agent stream is exhausted AND the queue is empty
    pm = PostMessage(
        logger=fake_logger,
        internal_client=None,
        llm_gateway=None,
        conversation_history_store=None,
    )

    # Empty queue + exhausted → terminate
    q_empty = asyncio.Queue()
    assert pm._should_terminate_processing(True, q_empty) is True

    # Empty queue + not exhausted → continue
    assert pm._should_terminate_processing(False, q_empty) is False

    # Non‐empty queue + exhausted → continue
    q_nonempty = asyncio.Queue()
    q_nonempty.put_nowait(object())
    assert pm._should_terminate_processing(True, q_nonempty) is False


def test_prepare_conversation_messages_empty(pm):
    # no new messages -> empty
    assert pm._prepare_conversation_messages([], [], [], datetime.now()) == []


def test_prepare_conversation_messages_user_prompt_stamp(pm):
    # make a dummy UserPromptPart
    from pydantic_ai.messages import UserPromptPart

    part = UserPromptPart(content="hey", timestamp=None)
    msg = SimpleNamespace(parts=[part])
    ts = datetime(2022, 2, 2, 2, 2, 2)
    out = pm._prepare_conversation_messages([msg], [], [], ts)
    # the part should now carry initial timestamp
    assert out[0].parts[0].timestamp == ts


# Test fixtures for _process_data_input_message tests
@pytest.fixture
def mock_operation_dispatcher(monkeypatch):
    mock_results = OperationResults(
        operation_type=OperationType.POST, results=[OperationResultText.SUCCESS]
    )

    class MockDispatcher:
        def __init__(self, *_args, **_kwargs):
            pass

        async def dispatch(self, _dto):
            return mock_results

    monkeypatch.setattr(
        "app.chatbot.services.post_message.post_message.OperationDispatcher", MockDispatcher
    )
    return mock_results


@pytest.fixture
def mock_conversation_manager():
    manager = SimpleNamespace()
    manager.user_id = 123
    manager.get_or_create_conversation = AsyncMock()
    manager.save_conversation = AsyncMock()
    manager.format_for_agent = AsyncMock(return_value=[])
    return manager


@pytest.fixture
def mock_create_data_input_agent(monkeypatch):
    mock_agent = SimpleNamespace()
    mock_result = SimpleNamespace()
    mock_result.stream_text = Mock(return_value=AsyncIterator(["Agent response"]))
    mock_result.new_messages = Mock(
        return_value=[SimpleNamespace(), SimpleNamespace()]
    )  # Two messages, first will be popped

    # Create a proper async context manager mock
    class MockContextManager:
        def __init__(self, result):
            self.result = result

        async def __aenter__(self):
            return self.result

        async def __aexit__(self, exc_type, exc_val, exc_tb):
            return None

    mock_agent.run_stream = Mock(return_value=MockContextManager(mock_result))

    async def mock_create_agent(*args, **kwargs):
        return mock_agent

    monkeypatch.setattr(
        "app.chatbot.services.post_message.post_message.create_data_input_agent", mock_create_agent
    )
    return mock_agent


class AsyncIterator:
    def __init__(self, items):
        self.items = iter(items)

    def __aiter__(self):
        return self

    async def __anext__(self):
        try:
            return next(self.items)
        except StopIteration:
            raise StopAsyncIteration


class AsyncMock:
    def __init__(self, return_value=None):
        self.return_value = return_value
        self.call_count = 0
        self.call_args_list = []

    async def __call__(self, *args, **kwargs):
        self.call_count += 1
        self.call_args_list.append((args, kwargs))
        if callable(self.return_value):
            return self.return_value(*args, **kwargs)
        return self.return_value


class MockHasCannedResponseDTO:
    def __init__(self, message="Test canned response", dto_id="test-id"):
        self.id = dto_id
        self._message = message

    def response_message(self):
        return self._message


class MockInboundDataDTO:
    def __init__(self, dto_id="test-id"):
        self.id = dto_id


# Tests for _process_data_input_message method
@pytest.mark.usefixtures("mock_operation_dispatcher")
async def test_process_data_input_message_with_canned_response(pm, mock_conversation_manager):
    dto = MockHasCannedResponseDTO(message="Your consent has been withdrawn.", dto_id="test-123")

    results = []
    async for result in pm._process_data_input_message(
        user_id=123, dto=dto, conversation_manager=mock_conversation_manager, jwt_token="fake-jwt"
    ):
        results.append(result)

    assert len(results) == 1
    assert isinstance(results[0], AssistantTextDTO)
    assert results[0].message == "Your consent has been withdrawn."
    assert results[0].id == "fixed-id"  # From the stub_generate_id fixture
    assert isinstance(results[0].timestamp, datetime)

    assert mock_conversation_manager.save_conversation.call_count == 1
    saved_messages = mock_conversation_manager.save_conversation.call_args_list[0][0][0]
    assert len(saved_messages) == 1
    assert isinstance(saved_messages[0], ModelResponse)
    assert saved_messages[0].parts[0].content == "Your consent has been withdrawn."


@pytest.mark.usefixtures("mock_operation_dispatcher")
async def test_process_data_input_message_without_canned_response(
    pm, mock_conversation_manager, mock_create_data_input_agent
):
    dto = MockInboundDataDTO(dto_id="test-456")

    results = []
    async for result in pm._process_data_input_message(
        user_id=123, dto=dto, conversation_manager=mock_conversation_manager, jwt_token="fake-jwt"
    ):
        results.append(result)

    assert len(results) == 1
    assert isinstance(results[0], AssistantTextDTO)
    assert results[0].message == "Agent response"
    assert results[0].id == "fixed-id"
    assert isinstance(results[0].timestamp, datetime)

    assert mock_create_data_input_agent.run_stream.call_count == 1
    call_args = mock_create_data_input_agent.run_stream.call_args_list[0][1]
    assert call_args["user_prompt"] == "Continue."

    assert mock_conversation_manager.save_conversation.call_count == 1


async def test_process_data_input_message_canned_response_returns_none():
    class MockNoneResponseDTO:
        def __init__(self):
            self.id = "test-none"

        def response_message(self):
            return None

    class MockDispatcher:
        def __init__(self, *_args, **_kwargs):
            pass

        async def dispatch(self, _dto):
            return OperationResults(
                operation_type=OperationType.POST, results=[OperationResultText.SUCCESS]
            )

    pm = PostMessage(
        logger=SimpleNamespace(error=lambda *_args, **_kw: None, warn=lambda *_args, **_kw: None),
        internal_client=None,
        llm_gateway=None,
        conversation_history_store=None,
    )

    original_dispatcher = pm_module.OperationDispatcher
    pm_module.OperationDispatcher = MockDispatcher

    try:
        dto = MockNoneResponseDTO()
        conversation_manager = SimpleNamespace()
        conversation_manager.user_id = 123
        conversation_manager.get_or_create_conversation = AsyncMock()
        conversation_manager.save_conversation = AsyncMock()

        results = []
        async for result in pm._process_data_input_message(
            user_id=123, dto=dto, conversation_manager=conversation_manager, jwt_token="fake-jwt"
        ):
            results.append(result)

        assert len(results) == 1
        assert isinstance(results[0], AssistantTextDTO)
        assert results[0].message is None or results[0].message == "None"

    finally:
        pm_module.OperationDispatcher = original_dispatcher


async def test_process_data_input_message_operation_failure_sets_incomplete(
    pm, mock_conversation_manager
):
    class MockFailingDispatcher:
        def __init__(self, *_args, **_kwargs):
            pass

        async def dispatch(self, _dto):
            return OperationResults(
                operation_type=OperationType.POST, results=[OperationResultText.SERVER_ERROR]
            )

    original_dispatcher = pm_module.OperationDispatcher
    pm_module.OperationDispatcher = MockFailingDispatcher

    try:
        dto = MockHasCannedResponseDTO(message="Test message", dto_id="fail-test")

        results = []
        async for result in pm._process_data_input_message(
            user_id=123,
            dto=dto,
            conversation_manager=mock_conversation_manager,
            jwt_token="fake-jwt",
        ):
            results.append(result)

        assert len(results) == 2
        assert isinstance(results[0], SetIncompleteDTO)
        assert results[0].id == "fail-test"
        assert isinstance(results[1], AssistantTextDTO)
        assert results[1].message == "Test message"

    finally:
        pm_module.OperationDispatcher = original_dispatcher


async def test_process_data_input_message_has_canned_response_protocol_check():
    canned_dto = MockHasCannedResponseDTO()
    assert isinstance(canned_dto, HasCannedResponseProtocol)
    assert canned_dto.response_message() == "Test canned response"

    regular_dto = MockInboundDataDTO()
    assert not isinstance(regular_dto, HasCannedResponseProtocol)

    withdraw_dto = User_WithdrawConsentDTO(
        timestamp=datetime.now(),
        id=123,
        completed=False,
        data_layout="user_consent_withdraw_layout",
        withdrawn=True,
    )
    assert isinstance(withdraw_dto, HasCannedResponseProtocol)
    assert withdraw_dto.response_message() == "Your consent has been withdrawn."

    no_withdraw_dto = User_WithdrawConsentDTO(
        timestamp=datetime.now(),
        id=456,
        completed=False,
        data_layout="user_consent_withdraw_layout",
        withdrawn=False,
    )
    assert isinstance(no_withdraw_dto, HasCannedResponseProtocol)
    assert (
        no_withdraw_dto.response_message()
        == "You've decided not to withdraw your consent at this time. How else can I help you?"
    )
