# tests/test_operations.py
import json
from datetime import date, datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from pydantic import BaseModel

from api.enums.enums import Relationship
from app.chatbot.constants import (
    CHATBOT_ENDPOINT_ROOT,
    OPERATIONS_ENDPOINT_ROOT,
    OperationGroup,
    OperationResultText,
)
from app.chatbot.dtos.requests import (
    SubSchema,
    User_AddressesDTO,
    User_PersonalDetailsDTO,
    User_RelationsDTO,
    User_WithdrawConsentDTO,
)
from app.chatbot.services.post_message.operations import (
    OperationDispatcher,
    OperationResults,
    OperationType,
)


@pytest.fixture
def fake_store():
    store = MagicMock()
    store.overwrite_message_in_conversation = AsyncMock()
    return store


@pytest.fixture
def fake_client():
    # internal_client.get_internal_client() -> async context manager returning client
    client = AsyncMock()
    client_ctx = AsyncMock()
    client_ctx.__aenter__.return_value = client
    client_ctx.__aexit__.return_value = False
    internal = MagicMock()
    internal.get_internal_client.return_value = client_ctx
    return internal, client


@pytest.fixture
def dispatcher(fake_store, fake_client):
    internal, _ = fake_client
    return OperationDispatcher(
        logger=MagicMock(),
        store=fake_store,
        internal_client=internal,
        user_id=42,
        jwt_token="jwt-token",
    )


async def test_execute_put_post_creates_and_posts_and_puts(dispatcher, fake_store, fake_client):
    _, client = fake_client

    # Define two dummy SubSchema models: one without id (POST), one with id (PUT)
    class DummyModel(BaseModel):
        id: int | None = None
        datum: str

    m1 = DummyModel(id=None, datum="thing1")
    m2 = DummyModel(id=123, datum="thing2")

    # mock client.request to return objects with status_code attribute
    response1 = AsyncMock()
    response1.status_code = 201
    response2 = AsyncMock()
    response2.status_code = 200
    client.request.side_effect = [response1, response2]

    results = await dispatcher._execute_put_post(
        message_id=7,
        completed=True,
        entity_name="addresses",
        base_uri=f"{OPERATIONS_ENDPOINT_ROOT}/client",
        data=[m1, m2],
    )

    # ensure overwrite_message_in_conversation was called with JSON dump of models
    fake_store.overwrite_message_in_conversation.assert_awaited_once_with(
        42, 7, True, [{"id": None, "datum": "thing1"}, {"id": 123, "datum": "thing2"}]
    )

    # verify client.request calls: first POST, then PUT
    assert client.request.call_count == 2
    # first call: POST to /.../42/addresses

    called_args1 = client.request.call_args_list[0]
    assert called_args1[0][0] == OperationType.POST.value
    assert called_args1[0][1].endswith("/42/addresses")
    assert called_args1[1]["json"] == {"id": None, "datum": "thing1"}

    # second call: PUT to /.../42/addresses/123
    called_args2 = client.request.call_args_list[1]
    assert called_args2[0][0] == OperationType.PUT.value
    assert called_args2[0][1].endswith("/42/addresses/123")
    assert called_args2[1]["json"] == {"id": 123, "datum": "thing2"}

    # OperationResults
    assert isinstance(results, OperationResults)
    assert results.operation_type == OperationType.PUT  # last method used
    assert results.results == [OperationResultText.SUCCESS, OperationResultText.SUCCESS]


async def test_execute_put_post_creates_and_posts_and_puts_fails(
    dispatcher, fake_store, fake_client
):
    _, client = fake_client

    # Define two dummy SubSchema models: one without id (POST), one with id (PUT)
    class DummyModel(BaseModel):
        id: int | None = None
        datum: str

    m1 = DummyModel(id=None, datum="thing1")
    m2 = DummyModel(id=123, datum="thing2")

    # mock client.request to return objects with status_code attribute
    response1 = AsyncMock()
    response1.status_code = 201
    response2 = AsyncMock()
    response2.status_code = 500
    client.request.side_effect = [response1, response2]

    results = await dispatcher._execute_put_post(
        message_id=7,
        completed=True,
        entity_name="addresses",
        base_uri=f"{OPERATIONS_ENDPOINT_ROOT}/client",
        data=[m1, m2],
    )

    # ensure overwrite_message_in_conversation was called with JSON dump of models
    fake_store.overwrite_message_in_conversation.assert_awaited_once_with(
        42, 7, False, [{"id": None, "datum": "thing1"}, {"id": 123, "datum": "thing2"}]
    )

    # verify client.request calls: first POST, then PUT
    assert client.request.call_count == 2
    # first call: POST to /.../42/addresses

    called_args1 = client.request.call_args_list[0]
    assert called_args1[0][0] == OperationType.POST.value
    assert called_args1[0][1].endswith("/42/addresses")
    assert called_args1[1]["json"] == {"id": None, "datum": "thing1"}

    # second call: PUT to /.../42/addresses/123
    called_args2 = client.request.call_args_list[1]
    assert called_args2[0][0] == OperationType.PUT.value
    assert called_args2[0][1].endswith("/42/addresses/123")
    assert called_args2[1]["json"] == {"id": 123, "datum": "thing2"}

    # OperationResults
    assert isinstance(results, OperationResults)
    assert results.operation_type == OperationType.PUT  # last method used
    assert results.results == [OperationResultText.SUCCESS, OperationResultText.SERVER_ERROR]


async def test_dispatch_personal_details_and_store(dispatcher, fake_store, fake_client):
    _, client = fake_client

    ts = datetime.now()
    # Prepare a User_PersonalDetailsDTO with fields
    dto = User_PersonalDetailsDTO(
        id=99,
        timestamp=ts,
        completed=True,
        data_layout=f"{OperationGroup.User_PersonalDetails.value}_layout",
        available_fields=[
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "mobile_number",
            "date_of_birth",
        ],
        first_name="Alice",
        last_name="Smith",
        email="<EMAIL>",
        phone_number="1234567890",
        mobile_number="1234567891",
        date_of_birth=date(1990, 1, 1),
    )
    # Simulate client.put returning status
    fake_response = AsyncMock()
    fake_response.status_code = 200
    client.put.return_value = fake_response

    result = await dispatcher.dispatch(dto)

    # verify the internal_client.get_internal_client() context was used for .put
    client.put.assert_awaited_once()
    call_args = client.put.call_args_list[0]
    assert call_args[0][0].endswith("/42")
    assert json.loads(call_args[1]["content"]) == {
        "data_layout": f"{OperationGroup.User_PersonalDetails.value}_layout",
        "available_fields": [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "mobile_number",
            "date_of_birth",
        ],
        "first_name": "Alice",
        "last_name": "Smith",
        "email": "<EMAIL>",
        "phone_number": "1234567890",
        "mobile_number": "1234567891",
        "date_of_birth": "1990-01-01",
    }

    # verify store overwrite with cleaned payload
    expected_payload = {
        "first_name": "Alice",
        "last_name": "Smith",
        "email": "<EMAIL>",
        "phone_number": "1234567890",
        "mobile_number": "1234567891",
        "date_of_birth": "1990-01-01",
    }
    fake_store.overwrite_message_in_conversation.assert_awaited_once_with(
        42, 99, True, expected_payload
    )

    assert result.operation_type == OperationType.PUT
    assert result.operation_group == OperationGroup.User_PersonalDetails
    assert result.results == [OperationResultText.SUCCESS]


async def test_dispatch_personal_details_and_store_fails(dispatcher, fake_store, fake_client):
    _, client = fake_client

    ts = datetime.now()
    # Prepare a User_PersonalDetailsDTO with fields
    dto = User_PersonalDetailsDTO(
        id=99,
        timestamp=ts,
        completed=True,
        data_layout=f"{OperationGroup.User_PersonalDetails.value}_layout",
        available_fields=[
            "first_name",
        ],
        first_name="Alice",
    )
    # Simulate client.put returning status
    fake_response = AsyncMock()
    fake_response.status_code = 500
    client.put.return_value = fake_response

    result = await dispatcher.dispatch(dto)

    # verify the internal_client.get_internal_client() context was used for .put
    client.put.assert_awaited_once()
    call_args = client.put.call_args_list[0]
    assert call_args[0][0].endswith("/42")
    assert json.loads(call_args[1]["content"]) == {
        "data_layout": f"{OperationGroup.User_PersonalDetails.value}_layout",
        "available_fields": [
            "first_name",
        ],
        "first_name": "Alice",
        "last_name": None,
        "email": None,
        "phone_number": None,
        "mobile_number": None,
        "date_of_birth": None,
    }

    # verify store overwrite with cleaned payload
    expected_payload = {
        "first_name": "Alice",
        "last_name": None,
        "email": None,
        "phone_number": None,
        "mobile_number": None,
        "date_of_birth": None,
    }

    # The call failed, so we set completed to False, regardless of the DTO's 'completed' field value
    fake_store.overwrite_message_in_conversation.assert_awaited_once_with(
        42, 99, False, expected_payload
    )

    assert result.operation_type == OperationType.PUT
    assert result.operation_group == OperationGroup.User_PersonalDetails
    assert result.results == [OperationResultText.SERVER_ERROR]


# NOTE: The following tests include two entities per DTO and uses one with and one without an id to elicit a POST -> PUT. This is purely due to ease in testing In practice, we should expect a list of multiple entities either all to have ids i.e. POST, or none to have ids i.e. PUT.


async def test_dispatch_addresses_and_relations(dispatcher, fake_store, fake_client):
    _, client = fake_client
    # Prepare DTOs
    addresses_dto = User_AddressesDTO(
        timestamp=datetime.now(),
        id=5,
        completed=True,
        available_fields=["address_line_one", "city", "post_code"],
        data_layout=f"{OperationGroup.User_Addresses.value}_layout",
        addresses=[
            SubSchema.AddressSchema(id=None, address_line_one="A", city="B", post_code="C"),
            SubSchema.AddressSchema(id=1, address_line_one="D", city="E", post_code="F"),
        ],
    )
    # simulate two responses
    r1 = AsyncMock()
    r1.status_code = 201
    r2 = AsyncMock()
    r2.status_code = 200
    client.request.side_effect = [r1, r2]

    res_addr = await dispatcher.dispatch(addresses_dto)
    # verify operation group/tag
    assert res_addr.operation_group == OperationGroup.User_Addresses
    assert res_addr.operation_type == OperationType.PUT
    assert res_addr.results == [OperationResultText.SUCCESS, OperationResultText.SUCCESS]

    # Relations similar
    rel_dto = User_RelationsDTO(
        timestamp=datetime.now(),
        id=8,
        completed=False,
        available_fields=["first_name", "last_name", "date_of_birth", "relationship_type"],
        data_layout=f"{OperationGroup.User_Relations.value}_layout",
        relations=[
            SubSchema.RelationSchema(
                id=None,
                first_name="X",
                last_name="Y",
                date_of_birth=date(1990, 1, 1),
                relationship_type=Relationship.Child,
            ),
            SubSchema.RelationSchema(
                id=1,
                first_name="Z",
                last_name="A",
                date_of_birth=date(1990, 1, 1),
                relationship_type=Relationship.Child,
            ),
        ],
    )
    r3 = AsyncMock()
    r3.status_code = 201
    r4 = AsyncMock()
    r4.status_code = 200
    client.request.side_effect = [r3, r4]

    res_rel = await dispatcher.dispatch(rel_dto)
    assert res_rel.operation_group == OperationGroup.User_Relations
    assert res_rel.operation_type == OperationType.PUT
    assert res_rel.results == [OperationResultText.SUCCESS, OperationResultText.SUCCESS]


async def test_dispatch_withdraw_consent_success(dispatcher, fake_store, fake_client):
    _, client = fake_client

    dto = User_WithdrawConsentDTO(
        timestamp=datetime.now(),
        id=123,
        completed=True,
        data_layout="user_consent_withdraw_layout",
        withdrawn=True,
    )

    fake_response = AsyncMock()
    fake_response.status_code = 200
    client.put.return_value = fake_response

    result = await dispatcher.dispatch(dto)

    client.put.assert_awaited_once()
    call_args = client.put.call_args_list[0]
    assert call_args[0][0] == f"{CHATBOT_ENDPOINT_ROOT}/consent/42/withdraw"
    assert call_args[1]["headers"] == {"Authorization": "jwt-token"}

    fake_store.overwrite_message_in_conversation.assert_awaited_once_with(
        42, 123, True, {"withdrawn": True}
    )

    assert result.operation_type == OperationType.PUT
    assert result.operation_group == OperationGroup.User_Consent_Withdrawal
    assert result.results == [OperationResultText.SUCCESS]


async def test_dispatch_withdraw_consent_failure(dispatcher, fake_store, fake_client):
    _, client = fake_client

    dto = User_WithdrawConsentDTO(
        timestamp=datetime.now(),
        id=456,
        completed=True,
        data_layout="user_consent_withdraw_layout",
        withdrawn=True,
    )

    fake_response = AsyncMock()
    fake_response.status_code = 500
    client.put.return_value = fake_response

    result = await dispatcher.dispatch(dto)

    client.put.assert_awaited_once()
    call_args = client.put.call_args_list[0]
    assert call_args[0][0] == f"{CHATBOT_ENDPOINT_ROOT}/consent/42/withdraw"
    assert call_args[1]["headers"] == {"Authorization": "jwt-token"}

    fake_store.overwrite_message_in_conversation.assert_awaited_once_with(
        42, 456, False, {"withdrawn": True}
    )

    assert result.operation_type == OperationType.PUT
    assert result.operation_group == OperationGroup.User_Consent_Withdrawal
    assert result.results == [OperationResultText.SERVER_ERROR]


async def test_dispatch_withdraw_consent_not_withdrawn_completed_true(
    dispatcher, fake_store, fake_client
):
    _, client = fake_client

    dto = User_WithdrawConsentDTO(
        timestamp=datetime.now(),
        id=999,
        completed=True,
        data_layout="user_consent_withdraw_layout",
        withdrawn=False,
    )

    result = await dispatcher.dispatch(dto)

    client.put.assert_not_called()

    fake_store.overwrite_message_in_conversation.assert_awaited_once_with(
        42, 999, True, {"withdrawn": False}
    )

    assert result.operation_type == OperationType.PUT
    assert result.operation_group == OperationGroup.User_Consent_Withdrawal
    assert result.results == []
    assert result.additional_context == "The user decided not to withdraw their consent."
