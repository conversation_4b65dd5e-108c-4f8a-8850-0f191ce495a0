# pylint: disable=E1101
from dataclasses import dataclass
from datetime import date, datetime
from typing import Any
from unittest.mock import ANY, AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest

from api.enums import IdentityCheckResult
from api.exceptions.base_exceptions import ExternalServiceException
from api.exceptions.client_exceptions import (
    ClientDataIncompleteError,
    ClientNotFoundError,
    ClientNotRegisteredForMonitoringError,
)
from app.aml.services.aml_service import AMLCommandService, AMLQueryService
from app.clients.services.add_client import AddClientService
from app.shared_kernel.dtos.client_dtos import AddressDTO, ClientDetailDTO, CountryDTO
from database.models import ClientAddressModel, CountryModel
from database.repositories.aml.aml_monitoring_dtos import AMLMonitoringDTO
from database.repositories.aml.aml_search_dtos import AddAMLSearchResultDTO, AMLSearchCriteriaDTO
from database.repositories.aml.aml_search_mapper import evaluate_identity_check_result
from gateway_accessors.zignsec.exceptions import ZignSecAPIException
from gateway_accessors.zignsec.models import (
    AMLSetupMonitoringZignsecRequest,
    AMLValidateIdentityZignsecRequest,
    DueDiligence,
    IdentityMatchLevel,
    MonitoringResultResponse,
    MonitoringSessionResponse,
    ValidateIdentityZignsecResponse,
)
from tests import fakes
from tests.unit.services.fake_repo_and_uow import FakeUnitOfWork


def _create_address(client_id):
    return ClientAddressModel(
        client_id=client_id,
        address_line_one="1 Somewhere",
        city="Somewhere",
        post_code="S12 3AB",
        country_id=1,
        is_primary=True,
        country=CountryModel(
            id=1,
            iso_alpha2="GB",
            iso_alpha3="GBR",
            name="United Kingdom",
        ),
    )


@pytest.fixture
def aml_command_service(uow: FakeUnitOfWork) -> AMLCommandService:
    return AMLCommandService(uow=uow, logger=MagicMock(), zignsec_accessor=AsyncMock())


@pytest.fixture
def aml_query_service(uow: FakeUnitOfWork) -> AMLQueryService:
    return AMLQueryService(
        logger=MagicMock(),
        zignsec_accessor=AsyncMock(),
        aml_search_repo=AsyncMock(),
        aml_monitoring_repo=AsyncMock(),
    )


@pytest.mark.asyncio
class TestValidateClientAndMonitor:
    async def test_raises_expected_exception_when_the_client_does_not_exist(
        self, aml_command_service: AMLCommandService
    ) -> None:
        async with aml_command_service.uow as uow:
            uow.clients2.get_by_id.return_value = None

        with pytest.raises(ClientNotFoundError):
            await aml_command_service.client_identity_check(999)

    async def test_raises_expected_exception_when_the_client_has_no_address(
        self, add_client_service: AddClientService, aml_command_service: AMLCommandService
    ) -> None:
        # Seed a client with no addresses
        async with aml_command_service.uow as uow:
            uow.clients2.get_by_id.return_value = ClientDetailDTO(
                id=1,
                first_name="John",
                last_name="Doe",
                date_of_birth=date(1990, 1, 1),
                addresses=[],
                type="client",
                links=[],
                email="",
            )
        with pytest.raises(ClientDataIncompleteError):
            await aml_command_service.client_identity_check(1)

    @pytest.mark.parametrize(
        "identity_check_result",
        (IdentityCheckResult.PASS, IdentityCheckResult.REFER, IdentityCheckResult.FAIL),
    )
    @patch(
        "app.aml.services.aml_service.AMLCommandService._add_client_to_monitoring_list",
        new_callable=AsyncMock,
    )
    @patch(
        "app.aml.services.aml_service.AMLCommandService._validate_client_identity",
        new_callable=AsyncMock,
    )
    async def test_makes_expected_calls(
        self,
        mock_validate: AsyncMock,
        mock_add: AsyncMock,
        identity_check_result: IdentityCheckResult,
        uow: FakeUnitOfWork,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        # Seed a client
        client_data = fakes.fake_client_data()
        client = ClientDetailDTO(
            id=1,
            first_name=client_data["first_name"],
            last_name=client_data["last_name"],
            date_of_birth=client_data["date_of_birth"],
            addresses=[
                AddressDTO(
                    address_line_one="1 Somewhere",
                    city="Somewhere",
                    post_code="S12 3AB",
                    country_id=1,
                    country=CountryDTO(
                        id=1, iso_alpha2="GB", name="United Kingdom", iso_alpha3="GBR"
                    ),
                    is_primary=True,
                )
            ],
            type="client",
            links=[],
            email="",
        )
        async with aml_command_service.uow as uow:
            uow.clients2.get_by_id.return_value = client
        mock_validate.return_value = identity_check_result

        result = await aml_command_service.client_identity_check(1)
        assert result == identity_check_result
        mock_validate.assert_awaited_once_with(client)

        if identity_check_result != IdentityCheckResult.FAIL:
            mock_add.assert_awaited_once_with(client)
        else:
            mock_add.assert_not_awaited()


@pytest.mark.asyncio
class TestValidateClientIdentity:
    async def test_raises_expected_exception_when_external_api_errors(
        self,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        # Seed a client
        client = ClientDetailDTO(
            id=1,
            title_id=1,
            first_name="first",
            last_name="last",
            email="email",
            type="client",
            addresses=[
                AddressDTO(
                    address_line_one="1 Somewhere",
                    city="Somewhere",
                    post_code="S12 3AB",
                    country_id=1,
                    country=CountryDTO(
                        id=1, iso_alpha2="GB", name="United Kingdom", iso_alpha3="GBR"
                    ),
                    is_primary=True,
                )
            ],
            links=[],
            date_of_birth=date(year=2000, month=2, day=2),
        )

        aml_command_service.zignsec_accessor.validate_identity.side_effect = [
            ZignSecAPIException("Something bad happened...")
        ]

        with pytest.raises(ExternalServiceException):
            await aml_command_service._validate_client_identity(client)
        aml_command_service.zignsec_accessor.validate_identity.assert_called_once()

    async def test_saves_validation_result_to_db(
        self,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        with patch.object(FakeUnitOfWork, "aml_searches2", new_callable=AsyncMock) as patched_repo:
            # Seed a client
            client_data = fakes.fake_client_data()
            client = ClientDetailDTO(
                id=1,
                first_name=client_data["first_name"],
                last_name=client_data["last_name"],
                date_of_birth=client_data["date_of_birth"],
                addresses=[
                    AddressDTO(
                        address_line_one="1 Somewhere",
                        city="Somewhere",
                        post_code="S12 3AB",
                        country_id=1,
                        country=CountryDTO(
                            id=1, iso_alpha2="GB", name="United Kingdom", iso_alpha3="GBR"
                        ),
                        is_primary=True,
                    )
                ],
                type="client",
                links=[],
                email="",
            )
            async with aml_command_service.uow as uow:
                uow.clients2.get_by_id.return_value = client

            request_data = AMLValidateIdentityZignsecRequest(
                first_name=client.first_name,
                last_name=client.last_name,
                country_code="GB",
                address=client.primary_address.address_line_one,
                city=client.primary_address.city,
                postcode=client.primary_address.post_code,
                date_of_birth=client.date_of_birth,
            )

            session_id = uuid4()

            api_response = ValidateIdentityZignsecResponse(
                id=session_id,
                WasFound=True,
                MatchLevel=IdentityMatchLevel.High,
                DetailCode="",
                ValidationDetails={},
            )
            aml_command_service.zignsec_accessor.validate_identity.return_value = api_response

            result = await aml_command_service._validate_client_identity(client)

            assert result == IdentityCheckResult.PASS
            aml_command_service.zignsec_accessor.validate_identity.assert_awaited_once_with(
                request_data
            )
            expected = AddAMLSearchResultDTO(
                client_id=1,
                search_criteria=AMLSearchCriteriaDTO(
                    first_name="FirstName",
                    last_name="LastName",
                    address="1 Somewhere",
                    city="Somewhere",
                    post_code="S12 3AB",
                    country_code="GB",
                    date_of_birth=client_data["date_of_birth"],
                ),
                found=True,
                match_level=IdentityMatchLevel.High,
                full_results={
                    "id": session_id,
                    "detail_code": "",
                    "match_level": IdentityMatchLevel.High,
                    "was_found": True,
                    "validation_details": {},
                    "verified_age": None,
                },
            )

            patched_repo.add_search_result.assert_called_once_with(expected)


@dataclass
class FakeValidateIdentityResponse:
    was_found: bool
    match_level: IdentityMatchLevel


class TestEvaluateIdentityCheckResult:
    @pytest.mark.parametrize(
        "response, expected_result",
        (
            (
                FakeValidateIdentityResponse(was_found=False, match_level=IdentityMatchLevel.Low),
                IdentityCheckResult.FAIL,
            ),
            (
                FakeValidateIdentityResponse(was_found=False, match_level=IdentityMatchLevel.High),
                IdentityCheckResult.FAIL,
            ),
            (
                FakeValidateIdentityResponse(was_found=True, match_level=IdentityMatchLevel.Low),
                IdentityCheckResult.FAIL,
            ),
            (
                FakeValidateIdentityResponse(was_found=True, match_level=IdentityMatchLevel.Medium),
                IdentityCheckResult.REFER,
            ),
            (
                FakeValidateIdentityResponse(was_found=True, match_level=IdentityMatchLevel.High),
                IdentityCheckResult.PASS,
            ),
        ),
    )
    def test_identifies_expected_state(
        self,
        response: FakeValidateIdentityResponse,
        expected_result: IdentityCheckResult,
    ) -> None:
        assert (
            evaluate_identity_check_result(response.match_level, response.was_found)
            == expected_result
        )


@pytest.mark.asyncio
class TestGetClientMonitoringDetail:
    async def test_raises_expected_exception_when_client_not_registered_for_monitoring(
        self, aml_query_service: AMLQueryService
    ) -> None:
        aml_query_service.aml_monitoring_repo.get_by_client_id.return_value = None

        with pytest.raises(ClientNotRegisteredForMonitoringError):
            await aml_query_service.get_client_monitoring_detail(999)

    async def test_raises_expected_exception_when_external_api_errors(
        self, aml_query_service: AMLQueryService
    ) -> None:
        session_id = uuid4()
        aml_query_service.aml_monitoring_repo.get_by_client_id.return_value = AMLMonitoringDTO(
            client_id=999,
            zignsec_session_id=session_id,
            created_datetime=datetime.now(),
            raw_data={},
            matches=[],
        )

        aml_query_service.zignsec_accessor.get_monitoring_detail.side_effect = [
            ZignSecAPIException("Oops!")
        ]
        with pytest.raises(ExternalServiceException):
            await aml_query_service.get_client_monitoring_detail(999)

    async def test_calls_accessor_with_expected_args(
        self, aml_query_service: AMLQueryService
    ) -> None:
        session_id = uuid4()
        aml_query_service.aml_monitoring_repo.get_by_client_id.return_value = AMLMonitoringDTO(
            client_id=1,
            zignsec_session_id=session_id,
            created_datetime=datetime.now(),
            raw_data={},
            matches=[],
        )

        result = MonitoringSessionResponse(
            id=session_id,
            status="Monitoring",
            result=MonitoringResultResponse(
                matches=[],
                dueDiligence=DueDiligence(decisions=[], matchesDecisionHistory=[]),
            ),
            lastUpdated=datetime.now(),
        )
        aml_query_service.zignsec_accessor.get_monitoring_detail.return_value = result

        assert await aml_query_service.get_client_monitoring_detail(999) == {
            "id": session_id,
            "matches": [],
            "status": "Monitoring",
        }

        aml_query_service.zignsec_accessor.get_monitoring_detail.assert_called_once_with(session_id)


@pytest.mark.asyncio
class TestAddClientToMonitoringList:
    def request_for_client(
        self, client_id: int, client_data: dict[str, Any]
    ) -> AMLSetupMonitoringZignsecRequest:
        return AMLSetupMonitoringZignsecRequest(
            client_id=client_id,
            first_name=client_data["first_name"],
            last_name=client_data["last_name"],
            gender=None,
            date_of_birth=client_data["date_of_birth"],
            country_code="GB",
        )

    async def test_makes_expected_calls(
        self,
        uow: FakeUnitOfWork,
        add_client_service: AddClientService,
        aml_command_service: AMLCommandService,
    ) -> None:
        client_data = fakes.fake_client_data()
        client = ClientDetailDTO(
            id=1,
            first_name=client_data["first_name"],
            last_name=client_data["last_name"],
            date_of_birth=client_data["date_of_birth"],
            addresses=[
                AddressDTO(
                    address_line_one="1 Somewhere",
                    city="Somewhere",
                    post_code="S12 3AB",
                    country_id=1,
                    country=CountryDTO(
                        id=1, iso_alpha2="GB", name="United Kingdom", iso_alpha3="GBR"
                    ),
                    is_primary=True,
                )
            ],
            type="client",
            links=[],
            email="",
        )
        async with aml_command_service.uow as uow:
            uow.clients2.get_by_id.return_value = client
        request_data = self.request_for_client(1, client_data)
        session_id = uuid4()

        aml_command_service.zignsec_accessor.add_person_to_monitoring.return_value = (
            MonitoringSessionResponse(
                id=session_id,
                status="Monitoring",
                result=MonitoringResultResponse(
                    matches=[],
                    dueDiligence=DueDiligence(decisions=[], matchesDecisionHistory=[]),
                ),
                lastUpdated=datetime.now(),
            )
        )
        with (
            patch.object(
                FakeUnitOfWork, "aml_monitoring_sessions2", new_callable=AsyncMock
            ) as patched_monitoring_repo,
            patch.object(
                FakeUnitOfWork, "aml_searches2", new_callable=AsyncMock
            ) as patched_search_repo,
        ):
            patched_monitoring_repo.get_by_client_id.return_value = None
            patched_search_repo.add_search_result.return_value = None

            await aml_command_service.client_identity_check(1)

            patched_monitoring_repo.get_by_client_id.assert_called_once_with(1)
            aml_command_service.zignsec_accessor.add_person_to_monitoring.assert_called_once_with(
                request_data
            )

            result = AMLMonitoringDTO(
                client_id=1,
                zignsec_session_id=session_id,
                matches=[],
                raw_data={
                    "id": session_id,
                    "created": ANY,
                    "status": "Monitoring",
                    "result": {
                        "matches": [],
                        "due_diligence": {"decisions": [], "matches_decision_history": []},
                    },
                },
                id=None,
                created_datetime=ANY,
                updated_datetime=None,
            )
            patched_monitoring_repo.add_monitoring_session.assert_called_once_with(result)

    async def test_does_nothing_when_client_is_already_setup(
        self, aml_command_service: AMLCommandService
    ) -> None:
        with patch.object(
            FakeUnitOfWork, "aml_monitoring_sessions2", new_callable=AsyncMock
        ) as patched_repo:
            patched_repo.get_by_client_id.return_value = "Some session"

            client = ClientDetailDTO(
                id=999,
                type="client",
                email="<EMAIL>",
                addresses=[
                    AddressDTO(
                        is_primary=True,
                        country=CountryDTO(
                            id=1, iso_alpha2="GB", iso_alpha3="GBR", name="United Kingdom"
                        ),
                    )
                ],
                links=[],
            )

            await aml_command_service._add_client_to_monitoring_list(client)

            aml_command_service.zignsec_accessor.add_person_to_monitoring.assert_not_called()


@pytest.mark.asyncio
class TestProcessMonitoringStatusUpdate:
    async def test_makes_expected_calls(self, aml_command_service: AMLCommandService) -> None:
        session_id = uuid4()
        notification = AMLMonitoringDTO(
            client_id=1,
            zignsec_session_id=session_id,
            matches=[],
            raw_data={
                "id": session_id,
                "created": ANY,
                "status": "Monitoring",
                "result": {
                    "matches": [],
                    "due_diligence": {"decisions": [], "matches_decision_history": []},
                },
            },
            id=None,
            created_datetime=ANY,
            updated_datetime=None,
        )
        with patch.object(
            FakeUnitOfWork, "aml_monitoring_sessions2", new_callable=AsyncMock
        ) as patched_repo:
            await aml_command_service.update_watchlist_data(notification)

            patched_repo.update_monitoring_session.assert_called_once()
