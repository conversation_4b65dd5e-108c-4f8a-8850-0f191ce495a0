from unittest.mock import AsyncMock, MagicMock

import pytest

from api.enums import User<PERSON><PERSON>
from app.clients.services.client_chatbot_permissions import (
    ClientChatbotPermissionsQueryService,
    ClientChatbotPermissionsService,
)
from app.shared_kernel.dtos.client_dtos import Client<PERSON><PERSON>ilD<PERSON>
from tests.unit.services.fake_repo_and_uow import FakeUnitOfWork


@pytest.fixture
def client_dto() -> ClientDetailDTO:
    return ClientDetailDTO(
        id=1,
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        email="<EMAIL>",
        type="client",
        addresses=[],
        links=[],
        chatbot_consent_granted=True,
    )


@pytest.fixture
def client_dto_no_consent() -> ClientDetailDTO:
    return ClientDetailDTO(
        id=1,
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        email="<EMAIL>",
        type="client",
        addresses=[],
        links=[],
        chatbot_consent_granted=False,
    )


@pytest.fixture
def client_dto_no_email() -> ClientDetailDTO:
    return ClientDetailDTO(
        id=1,
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        email=None,
        type="client",
        addresses=[],
        links=[],
        chatbot_consent_granted=False,
    )


@pytest.fixture
def mock_client_repository():
    return AsyncMock()


@pytest.fixture
def mock_cognito_gateway():
    return MagicMock()


class TestClientChatbotPermissionsQueryService:
    async def test_get_chatbot_consent_status_returns_true_when_client_has_consent(
        self, mock_client_repository: AsyncMock, client_dto: ClientDetailDTO
    ) -> None:
        # Arrange
        mock_client_repository.get_by_id.return_value = client_dto
        service = ClientChatbotPermissionsQueryService(
            logger=MagicMock(), client_repository=mock_client_repository
        )

        # Act
        result = await service.get_chatbot_consent_status(1)

        # Assert
        assert result is True
        mock_client_repository.get_by_id.assert_called_once_with(1)

    async def test_get_chatbot_consent_status_returns_false_when_client_has_no_consent(
        self, mock_client_repository: AsyncMock, client_dto_no_consent: ClientDetailDTO
    ) -> None:
        # Arrange
        mock_client_repository.get_by_id.return_value = client_dto_no_consent
        service = ClientChatbotPermissionsQueryService(
            logger=MagicMock(), client_repository=mock_client_repository
        )

        # Act
        result = await service.get_chatbot_consent_status(1)

        # Assert
        assert result is False
        mock_client_repository.get_by_id.assert_called_once_with(1)

    async def test_get_chatbot_consent_status_returns_false_when_client_not_found(
        self, mock_client_repository: AsyncMock
    ) -> None:
        # Arrange
        mock_client_repository.get_by_id.return_value = None
        service = ClientChatbotPermissionsQueryService(
            logger=MagicMock(), client_repository=mock_client_repository
        )

        # Act
        result = await service.get_chatbot_consent_status(999)

        # Assert
        assert result is False
        mock_client_repository.get_by_id.assert_called_once_with(999)


class TestClientChatbotPermissionsService:
    async def test_grant_chatbot_consent_updates_db_and_cognito(
        self, uow: FakeUnitOfWork, mock_cognito_gateway: MagicMock, client_dto: ClientDetailDTO
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = client_dto
        mock_cognito_gateway.get_user_groups.return_value = ["ExistingRole"]
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act
        await service.grant_chatbot_consent(1)

        # Assert
        async with uow:
            uow.clients2.grant_chatbot_consent.assert_called_once_with(1)
        mock_cognito_gateway.get_user_groups.assert_called_once_with(client_dto.email)
        expected_groups = sorted([UserRole.ChatbotUser.value, "ExistingRole"])
        actual_call = mock_cognito_gateway.sync_user_groups.call_args[0]
        assert actual_call[0] == client_dto.email
        assert sorted(actual_call[1]) == expected_groups
        uow.mock_session.commit.assert_awaited_once()

    async def test_grant_chatbot_consent_does_nothing_when_client_not_found(
        self, uow: FakeUnitOfWork, mock_cognito_gateway: MagicMock
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = None
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act
        await service.grant_chatbot_consent(1)

        # Assert
        async with uow:
            uow.clients2.grant_chatbot_consent.assert_not_called()
        mock_cognito_gateway.get_user_groups.assert_not_called()
        mock_cognito_gateway.sync_user_groups.assert_not_called()

    async def test_grant_chatbot_consent_does_nothing_when_client_has_no_email(
        self,
        uow: FakeUnitOfWork,
        mock_cognito_gateway: MagicMock,
        client_dto_no_email: ClientDetailDTO,
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = client_dto_no_email
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act
        await service.grant_chatbot_consent(1)

        # Assert
        async with uow:
            uow.clients2.grant_chatbot_consent.assert_not_called()
        mock_cognito_gateway.get_user_groups.assert_not_called()
        mock_cognito_gateway.sync_user_groups.assert_not_called()

    async def test_grant_chatbot_consent_aborts_transaction_on_cognito_exception(
        self, uow: FakeUnitOfWork, mock_cognito_gateway: MagicMock, client_dto: ClientDetailDTO
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = client_dto
        mock_cognito_gateway.get_user_groups.side_effect = Exception("Cognito error")
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act & Assert
        with pytest.raises(Exception, match="Cognito error"):
            await service.grant_chatbot_consent(1)
            uow.mock_session.commit.assert_not_awaited()

    async def test_withdraw_chatbot_consent_updates_db_and_cognito(
        self, uow: FakeUnitOfWork, mock_cognito_gateway: MagicMock, client_dto: ClientDetailDTO
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = client_dto
        mock_cognito_gateway.get_user_groups.return_value = [
            "ExistingRole",
            UserRole.ChatbotUser.value,
        ]
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act
        await service.withdraw_chatbot_consent(1)

        # Assert
        async with uow:
            uow.clients2.withdraw_chatbot_consent.assert_called_once_with(1)
        mock_cognito_gateway.get_user_groups.assert_called_once_with(client_dto.email)
        mock_cognito_gateway.sync_user_groups.assert_called_once_with(
            client_dto.email, ["ExistingRole"]
        )
        uow.mock_session.commit.assert_awaited_once()

    async def test_withdraw_chatbot_consent_does_nothing_when_client_not_found(
        self, uow: FakeUnitOfWork, mock_cognito_gateway: MagicMock
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = None
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act
        await service.withdraw_chatbot_consent(1)

        # Assert
        async with uow:
            uow.clients2.withdraw_chatbot_consent.assert_not_called()
        mock_cognito_gateway.get_user_groups.assert_not_called()
        mock_cognito_gateway.sync_user_groups.assert_not_called()

    async def test_withdraw_chatbot_consent_does_nothing_when_client_has_no_email(
        self,
        uow: FakeUnitOfWork,
        mock_cognito_gateway: MagicMock,
        client_dto_no_email: ClientDetailDTO,
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = client_dto_no_email
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act
        await service.withdraw_chatbot_consent(1)

        # Assert
        async with uow:
            uow.clients2.withdraw_chatbot_consent.assert_not_called()
        mock_cognito_gateway.get_user_groups.assert_not_called()
        mock_cognito_gateway.sync_user_groups.assert_not_called()

    async def test_withdraw_chatbot_consent_aborts_transaction_on_cognito_exception(
        self, uow: FakeUnitOfWork, mock_cognito_gateway: MagicMock, client_dto: ClientDetailDTO
    ) -> None:
        # Arrange
        async with uow:
            uow.clients2.get_by_id.return_value = client_dto
        mock_cognito_gateway.get_user_groups.side_effect = Exception("Cognito error")
        service = ClientChatbotPermissionsService(
            uow=uow, logger=MagicMock(), cognito_gateway=mock_cognito_gateway
        )

        # Act & Assert
        with pytest.raises(Exception, match="Cognito error"):
            await service.withdraw_chatbot_consent(1)
            uow.mock_session.commit.assert_not_awaited()
