from collections import defaultdict
from typing import Any, Iterable, Self
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON>ck

from api import enums
from app.documents.enums import DocumentType
from app.fees.entities.fee_split_templates import FeeSplitTemplate
from app.shared_kernel.dtos.case_dtos import AdministratorDTO
from app.shared_kernel.dtos.client_dtos import AddressD<PERSON>
from app.shared_kernel.dtos.client_relation_dtos import AddClient<PERSON>elationDTO
from app.shared_kernel.dtos.client_snapshot_dtos import Client<PERSON>elationD<PERSON>
from app.shared_kernel.dtos.goals import ExpectedFeeDTO
from app.shared_kernel.dtos.user_dtos import UserDTO
from app.shared_kernel.enums.repositories import ActivityType
from database.models import (
    AdviceModel,
    ClientHoldingExpectedFeeModel,
    ClientHoldingModel,
    ClientLinkModel,
    DocumentGenerationTemplate,
    FeesPaidModel,
    OwnerModel,
    PaymentsModel,
    PaymentStatementMatchModel,
    ProviderModel,
    StatementModel,
)
from database.repositories import (
    AbstractAdviceRepository,
    AbstractClientHoldingExpectedFeeRepository,
    AbstractClientHoldingLinkRepository,
    AbstractClientLinkRepository,
    AbstractDocumentTemplateRepository,
    AbstractFeePaidRepository,
    AbstractHoldingRepository,
    AbstractMatchRepository,
    AbstractOwnerRepository,
    AbstractPaymentRepository,
    AbstractProviderRepository,
    AbstractStatementRepository,
    AbstractUserRepository,
)
from database.repositories.abstract_unit_of_work import AbstractUnitOfWork, AsyncSessionInterface
from database.repositories.base_event_repository import AbstractEventRepository
from database.repositories.base_repository import AbstractRepository, Entity
from database.repositories.case.case_repository import SQLAlchemyCaseRepository
from database.repositories.clients.client_address_repository import AbstractClientAddressRepository
from database.repositories.clients.client_relations_repository import (
    AbstractClientRelationsRepository,
)
from database.repositories.clients.client_repository import SQLAlchemyClientRepository
from database.repositories.fee_split_repository import AbstractFeeSplitTemplateRepository
from database.repositories.fees.expected_fee_respository import (
    SQLAlchemyExpectedFeeRepository,
)
from database.repositories.holdings.holdings_repository import SQLAlchemyHoldingsRepository


class FakeRepository(AbstractRepository[Entity]):
    def __init__(self) -> None:
        self._entities: dict[int | Any, Entity] = {}
        self._next_id = 1

    async def add(self, entity: Entity) -> None:
        # Simulate an auto-increment ID
        if hasattr(entity, "id") and entity.id is None:
            entity.id = self._next_id
            self._next_id += 1
        self._entities[entity.id] = entity

    async def bulk_add(self, entities: Iterable[Entity]) -> None:
        for entity in entities:
            await self.add(entity)

    async def count(self, *args) -> int:
        return len(self._entities)

    async def find(self, *args, offset: int = 0, limit: int | None = None) -> list[Entity]:
        # For simplicity, assume args are not used for filtering
        entities = list(self._entities.values())
        end = None if limit is None else offset + limit
        return entities[offset:end]

    async def get(self, entity_id: int) -> Entity | None:
        return self._entities.get(entity_id)

    async def list(self, offset: int = 0, limit: int | None = None) -> list[Entity]:
        return await self.find(offset=offset, limit=limit)

    async def remove(self, entity: Entity) -> None:
        if entity.id in self._entities:
            del self._entities[entity.id]

    def with_eager_load(self, fields_to_eager_load: Iterable[Any]) -> Self:
        # No-op for fake repository
        return self

    def with_options(self, options: Iterable[Any]) -> Self:
        # No-op for fake repository
        return self

    def with_joins(self, joins: Iterable[Any]) -> Self:
        # No-op for fake repository
        return self

    def with_order_by(self, order_by: Any) -> Self:
        # No-op for fake repository
        return self


class FakeAdviceRepository(AbstractAdviceRepository, FakeRepository):
    async def get(self, holding_id: int, goal_id: int) -> AdviceModel:  # type: ignore
        for entity in self._entities.values():
            if entity.holding_id == holding_id and entity.case_goal_id == goal_id:
                return entity
        return None


class FakeClientLinkRepository(AbstractClientLinkRepository, FakeRepository):
    async def add(self, entity) -> None:
        self._entities[(entity.client_id, entity.linked_client_id)] = entity

    async def get(self, client_id: int, linked_client_id: int) -> ClientLinkModel:  # type: ignore
        for entity in self._entities.values():
            if entity.client_id == client_id and entity.linked_client_id == linked_client_id:
                return entity
        return None

    async def get_by_client_id(self, client_id: int) -> list[ClientLinkModel]:
        return [entity for entity in self._entities.values() if entity.client_id == client_id]


class FakeClientHoldingLinksRepository(AbstractClientHoldingLinkRepository, FakeRepository):
    async def are_linked(self, client_id: int, holding_id: int) -> bool:
        for entity in self._entities.values():
            if entity.client_id == client_id and entity.client_holding_id == holding_id:
                return True
        return False

    async def bulk_remove(self, client_ids: Iterable[int], holding_id: int) -> None:
        raise NotImplementedError


class FakeHoldingsRepository(AbstractHoldingRepository, FakeRepository):
    async def get_by_client_id(self, client_id: int) -> list[ClientHoldingModel]:
        client_holdings = []
        for holding_link in self._entities.values():
            if holding_link.client_id == client_id:
                client_holdings.append(holding_link.holding)
        return client_holdings

    async def add(self, holding: ClientHoldingModel):
        if hasattr(holding, "id"):
            holding.id = self._next_id
            self._next_id += 1
        self._entities[holding.id] = holding

    async def get(self, holding_id: int) -> ClientHoldingModel | None:
        return self._entities.get(holding_id)

    async def get_account(self, account_id: int) -> ClientHoldingModel | None:
        raise NotImplementedError

    async def get_by_account_number(self, account_number: str) -> ClientHoldingModel:
        raise NotImplementedError

    async def update_portfolio_model(self, holding_id: int, new_portfolio_model_id: int) -> None:
        raise NotImplementedError


class FakeMatchRepository(AbstractMatchRepository, FakeRepository):
    async def get_by_pair(self, payment_id: int, statement_id: int) -> Entity:
        for entity in self._entities.values():
            if entity.payment_id == payment_id and entity.statement_id == statement_id:
                return entity
        return None

    async def get_by_payment_id(self, payment_id: int) -> PaymentStatementMatchModel | None:
        for entity in self._entities.values():
            if entity.payment_id == payment_id:
                return entity
        return None

    async def get_by_statement_id(self, statement_id: int) -> PaymentStatementMatchModel | None:
        for entity in self._entities.values():
            if entity.statement_id == statement_id:
                return entity
        return None


class FakePaymentRepository(AbstractPaymentRepository, FakeRepository):
    async def get_unmatched(self) -> list[PaymentsModel]:
        return list(self._entities.values())


class FakeStatementRepository(AbstractStatementRepository, FakeRepository):
    async def get_unmatched(self) -> list[StatementModel]:
        return list(self._entities.values())


class FakeProviderRepository(AbstractProviderRepository, FakeRepository):
    async def get_by_name(self, name: str) -> ProviderModel:
        return next(entity for entity in self._entities.values() if entity.name == name)


class FakeFeePaidRepository(AbstractFeePaidRepository, FakeRepository):
    async def get_by_match_id(self, match_id: int) -> FeesPaidModel:
        return [entity for entity in self._entities.values() if entity.fee_match_id == match_id]

    async def remove_by_match_id(self, match_id: int) -> None:
        raise NotImplementedError

    async def get_holdings_for_recent_fees(self, months: int = 12) -> list:
        raise NotImplementedError


class FakeClientHoldingExpectedFeeRepository(
    AbstractClientHoldingExpectedFeeRepository, FakeRepository
):
    async def get_fees_for_account(self, account_id: int) -> list[ExpectedFeeDTO]:
        return [
            ExpectedFeeDTO(
                id=entity.id,
                case_goal_id=entity.case_goal_id,
                amount=entity.amount,
                fee_type=entity.fee_type,
                due_date=entity.due_date,
            )
            for entity in self._entities.values()
            if entity.holding_id == account_id
        ]

    async def get_fees_for_goal(self, case_goal_id: int) -> list[ExpectedFeeDTO]:
        return [
            ExpectedFeeDTO(
                id=entity.id,
                case_goal_id=entity.case_goal_id,
                amount=entity.amount,
                fee_type=entity.fee_type,
                due_date=entity.due_date,
            )
            for entity in self._entities.values()
            if entity.case_goal_id == case_goal_id
        ]

    async def get_account_fees_for_goal(
        self, case_goal_id: int, account_id: int
    ) -> list[ExpectedFeeDTO]:
        return [
            ExpectedFeeDTO(
                id=entity.id,
                case_goal_id=entity.case_goal_id,
                amount=entity.amount,
                fee_type=entity.fee_type,
                due_date=entity.due_date,
            )
            for entity in self._entities.values()
            if entity.case_goal_id == case_goal_id and entity.holding_id == account_id
        ]

    async def add_account_fee_estimations(
        self,
        *,
        case_goal_id: int,
        account_id: int,
        new_fees: Iterable[ExpectedFeeDTO],
        is_final: bool,
    ) -> None:
        if is_final:
            estimate_type = enums.HoldingFeeType.FINAL
        else:
            estimate_type = enums.HoldingFeeType.ESTIMATE

        entities = []
        for fee in new_fees:
            entities.append(
                ClientHoldingExpectedFeeModel(
                    case_goal_id=case_goal_id,
                    amount=fee.amount,
                    due_date=fee.due_date,
                    fee_type=fee.fee_type,
                    holding_id=account_id,
                    estimate_type=estimate_type,
                )
            )
        await self.bulk_add(entities)

    async def update_fee_estimations(
        self, updated_fees: Iterable[ExpectedFeeDTO], is_final: bool
    ) -> None:
        if is_final:
            estimate_type = enums.HoldingFeeType.FINAL
        else:
            estimate_type = enums.HoldingFeeType.ESTIMATE

        for fee in updated_fees:
            existing_entity = self._entities[fee.id]  # type: ignore
            existing_entity.amount = fee.amount
            existing_entity.due_date = fee.due_date
            existing_entity.fee_tyoe = fee.fee_type
            existing_entity.estimate_type = estimate_type

    async def delete_fee_estimations(self, fee_ids: Iterable[int]) -> None:
        for id in fee_ids:
            del self._entities[id]


class FakeFeeSplitRepository(FakeRepository, AbstractFeeSplitTemplateRepository):
    async def add(self, entity: FeeSplitTemplate) -> int:  # type: ignore
        await super().add(entity)
        return self._next_id - 1

    async def get_by_id(self, fee_split_id: int) -> FeeSplitTemplate:
        return await self.get(fee_split_id)

    async def persist(self, entity: FeeSplitTemplate):
        raise NotImplementedError()

    async def persist_all(self):
        raise NotImplementedError()


class FakeOwnersRepository(AbstractOwnerRepository, FakeRepository):
    async def get_by_email(self, email: str) -> OwnerModel | None:
        raise NotImplementedError

    async def get_by_user_id(self, user_id: int) -> AdministratorDTO:
        owner = await self.get(user_id)
        return owner.to_dto()  # type: ignore

    async def filter_out_exiting_admin_ids(self, ids: list[int]) -> list[int]:
        return [id_ for id_ in ids if id_ not in self._entities]


class FakeUserRepository(AbstractUserRepository, FakeRepository):
    async def get_by_ids(self, ids: list[int]) -> list[UserDTO]:
        return [u for u in self._entities.values() if u.id in ids]

    async def get_by_email(self, email: str) -> UserDTO | None:
        try:
            return [u for u in self._entities.values() if u.email == email][0]
        except IndexError:
            return None

    async def get_by_cognito_id(self, cognito_id: str) -> UserDTO | None:
        for u in self._entities.values():
            if u.id == cognito_id:
                return u
        return None


class FakeDocumentTemplateRepository(AbstractDocumentTemplateRepository, FakeRepository):
    async def add(self, template: DocumentGenerationTemplate) -> None:
        self._entities[template.document_type] = template

    async def get(self, document_type: str) -> DocumentGenerationTemplate | None:  # type: ignore
        return self._entities.get(document_type, None)

    async def upsert_template(
        self,
        document_type: DocumentType,
        description: str,
        template_url: str,
    ) -> None:
        raise NotImplementedError


class FakeClientAddressRepository(AbstractClientAddressRepository, FakeRepository):
    def __init__(self):
        super().__init__()
        self.addresses: dict[int, list[AddressDTO]] = defaultdict(list)
        self.address_updates: dict[int, AddressDTO] = {}

    async def add_address(self, client_id: int, dto: AddressDTO) -> None:
        self.addresses[client_id].append(dto)

    async def update_address(self, client_id: int, address_id: int, dto: AddressDTO) -> None:
        self.address_updates[address_id] = dto


class FakeClientRelationsRepository(AbstractClientRelationsRepository, FakeRepository):
    def __init__(self) -> None:
        super().__init__()
        self._relations: dict[int, Any] = {}
        self._next_relation_id = 1

    async def add_relation(self, relation: AddClientRelationDTO) -> None:
        relation_model = {
            "id": self._next_relation_id,
            "client_id": relation.client_id,
            "first_name": relation.first_name,
            "last_name": relation.last_name,
            "date_of_birth": relation.date_of_birth,
            "relationship_type": relation.relationship_type,
        }
        self._relations[self._next_relation_id] = relation_model
        self._next_relation_id += 1

    async def update_relation(self, relation: ClientRelationDTO) -> None:
        if relation.id in self._relations:
            self._relations[relation.id].update(
                {
                    "first_name": relation.first_name,
                    "last_name": relation.last_name,
                    "date_of_birth": relation.date_of_birth,
                    "relationship_type": relation.relationship_type,
                }
            )


class FakeUnitOfWork(AbstractUnitOfWork):
    def __init__(self, *args, **kwargs) -> None:
        self.mock_session = AsyncMock(spec=AsyncSessionInterface)
        self._session: AsyncMock | None = None

        self._aml_monitoring_sessions2: FakeRepository | None = None
        self._aml_searches2: FakeRepository | None = None
        self._advice: FakeAdviceRepository | None = None
        self._advice_types: FakeRepository | None = None
        self._cases2: Mock = Mock(spec=SQLAlchemyCaseRepository)
        self._case_events: Mock = Mock(spec=AbstractEventRepository)
        self._cashflow_types: FakeRepository | None = None
        self._clients: FakeRepository | None = None
        self._clients2: Mock = Mock(spec=SQLAlchemyClientRepository)
        self._client_holding_links: FakeClientHoldingLinksRepository | None = None
        self._client_links: FakeRepository | None = None
        self._client_goals: FakeRepository | None = None
        self._client_selected_goals: FakeRepository | None = None
        self._countries: FakeRepository | None = None
        self._fee_paid: FakeFeePaidRepository | None = None
        self._fee_split_templates: FakeFeeSplitRepository | None = None
        self._goals: FakeRepository | None = None
        self._holdings: FakeHoldingsRepository | None = None
        self._holdings2: Mock = Mock(spec=SQLAlchemyHoldingsRepository)
        self._holdings_plan_info: FakeRepository | None = None
        self._matches: FakeMatchRepository | None = None
        self._nationalities: FakeRepository | None = None
        self._owners: FakeOwnersRepository | None = None
        self._payments: FakePaymentRepository | None = None
        self._products: FakeRepository | None = None
        self._providers: FakeProviderRepository | None = None
        self._tasks: FakeRepository | None = None
        self._users: FakeUserRepository | None = None
        self._client_fees: FakeClientHoldingExpectedFeeRepository | None = None
        self._review_calendars: FakeRepository | None = None
        self._document_templates: FakeDocumentTemplateRepository | None = None
        self._statements: FakeStatementRepository | None = None
        self._risk_profiles: FakeRepository | None = None
        self._forecaster_plans: FakeRepository | None = None
        self._review_group: FakeRepository | None = None
        self._genders: FakeRepository | None = None
        self._marital_statuses: FakeRepository | None = None
        self._product_types: FakeRepository | None = None
        self._personal_titles: FakeRepository | None = None
        self._portfolio_models: FakeRepository | None = None
        self._tasks2: FakeRepository | None = None
        self._documents: FakeRepository | None = None
        self._client_notes: FakeRepository | None = None
        self._client_relations: FakeClientRelationsRepository | None = None
        self._expected_fees: Mock = Mock(spec=SQLAlchemyExpectedFeeRepository)
        self._client_addresses: FakeClientAddressRepository | None = None

    def _raise_for_no_session(self) -> None:
        if self._session is None:
            raise RuntimeError(
                "Session is not initialized. Please use the Unit of Work context manager."
            )

    @property
    def aml_monitoring_sessions2(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._aml_monitoring_sessions2 is None:
            self._aml_monitoring_sessions2 = FakeRepository()
        return self._aml_monitoring_sessions2

    @property
    def advice(self) -> FakeAdviceRepository:
        self._raise_for_no_session()
        if self._advice is None:
            self._advice = FakeAdviceRepository()
        return self._advice

    @property
    def aml_searches2(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._aml_searches2 is None:
            self._aml_searches2 = FakeRepository()
        return self._aml_searches2

    @property
    def advice_types(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._advice_types is None:
            self._advice_types = FakeRepository()
        return self._advice_types

    @property
    def cases2(self) -> Mock:
        self._raise_for_no_session()
        return self._cases2

    @property
    def cases_old(self) -> Mock:
        self._raise_for_no_session()
        return self._cases_old

    @property
    def case_events(self) -> FakeRepository:
        self._raise_for_no_session()
        return self._case_events

    @property
    def cashflow_types(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._cashflow_types is None:
            self._cashflow_types = FakeRepository()
        return self._cashflow_types

    @property
    def clients(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._clients is None:
            self._clients = FakeRepository()
        return self._clients

    @property
    def clients2(self) -> Mock:
        self._raise_for_no_session()
        return self._clients2

    @property
    def client_holding_links(self) -> FakeClientHoldingLinksRepository:
        self._raise_for_no_session()
        if self._client_holding_links is None:
            self._client_holding_links = FakeClientHoldingLinksRepository()
        return self._client_holding_links

    @property
    def client_links(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._client_links is None:
            self._client_links = FakeRepository()
        return self._client_links

    @property
    def client_goals(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._client_goals is None:
            self._client_goals = FakeRepository()
        return self._client_goals

    @property
    def client_selected_goals(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._client_selected_goals is None:
            self._client_selected_goals = FakeRepository()
        return self._client_selected_goals

    @property
    def countries(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._countries is None:
            self._countries = FakeRepository()
        return self._countries

    @property
    def fee_paid(self) -> FakeFeePaidRepository:
        self._raise_for_no_session()
        if self._fee_paid is None:
            self._fee_paid = FakeFeePaidRepository()
        return self._fee_paid

    @property
    def fee_split_templates(self) -> FakeFeeSplitRepository:
        self._raise_for_no_session()
        if self._fee_split_templates is None:
            self._fee_split_templates = FakeFeeSplitRepository()
        return self._fee_split_templates

    @property
    def goals(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._goals is None:
            self._goals = FakeRepository()
        return self._goals

    @property
    def holdings(self) -> FakeHoldingsRepository:
        self._raise_for_no_session()
        if self._holdings is None:
            self._holdings = FakeHoldingsRepository()
        return self._holdings

    @property
    def holdings2(self) -> Mock:
        self._raise_for_no_session()
        return self._holdings2

    @property
    def holdings_plan_info(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._holdings_plan_info is None:
            self._holdings_plan_info = FakeRepository()
        return self._holdings_plan_info

    @property
    def matches(self) -> FakeMatchRepository:
        self._raise_for_no_session()
        if self._matches is None:
            self._matches = FakeMatchRepository()
        return self._matches

    @property
    def nationalities(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._nationalities is None:
            self._nationalities = FakeRepository()
        return self._nationalities

    @property
    def owners(self) -> FakeOwnersRepository:
        self._raise_for_no_session()
        if self._owners is None:
            self._owners = FakeOwnersRepository()
        return self._owners

    @property
    def payments(self) -> FakePaymentRepository:
        self._raise_for_no_session()
        if self._payments is None:
            self._payments = FakePaymentRepository()
        return self._payments

    @property
    def products(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._products is None:
            self._products = FakeRepository()
        return self._products

    @property
    def providers(self) -> FakeProviderRepository:
        self._raise_for_no_session()
        if self._providers is None:
            self._providers = FakeProviderRepository()
        return self._providers

    @property
    def tasks(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._tasks is None:
            self._tasks = FakeRepository()
        return self._tasks

    @property
    def users(self) -> FakeUserRepository:
        self._raise_for_no_session()
        if self._users is None:
            self._users = FakeUserRepository()
        return self._users

    @property
    def client_fees(self) -> FakeClientHoldingExpectedFeeRepository:
        self._raise_for_no_session()
        if self._client_fees is None:
            self._client_fees = FakeClientHoldingExpectedFeeRepository()
        return self._client_fees

    @property
    def review_calendars(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._review_calendars is None:
            self._review_calendars = FakeRepository()
        return self._review_calendars

    @property
    def document_templates(self) -> FakeDocumentTemplateRepository:
        self._raise_for_no_session()
        if self._document_templates is None:
            self._document_templates = FakeDocumentTemplateRepository()
        return self._document_templates

    @property
    def statements(self) -> FakeStatementRepository:
        self._raise_for_no_session()
        if self._statements is None:
            self._statements = FakeStatementRepository()
        return self._statements

    @property
    def risk_profiles(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._risk_profiles is None:
            self._risk_profiles = FakeRepository()
        return self._risk_profiles

    @property
    def forecaster_plans(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._forecaster_plans is None:
            self._forecaster_plans = FakeRepository()
        return self._forecaster_plans

    @property
    def review_group(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._review_group is None:
            self._review_group = FakeRepository()
        return self._review_group

    @property
    def genders(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._genders is None:
            self._genders = FakeRepository()
        return self._genders

    @property
    def marital_statuses(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._marital_statuses is None:
            self._marital_statuses = FakeRepository()
        return self._marital_statuses

    @property
    def product_types(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._product_types is None:
            self._product_types = FakeRepository()
        return self._product_types

    @property
    def personal_titles(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._personal_titles is None:
            self._personal_titles = FakeRepository()
        return self._personal_titles

    @property
    def portfolio_models(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._portfolio_models is None:
            self._portfolio_models = FakeRepository()
        return self._portfolio_models

    @property
    def tasks2(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._tasks2 is None:
            self._tasks2 = FakeRepository()
        return self._tasks2

    @property
    def documents(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._documents is None:
            self._documents = FakeRepository()
        return self._documents

    @property
    def client_notes(self) -> FakeRepository:
        self._raise_for_no_session()
        if self._client_notes is None:
            self._client_notes = FakeRepository()
        return self._client_notes

    @property
    def client_relations(self) -> FakeClientRelationsRepository:
        self._raise_for_no_session()
        if self._client_relations is None:
            self._client_relations = FakeClientRelationsRepository()
        return self._client_relations

    @property
    def expected_fees(self) -> FakeRepository:
        self._raise_for_no_session()
        return self._expected_fees

    @property
    def client_addresses(self) -> FakeClientAddressRepository:
        self._raise_for_no_session()
        if self._client_addresses is None:
            self._client_addresses = FakeClientAddressRepository()
        return self._client_addresses

    async def __aenter__(self) -> Self:
        self._session = self.mock_session
        return self

    async def __aexit__(self, *args) -> None:
        self._session = None

    async def add_activity(
        self,
        activity_type: ActivityType,
        obj: object | None = None,
        target: object | None = None,
    ) -> None:
        pass
