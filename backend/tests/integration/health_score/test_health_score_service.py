from datetime import date, timed<PERSON><PERSON>
from decimal import Decimal
from functools import partial
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest
from financial_models.health_score.goals import (
    BuildWealthGoal,
    EmergencyFundGoal,
    EstatePlanningGoal,
    InvestForChildrenGoal,
    PropertyGoal,
    ProtectGoal,
    RetirementGoal,
)
from financial_models.simulator.account import (
    Account,
    FinalSalaryPension,
    MortgageAccount,
    ProtectPolicy,
)
from financial_models.simulator.enums import (
    CashflowType as ModelCashflowType,
)
from financial_models.simulator.enums import (
    Frequency as ModelFrequency,
)
from financial_models.simulator.enums import ItemStatus, ProductType
from financial_models.simulator.operators.cashflow_operator import Cashflow
from sqlalchemy.ext.asyncio import AsyncSession

from api.enums import (
    CashflowType,
    ClientStatus,
    Frequency,
    Goals,
    ProductTypeType,
    YesNoNeedsUpdating,
)
from app.clients.dtos.factfind_dtos import GoalTypeAttributesDTO
from app.clients.services.view_factfind import GetFactfindDataService
from app.health_score.services.health_score_service import HealthScoreService
from database.models import (
    ClientFactfindModel,
    ClientIncomeExpenditureModel,
    ClientModel,
    IncomeExpenditureGroupsModel,
    IncomeExpenditureTypesModel,
    ProductModel,
    ProductTypeGroupsModel,
    ProductTypeModel,
)
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork
from tests.insert_manager.insert_manager import InsertManager


@pytest.fixture
def mock_factfind_service() -> AsyncMock:
    return AsyncMock(spec=GetFactfindDataService)


@pytest.fixture
async def seed_product_type_groups(async_session: AsyncSession) -> None:
    async with async_session:
        for group_id in range(1, 13):
            async_session.add(ProductTypeGroupsModel(id=group_id))
        await async_session.commit()


async def insert_client(
    date_of_birth: date,
    session: AsyncSession,
    insert_manager: InsertManager,
    retirement_age: int | None = None,
    qualified_for_state_pension: bool | None = None,
    monthly_retirement_income_required: Decimal | None = None,
    current_annual_income: Decimal | None = None,
    will_status: YesNoNeedsUpdating | None = None,
    lpa_status: YesNoNeedsUpdating | None = None,
) -> int:
    client_data = await insert_manager.generate_random_client()
    client_data["client_status_id"] = ClientStatus.ActiveOngoing
    client_data["date_of_birth"] = date_of_birth
    async with session:
        client = ClientModel(**client_data)
        factfind = ClientFactfindModel(
            client_id=client.id,
            retirement_age=retirement_age,
            monthly_retirement_income_required=monthly_retirement_income_required,
            state_pension=qualified_for_state_pension,
            has_will_in_place=will_status,
            has_power_of_attorney_in_place=lpa_status,
        )
        client.factfind = factfind
        if current_annual_income is not None:
            client.cashflows.append(
                ClientIncomeExpenditureModel(
                    client_id=client.id,
                    income_expenditure_type_id=1,
                    amount=float(current_annual_income),
                    frequency=Frequency.Yearly,
                    name="Current Annual Income",
                )
            )
        session.add(client)
        await session.flush()
        client_id = client.id
        await session.commit()
    return client_id


async def insert_holding(
    client_id: int,
    product_type: ProductTypeType,
    product_type_id: int,
    product_type_group_id: int,
    monthly_payment_direction: str,
    monthly_payment_amount: float,
    risk_level: int,
    session: AsyncSession,
    insert_manager: InsertManager,
    is_secured_against_address: bool | None = None,
    mortgage_end_date: date | None = None,
    interest_rate: Decimal | None = None,
    mortgage_product_end_date: date | None = None,
    monthly_payment: Decimal | None = None,
    cover_amount: Decimal | None = None,
    policy_end_date: date | None = None,
    index_linked: bool | None = None,
    accrual_rate: Decimal | None = None,
    predicted_final_salary: Decimal | None = None,
    predicted_years_of_service_at_retirement: int | None = None,
) -> int:
    async with session:
        product = ProductModel(
            product_type=ProductTypeModel(
                id=product_type_id, type=product_type, product_type_group_id=product_type_group_id
            ),
            name="Test Product",
            attributes={
                "monthly_payment_direction": monthly_payment_direction,
                "monthly_payment_amount": monthly_payment_amount,
                "risk_level": risk_level,
            },
            synonyms={},
        )
        if product_type == ProductTypeType.Mortgage:
            product.attributes["mortgage_end_date"] = mortgage_end_date
            product.attributes["interest_rate"] = interest_rate
            product.attributes["mortgage_product_end_date"] = mortgage_product_end_date
            product.attributes["monthly_payment"] = monthly_payment
            product.attributes["secured_against_address"] = 42
        elif product_type in (
            ProductTypeType.TermPolicy,
            ProductTypeType.IndemnityPolicy,
            ProductTypeType.WholeOfLifePolicy,
            ProductTypeType.IncomeProtectionPolicy,
        ):
            product.attributes["cover_amount"] = cover_amount
            product.attributes["policy_end_date"] = policy_end_date
        elif product_type == ProductTypeType.DefinedBenefitPension:
            product.attributes["is_current_job"] = False
            product.attributes["index_linked"] = index_linked
            product.attributes["accrual_rate"] = accrual_rate
            product.attributes["predicted_final_salary"] = predicted_final_salary
            product.attributes["predicted_years_of_service_at_retirement"] = (
                predicted_years_of_service_at_retirement
            )

        session.add(product)
        await session.flush()
        product_id = product.id
        await session.commit()

    holding_data = await insert_manager.insert_random_holding(
        client_id=client_id, product_id=product_id
    )
    return holding_data["id"]


@pytest.fixture
def add_client(async_session: AsyncSession, insert_manager: InsertManager):
    return partial(insert_client, session=async_session, insert_manager=insert_manager)


@pytest.fixture
def add_holding(async_session: AsyncSession, insert_manager: InsertManager):
    return partial(insert_holding, session=async_session, insert_manager=insert_manager)


@pytest.fixture
def health_score_service(
    uow2: SQLAlchemyUnitOfWork, mock_factfind_service: AsyncMock
) -> HealthScoreService:
    service = HealthScoreService(uow=uow2, ts_query_session=Mock(), logger=Mock())
    service.factfind_service = mock_factfind_service
    return service


@pytest.fixture
async def seed_income_type(async_session: AsyncSession) -> None:
    async with async_session:
        group = IncomeExpenditureGroupsModel(
            type=CashflowType.Income,
            name="Income",
            description="Some Income",
        )
        type_ = IncomeExpenditureTypesModel(
            income_expenditure_group=group,
            name="Salary",
            sort_order=1,
            is_active=True,
            is_essential=True,
        )
        async_session.add(type_)
        await async_session.commit()


@pytest.mark.usefixtures("seed_goals", "seed_product_type_groups")
async def test_load_client_returns_expected_client_model_emergency_fund(
    health_score_service: HealthScoreService,
    mock_factfind_service: AsyncMock,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1), retirement_age=65, qualified_for_state_pension=True
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=58,  # Savings Account
        product_type_group_id=6,  # Bank & Building Society
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.EmergencyFund,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.EmergencyFund),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        holding_id: {"valuation": Decimal(1000)}
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert client.client_id == client_id

    assert len(client.goals) == 1

    # Emergency Fund Goal assertions
    assert isinstance(client.goals[0], EmergencyFundGoal)
    emergency_fund = client.goals[0]
    assert emergency_fund.monthly_expenses == Decimal(0)
    assert emergency_fund.linked_accounts == [
        Account(
            holding_id=holding_id,
            cashflows=[
                Cashflow(
                    name="Contribution",
                    cashflow_type=ModelCashflowType.CREDIT,
                    value=Decimal(100),
                    frequency=ModelFrequency.MONTHLY,
                    base_date=date.today().replace(day=1),
                    inflation_rates=health_score_service.rate_manager.inflation_ts,
                )
            ],
            risk_level=1,
            balance=Decimal(1000),
            product_type=ProductType.SAVINGS_ACCOUNT,
            rate_manager=health_score_service.rate_manager,
            is_subject_to_inflation=True,
            inflation_frequency=ModelFrequency.ANNUALLY,
        )
    ]
    assert emergency_fund.rate_manager == health_score_service.rate_manager
    assert errors == {}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals")
async def test_load_client_returns_expected_client_model_estate_planning(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        will_status=YesNoNeedsUpdating.Yes,
        lpa_status=YesNoNeedsUpdating.NeedsUpdating,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.EstatePlanningAndWills,
        linked_holding_ids=[],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.EstatePlanningAndWills),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert isinstance(client.goals[0], EstatePlanningGoal)
    estate_planning = client.goals[0]
    assert estate_planning.will_status == ItemStatus.YES
    assert estate_planning.lpa_status == ItemStatus.NEEDS_UPDATING
    assert errors == {}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_product_type_groups")
async def test_load_client_returns_expected_client_model_invest_for_children(
    health_score_service: HealthScoreService,
    mock_factfind_service: AsyncMock,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        will_status=YesNoNeedsUpdating.Yes,
        lpa_status=YesNoNeedsUpdating.NeedsUpdating,
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=5,  # Current Account
        product_type_group_id=6,  # Bank accounts
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.InvestForChildren,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(
            type_=Goals.InvestForChildren,
            target_amount=1000,
            target_date=date.today() + timedelta(days=365),
        ),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        holding_id: {"valuation": Decimal(1000)}
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert isinstance(client.goals[0], InvestForChildrenGoal)
    invest_for_children = client.goals[0]
    assert invest_for_children.target_value == Decimal(1000)
    assert invest_for_children.target_date == date.today() + timedelta(days=365)
    assert invest_for_children.linked_accounts == [
        Account(
            holding_id=holding_id,
            cashflows=[
                Cashflow(
                    name="Contribution",
                    cashflow_type=ModelCashflowType.CREDIT,
                    value=Decimal(100),
                    frequency=ModelFrequency.MONTHLY,
                    base_date=date.today().replace(day=1),
                    inflation_rates=health_score_service.rate_manager.inflation_ts,
                )
            ],
            risk_level=1,
            balance=Decimal(1000),
            product_type=ProductType.CURRENT_ACCOUNT,
            rate_manager=health_score_service.rate_manager,
            is_subject_to_inflation=True,
            inflation_frequency=ModelFrequency.ANNUALLY,
        )
    ]
    assert invest_for_children.rate_manager == health_score_service.rate_manager
    assert errors == {}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_product_type_groups")
async def test_load_client_returns_expected_client_model_build_wealth(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        will_status=YesNoNeedsUpdating.Yes,
        lpa_status=YesNoNeedsUpdating.NeedsUpdating,
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.CryptoCurrency,
        product_type_id=72,  # Crypto
        product_type_group_id=1,  # Investment
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=5,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.BuildWealth,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(
            type_=Goals.BuildWealth,
            target_amount=1000,
            target_date=date.today() + timedelta(days=365),
        ),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert isinstance(client.goals[0], BuildWealthGoal)
    build_wealth = client.goals[0]
    assert build_wealth.target_value == Decimal(1000)
    assert build_wealth.target_date == date.today() + timedelta(days=365)
    assert build_wealth.linked_accounts == [
        Account(
            holding_id=holding_id,
            cashflows=[
                Cashflow(
                    name="Contribution",
                    cashflow_type=ModelCashflowType.CREDIT,
                    value=Decimal(100),
                    frequency=ModelFrequency.MONTHLY,
                    base_date=date.today().replace(day=1),
                    inflation_rates=health_score_service.rate_manager.inflation_ts,
                )
            ],
            risk_level=5,
            balance=Decimal(0),
            product_type=ProductType.INVESTMENT,
            rate_manager=health_score_service.rate_manager,
            is_subject_to_inflation=True,
            inflation_frequency=ModelFrequency.ANNUALLY,
        )
    ]
    assert build_wealth.rate_manager == health_score_service.rate_manager
    assert errors == {}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_product_type_groups")
async def test_load_client_returns_expected_client_model_property_no_ownership(
    health_score_service: HealthScoreService,
    mock_factfind_service: AsyncMock,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(date_of_birth=date(1990, 1, 1))
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=16,  # Cash ISA
        product_type_group_id=1,  # Investment
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.PropertyOwnership,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(
            type_=Goals.PropertyOwnership,
            target_amount=1000,
            target_date=date.today() + timedelta(days=365),
        ),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        holding_id: {"valuation": Decimal(1000)}
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert isinstance(client.goals[0], PropertyGoal)
    property_goal = client.goals[0]
    assert property_goal.target_value == Decimal(1000)
    assert property_goal.target_date == date.today() + timedelta(days=365)
    assert property_goal.linked_accounts == [
        Account(
            holding_id=holding_id,
            cashflows=[
                Cashflow(
                    name="Contribution",
                    cashflow_type=ModelCashflowType.CREDIT,
                    value=Decimal(100),
                    frequency=ModelFrequency.MONTHLY,
                    base_date=date.today().replace(day=1),
                    inflation_rates=health_score_service.rate_manager.inflation_ts,
                )
            ],
            risk_level=1,
            balance=Decimal(1000),
            product_type=ProductType.ISA_CASH,
            rate_manager=health_score_service.rate_manager,
            is_subject_to_inflation=True,
            inflation_frequency=ModelFrequency.ANNUALLY,
        )
    ]
    assert property_goal.rate_manager == health_score_service.rate_manager
    assert errors == {}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_product_type_groups")
async def test_load_client_returns_expected_client_model_property_with_ownership(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
    mock_factfind_service: AsyncMock,
) -> None:
    client_id = await add_client(date_of_birth=date(1990, 1, 1))
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Mortgage,
        product_type_id=24,  # Mortgage
        product_type_group_id=11,  # Mortgage
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
        mortgage_end_date=date(2050, 1, 1),
        interest_rate=Decimal(0.05),
        mortgage_product_end_date=date(2050, 1, 1),
        monthly_payment=Decimal(100),
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.PropertyOwnership,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.PropertyOwnership),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        42: {"valuation": Decimal(500000)}  # Fake property value
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert isinstance(client.goals[0], PropertyGoal)
    property_goal = client.goals[0]
    assert property_goal.linked_accounts == [
        MortgageAccount(
            holding_id=holding_id,
            monthly_payment=Decimal(100),
            product_end_date=date(2050, 1, 1),
            interest_rate=Decimal(0.05),
            end_date=date(2050, 1, 1),
            risk_level=-1,
            balance=Decimal(0),
            rate_manager=health_score_service.rate_manager,
            linked_property_id=42,
        )
    ]
    assert property_goal.rate_manager == health_score_service.rate_manager
    assert errors == {}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
async def test_load_client_returns_expected_client_model_retirement(
    health_score_service: HealthScoreService,
    mock_factfind_service: AsyncMock,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        qualified_for_state_pension=True,
        retirement_age=65,
        monthly_retirement_income_required=Decimal(1000),
        current_annual_income=Decimal(25000),
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=31,  # Occupational Pension Scheme
        product_type_group_id=4,  # Pension
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    final_salary_pension_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.DefinedBenefitPension,
        product_type_id=6,  # Final Salary Pension
        product_type_group_id=2,  # Pension - income paying
        monthly_payment_direction="contribution",
        monthly_payment_amount=0,  # N/A
        risk_level=0,  # N/A
        index_linked=True,
        accrual_rate=Decimal(0.05),
        predicted_final_salary=Decimal(100000),
        predicted_years_of_service_at_retirement=30,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.Retirement,
        linked_holding_ids=[holding_id, final_salary_pension_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.Retirement),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        holding_id: {"valuation": Decimal(1000)}
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert isinstance(client.goals[0], RetirementGoal)
    retirement = client.goals[0]
    assert retirement.retirement_date == date(2055, 1, 1)
    assert retirement.monthly_income_required == Decimal(1000)
    assert retirement.qualified_for_state_pension is True
    assert retirement.life_expectancy == 88
    assert retirement.current_annual_income == Decimal(25000)
    assert retirement.linked_accounts == [
        Account(
            holding_id=holding_id,
            cashflows=[
                Cashflow(
                    name="Contribution",
                    cashflow_type=ModelCashflowType.CREDIT,
                    value=Decimal(100),
                    frequency=ModelFrequency.MONTHLY,
                    base_date=date.today().replace(day=1),
                    inflation_rates=health_score_service.rate_manager.inflation_ts,
                )
            ],
            risk_level=1,
            balance=Decimal(1000),
            product_type=ProductType.PENSION,
            rate_manager=health_score_service.rate_manager,
            is_subject_to_inflation=True,
            inflation_frequency=ModelFrequency.ANNUALLY,
        )
    ]
    assert retirement.final_salary_pensions == [
        FinalSalaryPension(
            holding_id=final_salary_pension_id,
            index_linked=True,
            accrual_rate=Decimal(0.05),
            final_salary=Decimal(100000),
            years_of_service=30,
        )
    ]
    assert retirement.rate_manager == health_score_service.rate_manager
    assert errors == {}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_product_type_groups", "seed_income_type")
async def test_load_client_returns_expected_client_model_protect_goal(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
    mock_factfind_service: AsyncMock,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        retirement_age=65,
        current_annual_income=Decimal(25000),
    )
    # Add critical illness policy
    policy_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.TermPolicy,
        product_type_id=4,  # Critical illness cover
        product_type_group_id=5,  # Protection
        cover_amount=Decimal(100000),
        policy_end_date=date(2030, 1, 1),
        monthly_payment_direction="contribution",  # N/A
        monthly_payment_amount=100,  # N/A
        risk_level=1,  # N/A
    )
    # Add two debts with valuations
    debt_1_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.PersonalLoan,
        product_type_id=992,  # N/A
        product_type_group_id=10,  # Loan
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=0,
    )
    debt_2_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.CreditCard,
        product_type_id=993,  # N/A
        product_type_group_id=12,  # Other debt
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=0,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.ProtectYourselfAndFamily,
        linked_holding_ids=[policy_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.ProtectYourselfAndFamily),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        debt_1_id: {"valuation": Decimal(10000)},
        debt_2_id: {"valuation": Decimal(2500)},
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert isinstance(client.goals[0], ProtectGoal)
    protect = client.goals[0]
    assert protect.monthly_income.quantize(Decimal("0.01")) == Decimal(25000 / 12).quantize(
        Decimal("0.01")
    )
    assert protect.debt_balance == Decimal(12500)
    assert protect.retirement_date == date(2055, 1, 1)
    assert protect.linked_policies == [
        ProtectPolicy(
            holding_id=policy_id,
            balance=Decimal(0),
            product_type=ProductType.CRITICAL_ILLNESS_COVER,
            policy_end_date=date(2030, 1, 1),
            cover_amount=Decimal(100000),
        )
    ]


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
async def test_load_client_returns_expected_errors_for_no_linked_holding(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        qualified_for_state_pension=True,
        retirement_age=65,
        monthly_retirement_income_required=Decimal(1000),
        current_annual_income=Decimal(25000),
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.BuildWealth,
        linked_holding_ids=[],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.BuildWealth),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {
        1: "Please link an account to enable the score calculation.",
    }
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
async def test_load_client_returns_expected_warnings_shared_holding(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        qualified_for_state_pension=True,
        retirement_age=65,
        monthly_retirement_income_required=Decimal(1000),
        current_annual_income=Decimal(25000),
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=999,
        product_type_group_id=2,  # Pension
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.Retirement,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.Retirement),
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.BuildWealth,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.BuildWealth),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {
        1: "Please link an account to enable the score calculation.",
        2: "Please link an account to enable the score calculation.",
    }
    assert warnings == {
        1: {
            1: "This holding has been added to more than one goal, each holding can "
            "only be linked to a single goal.",
        },
        2: {
            1: "This holding has been added to more than one goal, each holding can "
            "only be linked to a single goal.",
        },
    }


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
async def test_load_client_returns_expected_warnings_missing_investment_attributes(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        qualified_for_state_pension=True,
        retirement_age=65,
        monthly_retirement_income_required=Decimal(1000),
        current_annual_income=Decimal(25000),
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=999,
        product_type_group_id=4,  # Pension
        monthly_payment_direction=None,
        monthly_payment_amount=None,
        risk_level=None,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.BuildWealth,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.BuildWealth),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {
        1: "Please link an account to enable the score calculation.",
    }
    assert warnings == {
        1: {
            1: "Please ensure that the contribution/withdrawal amounts and risk level are specified.",
        },
    }


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
async def test_load_client_returns_expected_warnings_invalid_holding_type(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        qualified_for_state_pension=True,
        retirement_age=65,
        monthly_retirement_income_required=Decimal(1000),
        current_annual_income=Decimal(25000),
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=999,
        product_type_group_id=10,  # Loans - not supported
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.BuildWealth,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.BuildWealth),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {
        1: "Please link an account to enable the score calculation.",
    }
    assert warnings == {
        1: {
            1: "Loans holdings are not supported and have not been used to calculate the health score.",
        },
    }


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
async def test_load_client_returns_expected_error_for_missing_retirement_data(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=None,
        qualified_for_state_pension=None,
        retirement_age=None,
        monthly_retirement_income_required=None,
        current_annual_income=Decimal(25000),
    )
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=999,
        product_type_group_id=4,  # Pension
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=3,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.Retirement,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.Retirement),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {
        1: "Please complete the following fields to enable the score calculation: Date of "
        "Birth, Retirement Age, Monthly Retirement Income Required, State Pension "
        "Eligibility",
    }
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
async def test_load_client_returns_expected_error_for_missing_estate_planning_data(
    health_score_service: HealthScoreService,
    add_client,
    add_client_goal,
) -> None:
    client_id = await add_client(
        date_of_birth=date(1990, 1, 1),
        will_status=None,
        lpa_status=None,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.EstatePlanningAndWills,
        linked_holding_ids=[],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.EstatePlanningAndWills),
    )

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {
        1: "Please complete the following fields to enable the score calculation: Will Status, Power "
        "of Attorney Status",
    }
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
@pytest.mark.parametrize(
    "goal_type",
    [Goals.PropertyOwnership, Goals.BuildWealth, Goals.InvestForChildren, Goals.TravelPlanning],
)
async def test_load_client_returns_expected_error_for_missing_goal_target_amount(
    goal_type: Goals,
    health_score_service: HealthScoreService,
    add_client,
    add_client_goal,
    add_holding,
    mock_factfind_service,
) -> None:
    client_id = await add_client(date_of_birth=date(1990, 1, 1))
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=16,  # Cash ISA
        product_type_group_id=1,  # Investment
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=goal_type,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(
            type_=goal_type,
            target_date=date.today() + timedelta(days=365),
        ),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        holding_id: {"valuation": Decimal(1000)}
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {1: "Please ensure that the target value for the goal is specified."}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_income_type", "seed_product_type_groups")
@pytest.mark.parametrize(
    "goal_type",
    [Goals.PropertyOwnership, Goals.BuildWealth, Goals.InvestForChildren, Goals.TravelPlanning],
)
async def test_load_client_returns_expected_error_for_missing_goal_target_date(
    goal_type: Goals,
    health_score_service: HealthScoreService,
    add_client,
    add_client_goal,
    add_holding,
    mock_factfind_service,
) -> None:
    client_id = await add_client(date_of_birth=date(1990, 1, 1))
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Account,
        product_type_id=16,  # Cash ISA
        product_type_group_id=1,  # Investment
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=goal_type,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(
            type_=goal_type,
            target_amount=1000,
        ),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        holding_id: {"valuation": Decimal(1000)}
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {1: "Please ensure that the target date for the goal is specified."}
    assert warnings == {}


@pytest.mark.usefixtures("seed_goals", "seed_product_type_groups")
async def test_load_client_returns_expected_error_for_missing_mortgage_attributes(
    health_score_service: HealthScoreService,
    add_client,
    add_holding,
    add_client_goal,
    mock_factfind_service: AsyncMock,
) -> None:
    client_id = await add_client(date_of_birth=date(1990, 1, 1))
    holding_id = await add_holding(
        client_id=client_id,
        product_type=ProductTypeType.Mortgage,
        product_type_id=24,  # Mortgage
        product_type_group_id=11,  # Mortgage
        monthly_payment_direction="contribution",
        monthly_payment_amount=100,
        risk_level=1,
        mortgage_end_date=date(2050, 1, 1),
        interest_rate=None,  # Oops, missing interest rate
        mortgage_product_end_date=date(2050, 1, 1),
        monthly_payment=Decimal(100),
    )
    await add_client_goal(
        client_ids=[client_id],
        goal=Goals.PropertyOwnership,
        linked_holding_ids=[holding_id],
        goal_attributes=GoalTypeAttributesDTO(type_=Goals.PropertyOwnership),
    )
    mock_factfind_service.get_latest_valuation_for_holdings.return_value = {
        42: {"valuation": Decimal(500000)}  # Fake property value
    }

    client, errors, warnings = await health_score_service._load_client(client_id)

    assert errors == {1: "Please link an account to enable the score calculation."}
    assert warnings == {
        1: {
            1: "Please ensure that the monthly payment, interest rate, mortgage end "
            "date and mortgage product end date are specified.",
        },
    }
