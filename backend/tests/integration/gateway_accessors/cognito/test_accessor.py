from typing import Generator

import pytest
from botocore.exceptions import ClientError

from api.enums import UserRole
from gateway_accessors.cognito.accessor import DevCognitoGateway
from gateway_accessors.cognito.exceptions import CognitoInvalidGroups
from tests.config import get_tests_settings

settings = get_tests_settings()


@pytest.fixture
def cognito_gateway() -> DevCognitoGateway:
    """Fixture for creating a CognitoGateway instance for testing."""
    gateway = DevCognitoGateway(
        aws_region="local",
        aws_access_key_id="",
        aws_secret_access_key="",
        aws_user_pool_id="cognito_local",
        aws_endpoint_url=settings.aws_cognito_endpoint_url,
        aws_send_email_on_user_creation=False,
    )
    return gateway


@pytest.fixture
def test_user_email() -> str:
    """Test user email."""
    return "<EMAIL>"


@pytest.fixture
def setup_test_groups(cognito_gateway: DevCognitoGateway) -> Generator[list[str], None, None]:
    """Setup test groups in Cognito."""
    test_groups = [role.value for role in UserRole]

    # Create groups if they don't exist
    existing_groups = cognito_gateway.get_cognito_groups()
    for group in test_groups:
        if group not in existing_groups:
            try:
                cognito_gateway.create_group(group)
            except ClientError as e:
                if e.response["Error"]["Code"] != "GroupExistsException":
                    raise

    yield test_groups


@pytest.fixture
def setup_test_user(
    cognito_gateway: DevCognitoGateway, test_user_email: str
) -> Generator[str, None, None]:
    """Setup a test user in Cognito."""
    # Clean up any existing user first
    try:
        cognito_gateway.delete_cognito_user(test_user_email)
    except ClientError:
        pass

    # Create test user
    cognito_gateway.aws_client.admin_create_user(
        UserPoolId=cognito_gateway.aws_user_pool_id,
        Username=test_user_email,
        UserAttributes=[
            {"Name": "email", "Value": test_user_email},
            {"Name": "email_verified", "Value": "true"},
        ],
        MessageAction="SUPPRESS",
    )

    yield test_user_email

    # Cleanup
    try:
        cognito_gateway.delete_cognito_user(test_user_email)
    except ClientError:
        pass


class TestSyncUserGroups:
    """Test suite for the sync_user_groups method."""

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_add_single_group(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        groups_to_add = [UserRole.Adviser]

        # Verify user has no groups initially
        initial_groups = cognito_gateway.get_user_groups(test_email)
        assert initial_groups == []

        # Sync groups
        cognito_gateway.sync_user_groups(test_email, groups_to_add)

        # Verify groups were added
        final_groups = cognito_gateway.get_user_groups(test_email)
        assert set(final_groups) == {UserRole.Adviser.value}

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_add_multiple_groups(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        groups_to_add = [UserRole.Adviser, UserRole.Compliance, UserRole.Paraplanner]

        # Sync groups
        cognito_gateway.sync_user_groups(test_email, groups_to_add)

        # Verify groups were added
        final_groups = cognito_gateway.get_user_groups(test_email)
        expected_groups = {role.value for role in groups_to_add}
        assert set(final_groups) == expected_groups

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_remove_groups(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        initial_groups = [UserRole.Adviser, UserRole.Compliance, UserRole.Paraplanner]
        final_groups = [UserRole.Adviser]

        # Add initial groups
        cognito_gateway.sync_user_groups(test_email, initial_groups)

        # Verify initial state
        current_groups = cognito_gateway.get_user_groups(test_email)
        assert set(current_groups) == {role.value for role in initial_groups}

        # Remove some groups
        cognito_gateway.sync_user_groups(test_email, final_groups)

        # Verify groups were removed
        current_groups = cognito_gateway.get_user_groups(test_email)
        assert set(current_groups) == {UserRole.Adviser.value}

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_mixed_add_remove(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        initial_groups = [UserRole.Adviser, UserRole.Compliance]
        final_groups = [UserRole.Compliance, UserRole.Paraplanner, UserRole.RelationshipManager]

        # Add initial groups
        cognito_gateway.sync_user_groups(test_email, initial_groups)

        # Sync to final groups (should remove Adviser, keep Compliance, add Paraplanner and RelationshipManager)
        cognito_gateway.sync_user_groups(test_email, final_groups)

        # Verify final state
        current_groups = cognito_gateway.get_user_groups(test_email)
        expected_groups = {role.value for role in final_groups}
        assert set(current_groups) == expected_groups

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_no_change_needed(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        groups = [UserRole.Adviser, UserRole.Compliance]

        # Add initial groups
        cognito_gateway.sync_user_groups(test_email, groups)

        # Sync with same groups (should be no-op)
        cognito_gateway.sync_user_groups(test_email, groups)

        # Verify groups remain the same
        current_groups = cognito_gateway.get_user_groups(test_email)
        expected_groups = {role.value for role in groups}
        assert set(current_groups) == expected_groups

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_empty_groups_list(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        initial_groups = [UserRole.Adviser, UserRole.Compliance]

        # Add initial groups
        cognito_gateway.sync_user_groups(test_email, initial_groups)

        # Verify initial state
        current_groups = cognito_gateway.get_user_groups(test_email)
        assert len(current_groups) == 2

        # Sync with empty list
        cognito_gateway.sync_user_groups(test_email, [])

        # Verify all groups were removed
        current_groups = cognito_gateway.get_user_groups(test_email)
        assert current_groups == []

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_with_string_groups(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        groups_as_strings = ["adviser", "compliance", "paraplanner"]

        # Sync groups using strings
        cognito_gateway.sync_user_groups(test_email, groups_as_strings)

        # Verify groups were added
        current_groups = cognito_gateway.get_user_groups(test_email)
        assert set(current_groups) == set(groups_as_strings)

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_mixed_enum_and_string(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        mixed_groups = [UserRole.Adviser, "compliance", UserRole.Paraplanner]

        # Sync groups using mixed types
        cognito_gateway.sync_user_groups(test_email, mixed_groups)

        # Verify groups were added
        current_groups = cognito_gateway.get_user_groups(test_email)
        expected_groups = {"adviser", "compliance", "paraplanner"}
        assert set(current_groups) == expected_groups

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_invalid_group_raises_exception(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        invalid_groups = ["nonexistent_group", UserRole.Adviser]

        # Should raise exception for invalid group
        with pytest.raises(CognitoInvalidGroups) as exc_info:
            cognito_gateway.sync_user_groups(test_email, invalid_groups)

        # Verify the exception contains the invalid group
        assert "nonexistent_group" in exc_info.value.groups
        assert exc_info.value.groups == ["nonexistent_group"]

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_all_invalid_groups(
        self, cognito_gateway: DevCognitoGateway, setup_test_user: str
    ) -> None:
        test_email = setup_test_user
        invalid_groups = ["invalid1", "invalid2", "invalid3"]

        # Should raise exception
        with pytest.raises(CognitoInvalidGroups) as exc_info:
            cognito_gateway.sync_user_groups(test_email, invalid_groups)

        # Verify all invalid groups are listed
        assert set(exc_info.value.groups) == set(invalid_groups)

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_user_not_found_raises_client_error(
        self, cognito_gateway: DevCognitoGateway
    ) -> None:
        nonexistent_email = "<EMAIL>"
        groups = [UserRole.Adviser]

        # Should raise ClientError when user doesn't exist
        with pytest.raises(ClientError) as exc_info:
            cognito_gateway.sync_user_groups(nonexistent_email, groups)

        # Verify it's a UserNotFoundException
        assert exc_info.value.response["Error"]["Code"] == "UserNotFoundException"

    @pytest.mark.usefixtures("setup_test_groups")
    def test_sync_user_groups_preserves_other_user_groups(
        self, cognito_gateway: DevCognitoGateway
    ) -> None:
        # Create two test users
        user1_email = "<EMAIL>"
        user2_email = "<EMAIL>"

        # Clean up any existing users
        for email in [user1_email, user2_email]:
            try:
                cognito_gateway.delete_cognito_user(email)
            except ClientError:
                pass

        try:
            # Create both users
            for email in [user1_email, user2_email]:
                cognito_gateway.aws_client.admin_create_user(
                    UserPoolId=cognito_gateway.aws_user_pool_id,
                    Username=email,
                    UserAttributes=[
                        {"Name": "email", "Value": email},
                        {"Name": "email_verified", "Value": "true"},
                    ],
                    MessageAction="SUPPRESS",
                )

            # Give each user different groups
            cognito_gateway.sync_user_groups(user1_email, [UserRole.Adviser])
            cognito_gateway.sync_user_groups(
                user2_email, [UserRole.Compliance, UserRole.Paraplanner]
            )

            # Verify initial state
            user1_groups = cognito_gateway.get_user_groups(user1_email)
            user2_groups = cognito_gateway.get_user_groups(user2_email)
            assert set(user1_groups) == {"adviser"}
            assert set(user2_groups) == {"compliance", "paraplanner"}

            # Update user1's groups
            cognito_gateway.sync_user_groups(
                user1_email, [UserRole.RelationshipManager, UserRole.CaseManagement]
            )

            # Verify user1's groups changed but user2's remained the same
            user1_groups = cognito_gateway.get_user_groups(user1_email)
            user2_groups = cognito_gateway.get_user_groups(user2_email)
            assert set(user1_groups) == {"relationship_manager", "case_management"}
            assert set(user2_groups) == {"compliance", "paraplanner"}

        finally:
            # Cleanup
            for email in [user1_email, user2_email]:
                try:
                    cognito_gateway.delete_cognito_user(email)
                except ClientError:
                    pass
