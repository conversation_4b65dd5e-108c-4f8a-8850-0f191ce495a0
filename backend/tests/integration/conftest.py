from dataclasses import asdict
from functools import partial
from itertools import permutations
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Iterator
from unittest.mock import patch
from uuid import UUID

import asyncpg
import pytest
from pydash.helpers import UNSET
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from api.enums import CaseStatus, CaseType, ClientLinkRelationship, ClientStatus, Goals
from app.clients.dtos.factfind_dtos import GoalTypeAttributesDTO
from common.event_publisher import EventPublisher
from database.models import (
    ClientCaseGoalModel,
    ClientCaseModel,
    ClientGoalsModel,
    ClientHoldingModel,
    ClientLinkModel,
    ClientModel,
    GoalTypeModel,
    ReviewGroupModel,
    ReviewSlotModel,
)
from database.repositories import unit_of_work
from database.session.Session import PostgresSessionMaker
from tests.insert_manager.insert_manager import InsertManager

DB_TEMPLATE_NAME_CLEAN = "test_db_template_empty"


@pytest.fixture()
def freeze_event_uuid():
    with patch("app.shared_kernel.type_defs.uuid4") as mock_uuid:
        mock_uuid.return_value = UUID("f3bc0093-b029-445f-b633-2fb13d7b363e")
        yield mock_uuid


@pytest.fixture
def temp_directory_path() -> Iterator[Path]:
    with TemporaryDirectory() as temp_dir_name:
        yield Path(temp_dir_name)


@pytest.fixture
def postgres_database_int(settings) -> PostgresSessionMaker:
    return PostgresSessionMaker(db_url=settings.async_url)


@pytest.fixture
def uow2(postgres_database_int):
    return unit_of_work.SQLAlchemyUnitOfWork(postgres_database_int)


@pytest.fixture(autouse=True, scope="session")
async def db_template_setup():
    """
    Integration tests inserts some static data that already exists,
    so override fixture to skip creating database from template.
    """
    return


@pytest.fixture(autouse=True)
async def db_for_tests(request, settings):
    con = await asyncpg.connect(settings.postgres_url)
    try:
        await con.fetch(
            """
            SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE pid <> pg_backend_pid();
            """,
        )
    except Exception as e:
        print(f"Error terminating: {e}")
    await con.execute(f"DROP DATABASE IF EXISTS {settings.db_database_name}")
    await con.execute(
        f"CREATE DATABASE {settings.db_database_name} TEMPLATE {DB_TEMPLATE_NAME_CLEAN}"
    )
    await con.close()


@pytest.fixture
async def insert_manager(async_session: AsyncSession) -> InsertManager:
    manager = InsertManager(async_session)
    await manager.insert_random_adviser()
    return manager


@pytest.fixture
async def client_id(insert_manager: InsertManager) -> int:
    client = await insert_manager.insert_random_client()
    return client["id"]


@pytest.fixture
def mute_event_publishing():
    # We are not interested in the events being published for the tests in this module
    with patch.object(EventPublisher, "publish"):
        yield


async def case_factory(adviser_id: int, session: AsyncSession) -> int:
    async with session:
        case = ClientCaseModel(
            name="Some case...",
            case_type=CaseType.AnnualReview,
            adviser_id=adviser_id,
            status=CaseStatus.Open,
        )
        session.add(case)
        await session.flush()
        case_id = case.id
        await session.commit()
    return case_id


async def case_goal_factory(case_id: int, goal_id: int, session: AsyncSession) -> int:
    async with session:
        case_goal = ClientCaseGoalModel(case_id=case_id, goal_id=goal_id)
        session.add(case_goal)
        await session.flush()
        case_goal_id = case_goal.id
        await session.commit()
    return case_goal_id


async def client_factory(
    session: AsyncSession,
    insert_manager: InsertManager,
) -> int:
    client_data = await insert_manager.generate_random_client()
    client_data["client_status_id"] = ClientStatus.ActiveOngoing
    async with session:
        client = ClientModel(**client_data)
        session.add(client)
        await session.flush()
        client_id = client.id
        await session.commit()
    return client_id


async def link_clients(client_ids: list[int], session: AsyncSession) -> None:
    client_links = [
        ClientLinkModel(
            client_id=left_id,
            linked_client_id=right_id,
            link_relationship=ClientLinkRelationship.Spouse,
        )
        for left_id, right_id in permutations(client_ids, 2)
    ]
    async with session:
        session.add_all(client_links)
        await session.commit()


async def review_group_factory(
    session: AsyncSession, review_month, review_frequency, client_ids: list[int]
):
    async with session:
        client_query = select(ClientModel).where(ClientModel.id.in_(client_ids))
        result = await session.execute(client_query)
        clients = result.scalars().all()

        review_group = ReviewGroupModel(
            review_month=review_month,
            review_frequency=review_frequency,
            clients=clients,
            is_active=True,
        )
        session.add(review_group)
        await session.commit()

    return review_group.id


async def review_slot_factory(
    slot_month: int,
    slot_year: int,
    session: AsyncSession,
    review_group_id: int,
    case_id: int | None = None,
) -> int:
    async with session:
        slot = ReviewSlotModel(
            slot_month=slot_month,
            slot_year=slot_year,
            case_id=case_id,
            review_group_id=review_group_id,
        )
        session.add(slot)
        await session.commit()
        slot_id = slot.id
    return slot_id


@pytest.fixture
def add_client(async_session: AsyncSession, insert_manager: InsertManager):
    return partial(client_factory, session=async_session, insert_manager=insert_manager)


@pytest.fixture
def add_slot(async_session: AsyncSession):
    return partial(review_slot_factory, session=async_session)


@pytest.fixture
def add_review_group(async_session: AsyncSession):
    return partial(review_group_factory, session=async_session)


@pytest.fixture
def make_link(async_session: AsyncSession):
    return partial(link_clients, session=async_session)


@pytest.fixture
def add_case(async_session: AsyncSession):
    return partial(case_factory, adviser_id=1, session=async_session)


@pytest.fixture
def add_case_goal(async_session: AsyncSession):
    return partial(case_goal_factory, session=async_session)


async def client_goal_factory(
    client_ids: list[int],
    goal: Goals,
    session: AsyncSession,
    goal_attributes: GoalTypeAttributesDTO | None = None,
    goal_objectives: str | None = UNSET,
    linked_holding_ids: list[int] | None = None,
) -> int:
    async with session:
        clients = []
        for client_id in client_ids:
            client = await session.get(ClientModel, client_id)
            clients.append(client)
        client_goal = ClientGoalsModel(
            client_id=client_id,  # TODO: Remove
            goal_id=goal,
            goal_objectives=f"Goal{goal} objectives"
            if goal != Goals.ClientSetup.value and goal_objectives is UNSET
            else goal_objectives,
            attributes=asdict(goal_attributes) if goal_attributes else {},
        )
        client_goal.clients = clients
        if linked_holding_ids:
            statement = select(ClientHoldingModel).where(
                ClientHoldingModel.id.in_(linked_holding_ids)
            )
            result = await session.execute(statement)
            holdings = result.scalars().all()
            client_goal.holdings = holdings
        session.add(client_goal)
        await session.commit()
        client_goal_id = client_goal.id
    return client_goal_id


@pytest.fixture
def add_client_goal(async_session: AsyncSession):
    return partial(client_goal_factory, session=async_session)


@pytest.fixture
async def seed_goals(async_session: AsyncSession) -> None:
    goals_data = [
        {
            "id": goal.value,
            "name": goal.name,
            "code": str(goal.name[:4]).upper(),
            "is_default": False,
            "client_goal": False,
        }
        for goal in Goals
    ]
    async_session.add_all((GoalTypeModel(**goal) for goal in goals_data))
    await async_session.commit()
