from dataclasses import asdict
from datetime import date
from unittest.mock import Magic<PERSON>ock

import pytest

from api.enums import ClientSource, ClientStatus, ClientType, enums
from app.clients import exceptions
from app.clients.services.add_client import AddClientService
from app.clients.services.client_query_services import ClientQueryServices
from tests import fakes


async def test_get_client_details_raises_exception_when_client_not_found(uow, cognito_proxy):
    get_client_details = ClientQueryServices(
        uow=uow,
        cognito=cognito_proxy,
        logger=MagicMock(),
    )
    with pytest.raises(exceptions.ClientNotFound):
        await get_client_details.get_by_id(client_id=-1)


async def test_get_client_details_returns_existing_client(
    uow, add_client_service: AddClientService, cognito_proxy
):
    async with uow:
        owner = await uow.owners.get_by_email("<EMAIL>")
        owner_id = owner.id

    client_data = fakes.fake_client_data(
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        date_of_birth=date(year=1980, month=1, day=31),
        phone_number="123",
        mobile_number="456",
        owner_id=owner_id,
    )
    client_id = await add_client_service.add_client(**client_data)

    get_client_details = ClientQueryServices(
        uow=uow,
        cognito=cognito_proxy,
        logger=MagicMock(),
    )
    client = await get_client_details.get_by_id(client_id=client_id)
    client = asdict(client)
    del client["date_of_birth"]
    assert client == {
        "id": 2,
        "email": "<EMAIL>",
        "no_email_reason_id": None,
        "title_id": None,
        "first_name": "John",
        "last_name": "Doe",
        "phone_number": "123",
        "mobile_number": "456",
        "marital_status_id": None,
        "gender": None,
        "gender_id": None,
        "nationality_id": None,
        "type": "client",
        "client_source_id": ClientSource.SocialMedia,
        "client_status_id": ClientStatus.LeadLive,
        "client_type_id": ClientType.Individual,
        "review_frequency": None,
        "review_month": None,
        "next_review_month": None,
        "addresses": [],
        "links": [],
        "advisor": {"id": 1, "first_name": "Jane", "last_name": "Brown", "email": None},
        "access_enabled": False,
        "client_agreement_id": None,
        "privacy_notice_id": None,
        "chatbot_consent_granted": False,
        "chatbot_consent_timestamp": None,
    }


async def test_get_client_overview_links(uow, add_client_service: AddClientService, cognito_proxy):
    async with uow:
        owner = await uow.owners.get_by_email("<EMAIL>")
        owner_id = owner.id

    client_data = fakes.fake_client_data(
        email="<EMAIL>",
        first_name="John",
        last_name="Doe",
        owner_id=owner_id,
    )
    client1_id = await add_client_service.add_client(**client_data)

    client_data = fakes.fake_client_data(
        email="<EMAIL>",
        client_links=[(client1_id, enums.ClientLinkRelationship.BusinessPartner)],
        owner_id=owner_id,
    )
    client2_id = await add_client_service.add_client(**client_data)

    get_client_details = ClientQueryServices(
        uow=uow,
        cognito=cognito_proxy,
        logger=MagicMock(),
    )
    client = await get_client_details.get_by_id(client_id=client2_id)
    assert asdict(client)["links"] == [
        {
            "client_id": client1_id,
            "first_name": "John",
            "last_name": "Doe",
            "link_relationship_id": enums.ClientLinkRelationship.BusinessPartner,
        }
    ]
