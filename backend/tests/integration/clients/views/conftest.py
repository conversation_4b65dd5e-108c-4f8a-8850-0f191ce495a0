from unittest.mock import Mock

import pytest

from app.clients.services.add_client import AddClientService
from database import models
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork
from tests.insert_manager import InsertManager


@pytest.fixture
async def uow(postgres_database):
    uow = SQLAlchemyUnitOfWork(postgres_database)
    async with uow:
        insert_manager = InsertManager(session=uow.session)
        await insert_manager.insert_holding_statuses()
        await insert_manager.insert_client_setup_goal()

        owner = models.OwnerModel(
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON>",
        )
        await uow.owners.add(owner)

        await uow.commit()

        account = models.ClientHoldingModel(account_number="00000", adviser=owner)
        await uow.holdings.add(account)
        await uow.commit()

    return uow


@pytest.fixture
def add_client_service(uow) -> AddClientService:
    return AddClientService(uow=uow, logger=Mock())
