import json
from typing import Literal

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from api.enums import ClientStatus


async def insert_goal(
    async_session: AsyncSession,
    name: str,
    is_default: bool,
    description: str,
    client_goal: bool = True,
) -> int:
    [[goal_id]] = await async_session.execute(
        text(
            "INSERT INTO goal_types (name, is_default, description, client_goal)"
            " VALUES (:name, :is_default, :description, :client_goal)"
            " RETURNING id"
        ),
        {
            "name": name,
            "is_default": is_default,
            "description": description,
            "client_goal": client_goal,
        },
    )
    return goal_id


async def insert_user(
    async_session: AsyncSession,
    email: str,
    first_name: str,
    last_name: str,
    user_type: Literal["owner", "client"],
) -> int:
    [[user_id]] = await async_session.execute(
        text(
            "INSERT INTO users (email, first_name, last_name, type)"
            " VALUES (:email, :first_name, :last_name, :user_type)"
            " RETURNING id"
        ),
        {
            "email": email,
            "first_name": first_name,
            "last_name": last_name,
            "user_type": user_type,
        },
    )
    return user_id


async def insert_owner(
    async_session: AsyncSession,
    email: str,
    first_name: str,
    last_name: str,
) -> int:
    user_id = await insert_user(async_session, email, first_name, last_name, "owner")
    [[owner_id]] = await async_session.execute(
        text("INSERT INTO owners (id, roles) VALUES (:id, :roles) RETURNING id"),
        {"id": user_id, "roles": json.dumps(["adviser"])},
    )
    return owner_id


async def insert_client(
    async_session: AsyncSession,
    email: str,
    first_name: str,
    last_name: str,
    owner_id: int | None = None,
    client_status_id: int = ClientStatus.LeadLive,
):
    user_id = await insert_user(async_session, email, first_name, last_name, "client")
    [[client_id]] = await async_session.execute(
        text(
            "INSERT INTO client (id, client_type_id, owner_id, title_id, date_of_birth, marital_status_id, nationality_id, "
            "currency, phone_number, is_mobile_available, annual_review_date, "
            "client_source_id, client_status_id, gender_id, chatbot_consent_granted, chatbot_consent_timestamp) "
            " VALUES (:id, 1, :owner_id, NULL, NULL, NULL, NULL, 'GBP', NULL, TRUE, NULL, 1, :client_status_id, NULL, FALSE, NULL)"
            " RETURNING id"
        ),
        {"id": user_id, "owner_id": owner_id, "client_status_id": client_status_id},
    )
    return client_id
