from typing import Any

import pytest
from sqlalchemy import select

from database.models import ClientModel, GoalTypeModel, OwnerModel
from database.repositories import BaseRepository, ClientRepository
from tests.integration import inserts


@pytest.fixture
async def goals_repository(async_session):
    goals_repository = BaseRepository(async_session)
    goals_repository.entity = GoalTypeModel
    yield goals_repository


@pytest.fixture
async def client_repository(async_session):
    client_repository = ClientRepository(async_session)
    yield client_repository


async def test_base_repository_can_save_an_entity(async_session, goals_repository):
    goal = GoalTypeModel(
        name="goal one",
        is_default=False,
        description="goal one description",
        client_goal=True,
    )
    await goals_repository.add(goal)
    await goals_repository.session.commit()

    rows = list(
        await async_session.execute(
            select(
                GoalTypeModel.name,
                GoalTypeModel.is_default,
                GoalTypeModel.description,
                GoalTypeModel.client_goal,
            )
        )
    )
    assert rows == [("goal one", False, "goal one description", True)]


async def test_base_repository_can_retrieve_an_entity_by_id(async_session, goals_repository):
    goal_id = await inserts.insert_goal(async_session, "goal name", False, "goal desc")

    goal: GoalTypeModel = await goals_repository.get(goal_id)
    expected = GoalTypeModel(name="goal name", is_default=False, description="goal desc")
    assert goal.name == expected.name
    assert goal.is_default == expected.is_default
    assert goal.description == expected.description


_goal_1_data = {
    "name": "goal 1",
    "is_default": True,
    "description": "goal 1 desc",
}
_goal_2_data = {
    "name": "goal 2",
    "is_default": True,
    "description": "goal 2 desc",
}
_goal_2_sub_data = {
    "name": "goal 2 - subcategory",
    "is_default": False,
    "description": "goal 2 desc",
}


def _check_goal_data(goal: GoalTypeModel, goal_id: int, data: dict):
    assert goal.id == goal_id
    assert goal.name == data["name"]
    assert goal.is_default == data["is_default"]
    assert goal.description == data["description"]
    return True


@pytest.fixture
async def inserted_goals(async_session) -> dict[int, dict[str, Any]]:
    inserted_goals = {
        await inserts.insert_goal(async_session, **goal_data): goal_data
        for goal_data in [_goal_1_data, _goal_2_data, _goal_2_sub_data]
    }
    yield inserted_goals


async def test_base_repository_can_search_by_id(inserted_goals, goals_repository):
    for goal_id, goal_data in inserted_goals.items():
        goals: list[GoalTypeModel] = await goals_repository.find(GoalTypeModel.id == goal_id)
        assert len(goals) == 1
        [goal] = goals
        assert _check_goal_data(goal, goal_id, goal_data)


async def test_base_repository_can_search_by_string(inserted_goals, goals_repository):
    goals: list[GoalTypeModel] = await goals_repository.find(GoalTypeModel.name.ilike("%goal%"))
    assert len(goals) == 3

    goals: list[GoalTypeModel] = await goals_repository.find(
        GoalTypeModel.description.ilike("%goal 1 desc%")
    )
    assert len(goals) == 1

    goals: list[GoalTypeModel] = await goals_repository.find(
        GoalTypeModel.description.ilike("%goal 2 desc%")
    )
    assert len(goals) == 2


async def test_base_repository_can_count_all_with_search(inserted_goals, goals_repository):
    goals: list[GoalTypeModel] = await goals_repository.find(
        GoalTypeModel.name.ilike("%goal%"), limit=1
    )
    assert len(goals) == 1

    goals_count_without_offset = await goals_repository.count(GoalTypeModel.name.ilike("%goal%"))
    assert goals_count_without_offset == 3


@pytest.fixture
async def inserted_clients(async_session) -> dict[int, dict[str, Any]]:
    owners = [
        {
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Smith",
        },
        {
            "email": "<EMAIL>",
            "first_name": "Thomas",
            "last_name": "Anderson",
        },
    ]
    inserted_owners = {
        await inserts.insert_owner(async_session, **owner): owner for owner in owners
    }
    id_of_first_owner, id_of_second_owner = inserted_owners.keys()
    clients = [
        {
            "email": "<EMAIL>",
            "first_name": "Dan",
            "last_name": "Huggins",
            "owner_id": id_of_first_owner,
        },
        {
            "email": "<EMAIL>",
            "first_name": "Aaron",
            "last_name": "Huggins",
            "owner_id": id_of_second_owner,
        },
    ]
    inserted_clients = {
        await inserts.insert_client(async_session, **client): client for client in clients
    }
    yield inserted_clients


async def test_search_on_join(inserted_clients, client_repository):
    clients_named_dan = await client_repository.find(ClientModel.first_name.ilike("%Dan%"))
    assert len(clients_named_dan) == 1
    dan: ClientModel
    [dan] = clients_named_dan
    statement = (
        select(ClientModel.id).join(ClientModel.owner).where(OwnerModel.first_name.ilike("%john%"))
    )
    str(statement)
    result = await client_repository.session.execute(statement)
    result = result.scalars().all()

    statement = select(ClientModel.id).where(OwnerModel.first_name.ilike("%john%"))
    result = await client_repository.session.execute(statement)
    result = result.scalars().all()
    clients_under_john = await client_repository.with_joins(ClientModel.owner).find(
        OwnerModel.id == dan.owner_id
    )
    assert len(clients_under_john) == 1
    clients_with_advisors_named_john = await client_repository.with_joins(ClientModel.owner).find(
        OwnerModel.first_name.ilike("%john%")
    )
    assert len(clients_with_advisors_named_john) == 1


async def test_should_bulk_save_objects(async_session, goals_repository):
    # Given
    goals = [
        GoalTypeModel(
            name="goal one",
            is_default=False,
            description="goal one description",
            client_goal=False,
        ),
        GoalTypeModel(
            name="goal two",
            is_default=True,
            description="goal two description",
            client_goal=True,
        ),
    ]
    # When
    await goals_repository.bulk_add(goals)
    await goals_repository.session.commit()

    # Then
    expected = [(goal.name, goal.is_default, goal.description, goal.client_goal) for goal in goals]
    rows = list(
        await async_session.execute(
            select(
                GoalTypeModel.name,
                GoalTypeModel.is_default,
                GoalTypeModel.description,
                GoalTypeModel.client_goal,
            )
        )
    )
    assert rows == expected


async def test_search_with_order_by(inserted_clients, client_repository):
    result = await client_repository.list()
    assert result[0].first_name == "Dan"

    result = await client_repository.with_order_by(ClientModel.first_name).list()
    assert result[0].first_name == "Aaron"
