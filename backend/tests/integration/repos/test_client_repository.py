from datetime import datetime

from api.enums import Client<PERSON>tatus
from database.repositories.client_repository import ClientRepository
from database.repositories.clients.client_repository import SQLAlchemyClientRepository
from tests.insert_manager import InsertManager


class TestExists:
    async def test_returns_true_when_the_client_id_exists(
        self, client_repo: ClientRepository, insert_manager: InsertManager
    ) -> None:
        client = await insert_manager.insert_random_client()
        assert await client_repo.exists(client_id=client["id"])

    async def test_returns_false_when_the_client_id_does_not_exist(
        self, client_repo: ClientRepository, insert_manager: InsertManager
    ) -> None:
        assert await client_repo.exists(client_id=999) is False


class TestGetClientsWithRecentFeesThatShouldBeActive:
    async def test_returns_expected_clients_for_each_client_status(
        self, client_repo: ClientRepository, insert_manager: InsertManager
    ) -> None:
        client = await insert_manager.insert_random_client()
        holding = await insert_manager.insert_random_holding(client_id=client["id"])

        # Test with non-dormant, non-active-ongoing client statuses
        for status in [ClientStatus.LeadLive, ClientStatus.ActiveTransactional]:
            await client_repo.bulk_update_status(client_ids=[client["id"]], status=status)
            result = await client_repo.get_clients_with_recent_fees_that_should_be_active(
                holding_ids=[holding["id"]]
            )
            assert len(result) == 1
            assert result[0].id == client["id"]

        # Test with dormant statuses
        for status in ClientStatus.closed_statuses():
            await client_repo.bulk_update_status(client_ids=[client["id"]], status=status)
            result = await client_repo.get_clients_with_recent_fees_that_should_be_active(
                holding_ids=[holding["id"]]
            )
            assert len(result) == 0

        # Test with active ongoing
        await client_repo.bulk_update_status(
            client_ids=[client["id"]], status=ClientStatus.ActiveOngoing
        )
        result = await client_repo.get_clients_with_recent_fees_that_should_be_active(
            holding_ids=[holding["id"]]
        )
        assert len(result) == 0


class TestNewClientRepository:
    """Tests for the repository defined in database/repositories/clients/client_repository.py."""

    async def test_grant_chatbot_consent(
        self, new_client_repo: SQLAlchemyClientRepository, insert_manager: InsertManager
    ) -> None:
        client = await insert_manager.insert_random_client()

        await new_client_repo.grant_chatbot_consent(client["id"])

        # Verify consent was granted
        updated_client = await new_client_repo.get_by_id(client["id"])
        assert updated_client.chatbot_consent_granted is True
        assert updated_client.chatbot_consent_timestamp is not None
        assert isinstance(updated_client.chatbot_consent_timestamp, datetime)

    async def test_withdraw_chatbot_consent(
        self, new_client_repo: SQLAlchemyClientRepository, insert_manager: InsertManager
    ) -> None:
        client = await insert_manager.insert_random_client()

        # First grant consent
        await new_client_repo.grant_chatbot_consent(client["id"])

        # Then withdraw it
        await new_client_repo.withdraw_chatbot_consent(client["id"])

        # Verify consent was withdrawn and timestamp cleared
        updated_client = await new_client_repo.get_by_id(client["id"])
        assert updated_client.chatbot_consent_granted is False
        assert updated_client.chatbot_consent_timestamp is None
