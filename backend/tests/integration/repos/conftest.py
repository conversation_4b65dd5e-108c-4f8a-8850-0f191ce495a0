from unittest.mock import Mock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from database.repositories.aml.aml_monitoring_repository import SQLAlchemyAMLMonitoringRepository
from database.repositories.case.review_repository import SQLAlchemyReviewRepository
from database.repositories.client_goal_repository import ClientGoalsRepository
from database.repositories.client_repository import ClientRepository
from database.repositories.clients.client_repository import SQLAlchemyClientRepository
from database.repositories.document_template_repository import (
    DocumentTemplateRepository,
)


@pytest.fixture
def document_template_repo(async_session: AsyncSession) -> DocumentTemplateRepository:
    return DocumentTemplateRepository(async_session)


@pytest.fixture
def review_calendar_repo(async_session: AsyncSession) -> SQLAlchemyReviewRepository:
    return SQLAlchemyReviewRepository(async_session, Mock)


@pytest.fixture
def monitoring_session_repo(async_session: AsyncSession) -> SQLAlchemyAMLMonitoringRepository:
    return SQLAlchemyAMLMonitoringRepository(async_session, Mock)


@pytest.fixture
def client_goals_repo(async_session: AsyncSession) -> ClientGoalsRepository:
    return ClientGoalsRepository(async_session)


@pytest.fixture
def client_repo(async_session: AsyncSession) -> ClientRepository:
    return ClientRepository(async_session)


@pytest.fixture
def new_client_repo(async_session: AsyncSession) -> SQLAlchemyClientRepository:
    return SQLAlchemyClientRepository(async_session)
