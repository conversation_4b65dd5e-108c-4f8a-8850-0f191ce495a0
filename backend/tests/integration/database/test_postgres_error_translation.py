import pytest

from database.models import ClientModel
from database.repositories.db_error_handling import Error<PERSON><PERSON>, IntegrityError
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork


async def test_translate_integrity_exception(postgres_database):
    uow = SQLAlchemyUnitOfWork(postgres_database)
    async with uow:
        client = ClientModel(first_name="<PERSON>", email="<EMAIL>")
        await uow.clients.add(client)
        await uow.commit()
        client2 = ClientModel(id=client.id, first_name="Johan2", email="<EMAIL>")
        await uow.clients.add(client2)
        with pytest.raises(IntegrityError) as e_info:
            await uow.commit()
    error_code, field_name = e_info.value.error_code, e_info.value.field_name
    assert error_code is ErrorCode.FIELD_NOT_UNIQUE
    assert field_name == "id"
