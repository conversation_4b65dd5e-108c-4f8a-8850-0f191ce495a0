import glob
import os
import re
from pathlib import Path

import alembic
import pytest
from alembic.autogenerate import compare_metadata
from alembic.config import Config
from alembic.runtime.migration import MigrationContext
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import create_async_engine

from database.models import BaseDBModel
from tests.config import get_tests_settings

__backend_path__ = Path(__file__).parents[3]
__config_path__ = __backend_path__ / "alembic.ini"
__migration_path__ = __backend_path__ / "migrations"
__versions_path__ = __migration_path__ / "versions"

settings = get_tests_settings()


@pytest.fixture(autouse=True)
async def db_for_tests(request):
    """
    `test_compare_schema` needs clean database in order to migrate it,
    so override fixture to skip initial migrations.
    """
    return


@pytest.fixture
def apply_migrations():
    config = Config(str(__config_path__))
    config.set_main_option("script_location", str(__migration_path__))

    alembic.command.upgrade(config, "head")
    yield
    alembic.command.downgrade(config, "base")


def _get_db_diff(conn: Connection):
    mc = MigrationContext.configure(conn)
    diff = compare_metadata(mc, BaseDBModel.metadata)

    return diff


@pytest.fixture
async def schema_diff(apply_migrations):
    engine = create_async_engine(settings.async_url)
    async with engine.begin() as conn:
        diff = await conn.run_sync(_get_db_diff)
        return diff


# This test does something strange to the event loop and so needs to be run last
@pytest.mark.order(-1)
def test_compare_schema(schema_diff):
    assert schema_diff == [], "Detected difference between models and DB schema."


def test_should_contain_date_in_version_name():
    # Given
    DATETIME_MATCH = "([0-9]{4})-([0-9]{2})-([0-9]{2})-([0-9]{2})-([0-9]{2})-([0-9]{2})"

    for version in glob.glob(f"{__versions_path__}/*.py"):
        filename = os.path.basename(version)

        # When
        has_match = re.search(DATETIME_MATCH, filename)

        # Then
        assert has_match, f"Version file found without date and time in the name. Path: {version}"
