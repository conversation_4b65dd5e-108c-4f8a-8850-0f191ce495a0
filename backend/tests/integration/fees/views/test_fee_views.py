from datetime import date
from decimal import Decimal
from unittest.mock import Mock

from api.common.pagination import pagination_parameters
from api.enums import FeeRoleType, FeeSplitType, enums
from app.clients.services.add_client import AddClientService
from app.fees.commands import matching, payments, statements
from app.fees.commands.fee_split_templates import AddFeeSplitTemplate
from app.fees.dtos.templates import AddFeeSplitTemplateDTO, AddFeeSplitTemplateLineDTO
from app.fees.queries.fees import get_fee_list
from database import models
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork
from tests import fakes
from tests.insert_manager import InsertManager


async def test_fee_list(postgres_database):
    uow = SQLAlchemyUnitOfWork(postgres_database)
    async with uow:
        insert_manager = InsertManager(session=uow.session)
        await insert_manager.insert_holding_statuses()
        await insert_manager.insert_client_setup_goal()

        owner = models.OwnerModel(email="<EMAIL>")
        await uow.owners.add(owner)
        await uow.commit()

    add_client_service = AddClientService(uow=uow, logger=Mock())
    client_data = fakes.fake_client_data(email="<EMAIL>", owner_id=1)
    client_id = await add_client_service.add_client(**client_data)

    fee_split = AddFeeSplitTemplateDTO(
        administrator_id=owner.id,
        is_active=True,
        template_name="Test template",
        lines=[
            AddFeeSplitTemplateLineDTO(
                administrator_id=owner.id,
                is_payable=True,
                role=FeeRoleType.Adviser,
                split_initial=Decimal(1),
                split_ongoing=Decimal(1),
                type=FeeSplitType.Standard_Fee,
            )
        ],
    )
    fee_split_id = await AddFeeSplitTemplate(uow=uow, logger=Mock())(dto=fee_split)

    async with uow:
        account = models.ClientHoldingModel(
            adviser_id=owner.id,
            fee_split_template_id=fee_split_id,
            account_number="1234",
        )
        await uow.holdings.add(account)
        await uow.commit()

        await uow.client_holding_links.add(
            models.ClientHoldingsLinkModel(client_id=client_id, client_holding_id=account.id)
        )
        await uow.commit()

    payment_id = await payments.add_client_payment(
        client_id=client_id,
        amount=Decimal("1"),
        payment_date=date.today(),
        notes="",
        uow=uow,
    )

    statement_id = await statements.add_client_statement(
        client_id=client_id,
        client_account_id=account.id,
        payment_date=date.today(),
        amount=Decimal("1"),
        fee_type_id=enums.FeeType.InitialFee,
        uow=uow,
    )

    await matching.match_payments_to_statements(
        selected_pairs=[(payment_id, statement_id)],
        uow=uow,
    )

    result = await get_fee_list(pagination=pagination_parameters(page=1, page_size=10), uow=uow)

    assert result.total_fee_count == 1
