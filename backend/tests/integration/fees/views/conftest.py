from decimal import Decimal
from unittest import mock

import pytest

from api.enums import FeeRoleType, FeeSplitType
from app.clients.services.add_client import AddClientService
from app.fees.commands.fee_split_templates import AddFeeSplitTemplate
from app.fees.dtos.templates import AddFeeSplitTemplateDTO, AddFeeSplitTemplateLineDTO
from database import models
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork
from tests import fakes
from tests.insert_manager import InsertManager


@pytest.fixture
async def uow(postgres_database):
    uow = SQLAlchemyUnitOfWork(postgres_database)

    client_data = fakes.fake_client_data(email="<EMAIL>", first_name="<PERSON>", last_name="Doe")
    client_data2 = fakes.fake_client_data(
        email="<EMAIL>", first_name="<PERSON>", last_name="<PERSON><PERSON>"
    )

    with mock.patch("app.clients.services.add_client.add_onboarding_case"):
        service = AddClientService(uow=SQLAlchemyUnitOfWork(postgres_database), logger=mock.Mock())
        client_id = await service.add_client(**client_data)
        client2_id = await service.add_client(**client_data2)

    async with uow:
        await InsertManager(session=uow.session).insert_holding_statuses()

        owner = models.OwnerModel(email="<EMAIL>", first_name="Matt", last_name="Davis")
        owner2 = models.OwnerModel(email="<EMAIL>", first_name="Anya", last_name="West")
        await uow.owners.add(owner)
        await uow.owners.add(owner2)
        await uow.commit()

    fee_split = AddFeeSplitTemplateDTO(
        administrator_id=owner.id,
        is_active=True,
        template_name="Test template",
        lines=[
            AddFeeSplitTemplateLineDTO(
                administrator_id=owner2.id,
                is_payable=True,
                role=FeeRoleType.Adviser,
                split_initial=Decimal(1),
                split_ongoing=Decimal(1),
                type=FeeSplitType.Standard_Fee,
            )
        ],
    )
    fee_split_id = await AddFeeSplitTemplate(uow=uow, logger=mock.Mock())(dto=fee_split)

    async with uow:
        provider = models.ProviderModel(name="Test Provider", is_active=True)
        provider2 = models.ProviderModel(name="Test Provider2", is_active=True)
        await uow.providers.add(provider)
        await uow.providers.add(provider2)

        account = models.ClientHoldingModel(
            account_number="00000",
            adviser=owner,
            fee_split_template_id=fee_split_id,
            provider=provider,
        )
        account2 = models.ClientHoldingModel(
            account_number="99999",
            adviser=owner,
            fee_split_template_id=fee_split_id,
            provider=provider,
        )
        await uow.holdings.add(account)
        await uow.holdings.add(account2)
        link = models.ClientHoldingsLinkModel(client_id=client_id, holding=account)
        link2 = models.ClientHoldingsLinkModel(client_id=client2_id, holding=account2)
        await uow.client_holding_links.add(link)
        await uow.client_holding_links.add(link2)
        await uow.commit()

    return uow
