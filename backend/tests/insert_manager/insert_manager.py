import json
import random
from collections import defaultdict
from datetime import date
from decimal import Dec<PERSON><PERSON>
from typing import Any

from faker import Faker
from faker.providers import BaseProvider
from sqlalchemy import text
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from api.enums import FeeModel, enums
from database.models import (
    GoalTypeModel,
    HoldingStatusModel,
    ProductTypeGroupsModel,
    ProductTypeModel,
)

InsertDict = dict[str, Any]


class PrizeProvider(BaseProvider):
    def __init__(self, generator: Any):
        super().__init__(generator)
        self.faker = Faker()

    def prize(self) -> Decimal:
        return Decimal(self.faker.pricetag()[1:].replace(",", ""))


class InsertManager:
    def __init__(self, session: AsyncSession):
        self.inserted_data: defaultdict[str, list[InsertDict]] = defaultdict(list)
        self.session: AsyncSession = session
        self.faker = Faker()
        self.faker.add_provider(PrizeProvider)
        self.holdings_statuses_inserted = False
        self.product_types_inserted = False

    async def generate_random_adviser(self) -> InsertDict:
        return {
            "email": self.faker.email(),
            "first_name": self.faker.first_name(),
            "last_name": self.faker.last_name(),
            "type": "owner",
            "roles": json.dumps(["superadmin"]),
        }

    async def generate_random_client(self) -> InsertDict:
        random_owner = self.faker.random_element(self.inserted_data["owners"])
        return {
            "email": self.faker.email(),
            "first_name": self.faker.first_name(),
            "last_name": self.faker.last_name(),
            "type": "client",
            "client_type_id": enums.ClientType.Individual,
            "owner_id": random_owner["id"],
            "title_id": None,
            "date_of_birth": self.faker.date_of_birth(minimum_age=18),
            "marital_status_id": None,
            "currency": "GDB",
            "phone_number": self.faker.phone_number(),
            "is_mobile_available": self.faker.boolean(),
            "client_source_id": self.faker.random_element(list(enums.ClientSource)),
            "client_status_id": enums.ClientStatus.ActiveOngoing,
        }

    async def generate_random_holding(
        self,
    ) -> InsertDict:
        random_owner = self.faker.random_element(self.inserted_data["owners"])
        return {
            "account_number": self.faker.credit_card_number(),
            "sub_account_number": self.faker.credit_card_number(),
            "adviser_id": random_owner["id"],
            "status_id": self.faker.random_element(list(enums.HoldingStatus)),
            "fee_model": random.choice([int(m) for m in FeeModel]),
        }

    async def insert_holding_statuses(self):
        await self.session.execute(
            insert(HoldingStatusModel),
            [{"name": status.name, "id": status} for status in list(enums.HoldingStatus)],
        )
        self.holdings_statuses_inserted = True

    async def insert_product_types(self):
        await self.session.execute(
            insert(ProductTypeGroupsModel)
            .values({"id": 2, "name": "A group", "group_type": 1})
            .on_conflict_do_nothing()
        )
        await self.session.execute(
            insert(ProductTypeModel),
            [
                {"type": type_.value, "product_type_group_id": 2}
                for type_ in list(enums.ProductTypeType)
            ],
        )
        self.product_types_inserted = True

    async def insert_client_setup_goal(self):
        await self.session.execute(
            insert(GoalTypeModel),
            [
                {
                    "id": enums.Goals.ClientSetup,
                    "name": enums.Goals.ClientSetup.name,
                    "code": "CS",
                    "is_default": False,
                    "client_goal": False,
                }
            ],
        )

    async def insert_random_product(self, product_type_id: int = 1):
        if not self.product_types_inserted:
            await self.insert_product_types()
        [product_id] = await self.session.execute(
            text(
                "INSERT INTO product (product_type_id, name) VALUES (:product_type_id, :name) RETURNING id"
            ),
            {"product_type_id": product_type_id, "name": "Some product"},
        )
        return product_id._asdict()["id"]

    async def insert_random_holding(
        self,
        client_id: int | list[int] | None,
        fee_split_template_id: int | None = None,
        fee_model: FeeModel = FeeModel.Custom,
        provider_id: int | None = None,
        adviser_id: int | None = None,
        product_id: int | None = None,
        status_id: int | None = None,
        quantity: int = 1,
    ):
        if not self.holdings_statuses_inserted:
            await self.insert_holding_statuses()
        if client_id is None:
            client_id = self.faker.random_element(self.inserted_data["clients"])["id"]
        if isinstance(client_id, int):
            client_id = [client_id]
        holding_data = await self.generate_random_holding()
        if adviser_id is not None:
            holding_data["adviser_id"] = adviser_id
        if product_id is not None:
            holding_data["product_id"] = product_id
        else:
            holding_data["product_id"] = await self.insert_random_product()
        if status_id is not None:
            holding_data["status_id"] = status_id
        [holding_data] = await self.session.execute(
            text(
                "INSERT INTO client_holdings (provider_id, fee_split_template_id, status_id, account_number, sub_account_number, adviser_id, product_id, fee_model)"
                " VALUES (:provider_id, :fee_split_template_id, :status_id, :account_number, :sub_account_number, :adviser_id, :product_id, :fee_model)"
                " RETURNING id, created, fee_split_template_id, product_id, status_id"
            ),
            holding_data
            | {
                "fee_split_template_id": fee_split_template_id,
                "provider_id": provider_id,
            },
        )

        holding_data = holding_data._asdict()

        await self.session.execute(
            text(
                "INSERT INTO client_holding_quantity (holding_id, date, quantity, is_current)"
                " VALUES (:client_holding_id, :qty_date, :quantity, True)"
            ),
            {
                "client_holding_id": holding_data["id"],
                "qty_date": date.today(),
                "quantity": quantity,
            },
        )

        for cid in client_id:
            await self.session.execute(
                text(
                    "INSERT INTO client_holdings_link (client_id, client_holding_id, share_percent)"
                    " VALUES (:client_id, :client_holding_id, 1)"
                ),
                {
                    "client_id": cid,
                    "client_holding_id": holding_data["id"],
                },
            )
        self.inserted_data["client_holdings"].append(holding_data)
        return holding_data

    async def generate_random_provider(self) -> InsertDict:
        return {
            "name": self.faker.company(),
            "is_active": self.faker.random.random() > 0.8,
        }

    async def generate_random_payment(
        self, payment_type: enums.StatementPaymentType | None, amount: Decimal | None
    ) -> InsertDict:
        if payment_type is None:
            payment_type = self.faker.random_element(list(enums.StatementPaymentType))
        if amount is None:
            amount = self.faker.pydecimal(
                right_digits=2, left_digits=8, max_value=500000, positive=True
            )
        client_id: int | None = None
        provider_id: int | None = None
        match payment_type:
            case enums.StatementPaymentType.Client:
                client_id = self.faker.random_element(self.inserted_data["client"])["id"]
            case enums.StatementPaymentType.Provider:
                provider_id = self.faker.random_element(self.inserted_data["providers"])["id"]
            case _:
                raise ValueError(f'Unsupported StatementPaymentType "{payment_type}".')
        return {
            "amount": amount,
            "payment_date": self.faker.date_this_decade(),
            "payment_type_id": payment_type,
            "notes": self.faker.text(),
            "client_id": client_id,
            "provider_id": provider_id,
        }

    async def generate_random_statement(
        self, statement_type: enums.StatementPaymentType | None, amount: Decimal | None
    ) -> InsertDict:
        if statement_type is None:
            statement_type = self.faker.random_element(list(enums.StatementPaymentType))
        if amount is None:
            amount = self.faker.pydecimal(
                right_digits=2, left_digits=8, max_value=500000, positive=True
            )
        client_id: int | None = None
        provider_id: int | None = None
        match statement_type:
            case enums.StatementPaymentType.Client:
                client_id = self.faker.random_element(self.inserted_data["client"])["id"]
            case enums.StatementPaymentType.Provider:
                provider_id = self.faker.random_element(self.inserted_data["providers"])["id"]
            case _:
                raise ValueError(f'Unsupported StatementPaymentType "{statement_type}".')
        return {
            "statement_type_id": statement_type,
            "statement_date": self.faker.date_this_decade(),
            "total_amount": amount,
            "provider_id": provider_id,
            "client_id": client_id,
        }

    async def generate_random_statement_line(
        self, statement_id: int | None, max_value: int, holding_id: int
    ):
        amount = self.faker.pydecimal(
            right_digits=2, left_digits=8, max_value=max_value, positive=True
        )
        return await self.generate_random_statement_line_fixed_amount(
            statement_id, amount, holding_id
        )

    async def generate_random_statement_line_fixed_amount(
        self, statement_id: int | None, amount: Decimal, holding_id: int
    ):
        if statement_id is None:
            statement_id = self.faker.random_element(self.inserted_data["fee_statement"])["id"]

        return {
            "statement_id": statement_id,
            "amount": amount,
            "type_id": self.faker.random_element(list(enums.FeeType)),
            "holding_id": holding_id,
        }

    async def insert_random_adviser(self) -> InsertDict:
        adviser_data = await self.generate_random_adviser()
        [adviser_data_user] = await self.session.execute(
            text(
                "INSERT INTO users (email, first_name, last_name, type)"
                " VALUES(:email, :first_name, :last_name, :type)"
                " RETURNING id, email, first_name, last_name, type"
            ),
            adviser_data,
        )
        adviser_data_user = adviser_data_user._asdict()
        [adviser_data_owner] = await self.session.execute(
            text("INSERT INTO owners (id, roles) VALUES (:id, :roles) RETURNING (id, roles)"),
            adviser_data_user | adviser_data,
        )
        adviser_data = adviser_data_owner._asdict() | adviser_data_user
        self.inserted_data["owners"].append(adviser_data)
        return adviser_data

    async def insert_random_client(self) -> InsertDict:
        client_data = await self.generate_random_client()
        [client_user_data] = await self.session.execute(
            text(
                "INSERT INTO users (email, first_name, last_name, type)"
                " VALUES(:email, :first_name, :last_name, :type)"
                " RETURNING id, email, first_name, last_name, type"
            ),
            client_data,
        )
        client_data = client_data | client_user_data._asdict()
        [client_client_data] = await self.session.execute(
            text(
                "INSERT INTO client (id, client_type_id, owner_id, title_id, date_of_birth, marital_status_id, currency, phone_number, is_mobile_available, client_source_id, client_status_id, chatbot_consent_granted, chatbot_consent_timestamp)"
                " VALUES (:id, :client_type_id, :owner_id, :title_id, :date_of_birth, :marital_status_id, :currency, :phone_number, :is_mobile_available, :client_source_id, :client_status_id, :chatbot_consent_granted, :chatbot_consent_timestamp)"
                " RETURNING (id, client_type_id, owner_id, title_id, date_of_birth, marital_status_id, currency, phone_number, is_mobile_available, client_source_id, client_status_id, chatbot_consent_granted, chatbot_consent_timestamp)"
            ),
            client_data | {"chatbot_consent_granted": False, "chatbot_consent_timestamp": None},
        )
        client_data = client_data | client_client_data._asdict()
        self.inserted_data["client"].append(client_data)
        return client_data

    async def insert_random_provider(self) -> InsertDict:
        provider_data = await self.generate_random_provider()
        [provider_data] = await self.session.execute(
            text(
                "INSERT INTO providers (name, is_active)"
                " VALUES (:name, :is_active)"
                " RETURNING id, name, is_active"
            ),
            provider_data,
        )
        provider_data = provider_data._asdict()
        self.inserted_data["providers"].append(provider_data)
        return provider_data

    async def insert_random_payment(
        self,
        payment_type: enums.StatementPaymentType | None = None,
        amount: Decimal | None = None,
    ) -> InsertDict:
        payment_data = await self.generate_random_payment(payment_type=payment_type, amount=amount)
        [payment_data] = await self.session.execute(
            text(
                "INSERT INTO fee_payments (amount, payment_date, payment_type_id, notes, client_id, provider_id)"
                " VALUES (:amount, :payment_date, :payment_type_id, :notes, :client_id, :provider_id)"
                " RETURNING id, amount, payment_date, payment_type_id, notes, client_id, provider_id"
            ),
            payment_data,
        )
        payment_data = dict(payment_data)
        self.inserted_data["fee_payments"].append(payment_data)
        return payment_data

    async def insert_random_statement(
        self,
        statement_type: enums.StatementPaymentType | None = None,
        amount: Decimal | None = None,
    ) -> InsertDict:
        statement_data = await self.generate_random_statement(statement_type, amount)
        [statement_data] = await self.session.execute(
            text(
                "INSERT INTO fee_statement (statement_type_id, statement_date, total_amount, provider_id, client_id)"
                " VALUES (:statement_type_id, :statement_date, :total_amount, :provider_id, :client_id)"
                " RETURNING id, statement_type_id, statement_date, total_amount, provider_id, client_id"
            ),
            statement_data,
        )
        statement_data = statement_data._asdict()
        self.inserted_data["fee_statement"].append(statement_data)
        holding_id = self.faker.random_element(self.inserted_data["client_holdings"])["id"]
        if statement_data["statement_type_id"] == enums.StatementPaymentType.Provider:
            amount = statement_data["total_amount"]
            while amount > 0:
                statement_line_data = await self.generate_random_statement_line(
                    statement_data["id"], int(amount), holding_id
                )
                [statement_line_data] = await self.session.execute(
                    text(
                        "INSERT INTO fee_statement_line_item (statement_id, amount, type_id, holding_id)"
                        " VALUES (:statement_id, :amount, :type_id, :holding_id)"
                        " RETURNING id, statement_id, amount, type_id, holding_id"
                    ),
                    statement_line_data,
                )
                statement_line_data = statement_line_data._asdict()
                amount -= statement_line_data["amount"]
        else:
            statement_line_data = await self.generate_random_statement_line_fixed_amount(
                statement_data["id"],
                Decimal(statement_data["total_amount"]),
                holding_id,
            )
            [statement_line_data] = await self.session.execute(
                text(
                    "INSERT INTO fee_statement_line_item (statement_id, amount, type_id, holding_id)"
                    " VALUES (:statement_id, :amount, :type_id, :holding_id)"
                    " RETURNING id, statement_id, amount, type_id, holding_id"
                ),
                statement_line_data,
            )
        return statement_data

    async def insert_fee_split_template(self):
        random_owner = self.faker.random_element(self.inserted_data["owners"])
        [[fee_split_template_id]] = await self.session.execute(
            text(
                "INSERT INTO fee_split_template (administrator_id, is_active, template_name)"
                " VALUES (:administrator_id, :is_active, :template_name)"
                " RETURNING id"
            ),
            {
                "administrator_id": random_owner["id"],
                "is_active": True,
                "template_name": "template name",
            },
        )
        await self.session.execute(
            text(
                "INSERT INTO fee_split_template_lines"
                " (fee_split_template_id, administrator_id, role_id, type_id, split_initial, split_ongoing, is_payable)"
                " VALUES "
                " (:fee_split_template_id, :administrator_id, :role_id, :type_id, :split_initial1, :split_ongoing1,"
                " :is_payable),"
                " (:fee_split_template_id, :administrator_id, :role_id, :type_id, :split_initial2, :split_ongoing2,"
                " :is_payable)"
            ),
            {
                "fee_split_template_id": fee_split_template_id,
                "administrator_id": random_owner["id"],
                "role_id": enums.FeeRoleType.Adviser,
                "type_id": enums.FeeSplitType.Standard_Fee,
                "is_payable": True,
                "split_initial1": Decimal("0.75"),
                "split_initial2": Decimal("0.25"),
                "split_ongoing1": Decimal("0.75"),
                "split_ongoing2": Decimal("0.25"),
            },
        )
        return fee_split_template_id

    async def insert_fee(
        self,
        holding_id: int,
        expected_date: date,
        amount: Decimal,
        estimate_type: enums.HoldingFeeType = enums.HoldingFeeType.ESTIMATE,
    ) -> None:
        await self.session.execute(
            text(
                "INSERT INTO client_holding_expected_fees"
                " (amount, due_date, fee_type, holding_id, estimate_type)"
                " VALUES"
                " (:amount, :expected_date, :fee_type, :holding_id, :estimate_type)"
            ),
            {
                "amount": amount,
                "expected_date": expected_date,
                "fee_type": 1,
                "holding_id": holding_id,
                "estimate_type": estimate_type,
            },
        )

    async def insert_review_group(
        self, client_ids: list[int], review_frequency="ANNUAL", review_month=6
    ) -> int:
        [[review_group_id]] = await self.session.execute(
            text(
                "INSERT INTO review_group"
                " (review_frequency, review_month, is_active)"
                " VALUES"
                " (:review_frequency, :review_month, true)"
                "RETURNING id"
            ),
            {
                "review_frequency": review_frequency,
                "review_month": review_month,
            },
        )
        for client_id in client_ids:
            await self.session.execute(
                text(
                    "INSERT INTO client_review_group_link"
                    " (client_review_group_id, client_id)"
                    " VALUES"
                    " (:client_review_group_id, :client_id)"
                ),
                {
                    "client_review_group_id": review_group_id,
                    "client_id": client_id,
                },
            )
        return review_group_id

    async def insert_review_slot(self, review_group_id, slot_month=6, slot_year=2024) -> int:
        [[review_slot_id]] = await self.session.execute(
            text(
                "INSERT INTO review_slots"
                " (review_group_id, slot_month, slot_year, version)"
                " VALUES"
                " (:review_group_id, :slot_month, :slot_year, 1)"
                "RETURNING id"
            ),
            {
                "review_group_id": review_group_id,
                "slot_month": slot_month,
                "slot_year": slot_year,
            },
        )
        return review_slot_id

    async def insert_client_address(
        self,
        client_id: int,
        address_line_one: str | None = None,
        address_line_two: str | None = None,
        address_line_three: str | None = None,
        address_line_four: str | None = None,
        city: str | None = None,
        post_code: str | None = None,
        country_id: int | None = None,
        moved_in_date: date | None = None,
        moved_out_date: date | None = None,
        is_primary: bool | None = True,
    ) -> int:
        if address_line_one is None:
            address_line_one = self.faker.street_address()
        if city is None:
            city = self.faker.city()
        if post_code is None:
            post_code = self.faker.postcode()

        [[address_id]] = await self.session.execute(
            text(
                "INSERT INTO client_addresses"
                " (client_id, address_line_one, address_line_two, address_line_three, address_line_four,"
                " city, post_code, country_id, moved_in_date, moved_out_date, is_primary)"
                " VALUES"
                " (:client_id, :address_line_one, :address_line_two, :address_line_three, :address_line_four,"
                " :city, :post_code, :country_id, :moved_in_date, :moved_out_date, :is_primary)"
                " RETURNING id"
            ),
            {
                "client_id": client_id,
                "address_line_one": address_line_one,
                "address_line_two": address_line_two,
                "address_line_three": address_line_three,
                "address_line_four": address_line_four,
                "city": city,
                "post_code": post_code,
                "country_id": country_id,
                "moved_in_date": moved_in_date,
                "moved_out_date": moved_out_date,
                "is_primary": is_primary,
            },
        )
        return address_id

    async def insert_client_relation(
        self,
        client_id: int,
        first_name: str | None = None,
        last_name: str | None = None,
        date_of_birth: date | None = None,
        relationship_type: enums.Relationship | None = None,
    ) -> int:
        if first_name is None:
            first_name = self.faker.first_name()
        if last_name is None:
            last_name = self.faker.last_name()
        if date_of_birth is None:
            date_of_birth = self.faker.date_of_birth(minimum_age=18)
        if relationship_type is None:
            relationship_type = self.faker.random_element(list(enums.Relationship))

        [[relation_id]] = await self.session.execute(
            text(
                "INSERT INTO client_relations"
                " (client_id, first_name, last_name, date_of_birth, relationship_type)"
                " VALUES"
                " (:client_id, :first_name, :last_name, :date_of_birth, :relationship_type)"
                " RETURNING id"
            ),
            {
                "client_id": client_id,
                "first_name": first_name,
                "last_name": last_name,
                "date_of_birth": date_of_birth,
                "relationship_type": relationship_type,
            },
        )
        return relation_id
