from abc import ABC, abstractmethod

from database.query_builders.aml_query_builder import AMLSearchQueryBuilder
from database.repositories.aml.aml_search_dtos import AddAMLSearchResultDTO, AMLSearchDTO
from database.repositories.aml.aml_search_mapper import (
    AMLSearchDTOMapper,
    AMLSearchResultModelMapper,
)
from database.repositories.base.base_db_operations import BaseSqlAlchemyOperations


class AbstractAMLSearchRepository(ABC):
    @abstractmethod
    async def get_latest_by_client_id(self, client_id: int) -> AMLSearchDTO | None:
        raise NotImplementedError

    @abstractmethod
    async def add_search_result(self, aml_search_result: AddAMLSearchResultDTO) -> bool:
        raise NotImplementedError


class SQLAlchemyAMLSearchRepository(BaseSqlAlchemyOperations, AbstractAMLSearchRepository):
    async def get_latest_by_client_id(self, client_id: int) -> AMLSearchDTO | None:
        statement = AMLSearchQueryBuilder().where_client_id(client_id).build()
        return await self._first(statement, AMLSearchDTOMapper)

    async def add_search_result(self, aml_search_result: AddAMLSearchResultDTO) -> bool:
        await self.add(aml_search_result, AMLSearchResultModelMapper)

        return True
