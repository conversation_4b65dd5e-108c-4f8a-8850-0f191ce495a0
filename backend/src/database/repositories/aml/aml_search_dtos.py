from dataclasses import dataclass
from datetime import date

from api.enums import IdentityCheckResult, IdentityMatchLevel


@dataclass(frozen=True, slots=True)
class AMLSearchCriteriaDTO:
    first_name: str
    last_name: str
    address: str
    city: str
    post_code: str
    country_code: str
    date_of_birth: date


@dataclass(frozen=True, slots=True)
class AMLSearchDTO:
    result: IdentityCheckResult
    date: date
    detail: list
    criteria: AMLSearchCriteriaDTO


@dataclass(frozen=True, slots=True)
class AddAMLSearchResultDTO:
    client_id: int
    search_criteria: AMLSearchCriteriaDTO
    found: bool
    match_level: IdentityMatchLevel
    full_results: dict
