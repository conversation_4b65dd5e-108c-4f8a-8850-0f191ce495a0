from api.enums import IdentityCheckResult, IdentityMatchLevel
from database.models import AMLSearchResultModel
from database.repositories.aml.aml_search_dtos import (
    AddAMLSearchResultDTO,
    AMLSearchCriteriaDTO,
    AMLSearchDTO,
)
from database.repositories.base.base_mapper import Base<PERSON><PERSON><PERSON>ap<PERSON>, BaseModelMapper


def evaluate_identity_check_result(
    match_level: str,
    was_found: bool,
) -> IdentityCheckResult:
    if not was_found or match_level == IdentityMatchLevel.Low:
        return IdentityCheckResult.FAIL
    elif match_level == IdentityMatchLevel.Medium:
        return IdentityCheckResult.REFER
    else:
        return IdentityCheckResult.PASS


class AMLSearchDTOMapper(BaseDTOMapper):
    @staticmethod
    def extract_details(details) -> list:
        data = details["validation_details"]
        codes = data["Codes"].split(";") if data["Codes"] else []

        return [{"code": code, "description": data.get(code)} for code in codes]

    @staticmethod
    def to_dto(model: AMLSearchResultModel) -> AMLSearchDTO:
        print(model.search_criteria)
        return AMLSearchDTO(
            result=evaluate_identity_check_result(
                match_level=model.match_level,
                was_found=model.found,
            ),
            date=model.created_at.date(),
            criteria=AMLSearchCriteriaDTO(
                first_name=model.search_criteria.get("FirstName"),
                last_name=model.search_criteria.get("LastName"),
                address=model.search_criteria.get("Address"),
                city=model.search_criteria.get("City"),
                country_code=model.search_criteria.get("CountryCode"),
                post_code=model.search_criteria.get("PostalCode"),
                date_of_birth=model.search_criteria.get("DateOfBirth"),
            ),
            detail=AMLSearchDTOMapper.extract_details(model.full_results),
        )


class AMLSearchResultModelMapper(BaseModelMapper):
    @staticmethod
    def to_model(data: AddAMLSearchResultDTO) -> AMLSearchResultModel:
        return AMLSearchResultModel(
            client_id=data.client_id,
            search_criteria={
                "FirstName": data.search_criteria.first_name,
                "LastName": data.search_criteria.last_name,
                "Address": data.search_criteria.address,
                "City": data.search_criteria.city,
                "CountryCode": data.search_criteria.country_code,
                "PostalCode": data.search_criteria.post_code,
                "DateOfBirth": data.search_criteria.date_of_birth,
            },
            found=data.found,
            match_level=data.match_level,
            full_results=data.full_results,
        )
