from abc import ABC, abstractmethod

from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession

from app.shared_kernel.app_session import AppSession
from app.shared_kernel.dtos.client_dtos import AddressDTO
from database.models.client_models import ClientAddressModel
from database.repositories.base.base_db_operations import BaseSqlAlchemyOperations
from database.repositories.base.base_mapper import BaseModelMapper


class AbstractClientAddressRepository(ABC):
    @abstractmethod
    async def add_address(self, client_id: int, dto: AddressDTO) -> None:
        raise NotImplementedError

    @abstractmethod
    async def update_address(self, client_id: int, address_id: int, dto: AddressDTO) -> None:
        raise NotImplementedError


class ClientAddressModelMapper(BaseModelMapper):
    @staticmethod
    def to_model(dto: AddressDTO) -> ClientAddressModel:  # type: ignore
        return ClientAddressModel(
            address_line_one=dto.address_line_one,
            address_line_two=dto.address_line_two,
            address_line_three=dto.address_line_three,
            address_line_four=dto.address_line_four,
            city=dto.city,
            country_id=dto.country_id,
            post_code=dto.post_code,
            moved_in_date=dto.moved_in_date,
            moved_out_date=dto.moved_out_date,
            is_primary=dto.is_primary,
        )


class ClientAddressRepository(BaseSqlAlchemyOperations, AbstractClientAddressRepository):
    def __init__(self, session: AsyncSession, app_session: AppSession | None = None):
        super().__init__(session, app_session)

    async def add_address(self, client_id: int, dto: AddressDTO) -> None:
        # If this address is being set as primary, clear other primary addresses for this client
        if dto.is_primary:
            await self._clear_other_primary_addresses_for_client_by_id(client_id)

        model = ClientAddressModelMapper.to_model(dto)
        model.client_id = client_id  # type: ignore
        self.session.add(model)

    async def update_address(self, client_id: int, address_id: int, dto: AddressDTO) -> None:
        # If this address is being set as primary, clear other primary addresses for this client
        if dto.is_primary:
            await self._clear_other_primary_addresses_for_client_by_id(client_id)

        stmt = (
            update(ClientAddressModel)
            .where(ClientAddressModel.id == address_id)
            .values(
                address_line_one=dto.address_line_one,
                address_line_two=dto.address_line_two,
                address_line_three=dto.address_line_three,
                address_line_four=dto.address_line_four,
                city=dto.city,
                post_code=dto.post_code,
                country_id=dto.country_id,
                moved_in_date=dto.moved_in_date,
                moved_out_date=dto.moved_out_date,
                is_primary=dto.is_primary,
            )
        )
        await self.session.execute(stmt)

    async def _clear_other_primary_addresses_for_client_by_id(self, client_id: int) -> None:
        """Clear is_primary flag for all addresses of a specific client."""
        clear_stmt = (
            update(ClientAddressModel)
            .where(ClientAddressModel.client_id == client_id)
            .values(is_primary=False)
        )
        await self.session.execute(clear_stmt)
