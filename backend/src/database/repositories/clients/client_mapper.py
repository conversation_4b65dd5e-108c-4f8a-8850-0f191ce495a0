from app.shared_kernel.dtos.administrators import <PERSON><PERSON>asi<PERSON><PERSON><PERSON>
from app.shared_kernel.dtos.client_dtos import (
    AddressDTO,
    ClientDetailDTO,
    ClientLinkDTO,
    ClientSummaryDTO,
    CountryDTO,
)
from database.models import (
    ClientAddressModel,
    ClientLinkModel,
    ClientModel,
    CountryModel,
    OwnerModel,
)
from database.repositories.base.base_mapper import BaseDTOMapper


class AdviserDTOMapper(BaseDTOMapper):
    @staticmethod
    def to_dto(model: OwnerModel) -> AdministratorBasicDTO:
        return AdministratorBasicDTO(
            id=model.id,
            first_name=model.first_name,
            last_name=model.last_name,
        )


class CountryDTOMapper(BaseDTOMapper):
    @staticmethod
    def to_dto(model: CountryModel) -> CountryDTO:
        return CountryDTO(
            id=model.id, name=model.name, iso_alpha2=model.iso_alpha2, iso_alpha3=model.iso_alpha3
        )


class ClientAddressDTOMapper(BaseDTOMapper):
    @staticmethod
    def to_dto(address: <PERSON>lientAddressModel) -> AddressDTO:
        return AddressDTO(
            address_line_one=address.address_line_one,
            address_line_two=address.address_line_two,
            address_line_three=address.address_line_three,
            address_line_four=address.address_line_four,
            city=address.city,
            country_id=address.country_id,
            country=CountryDTOMapper.to_dto(address.country) if address.country else False,
            post_code=address.post_code,
            moved_in_date=address.moved_in_date,
            moved_out_date=address.moved_out_date,
            is_primary=address.is_primary,
        )


class ClientLinkDTOMapper(BaseDTOMapper):
    @staticmethod
    def to_dto(link: ClientLinkModel) -> ClientLinkDTO:
        return ClientLinkDTO(
            client_id=link.clients.id,
            first_name=link.clients.first_name,
            last_name=link.clients.last_name,
            link_relationship_id=link.link_relationship,
        )


class ClientDTOMapper(BaseDTOMapper):
    @staticmethod
    def to_dto(model: ClientModel) -> ClientDetailDTO:
        return ClientDetailDTO(
            id=model.id,
            email=model.email,
            no_email_reason_id=model.no_email_reason_id,
            title_id=model.title_id,
            first_name=model.first_name,
            last_name=model.last_name,
            date_of_birth=model.date_of_birth,
            phone_number=model.phone_number,
            mobile_number=model.mobile_number,
            marital_status_id=model.marital_status_id,
            gender_id=model.gender_id,
            gender=model.gender.name if model.gender else None,
            nationality_id=model.nationality_id,
            type=model.type,
            client_source_id=model.client_source_id,
            client_status_id=model.client_status_id,
            client_type_id=model.client_type_id,
            addresses=[ClientAddressDTOMapper.to_dto(address) for address in model.addresses],
            links=[ClientLinkDTOMapper.to_dto(link) for link in model.links],
            advisor=AdviserDTOMapper.to_dto(model.owner) if model.owner else None,
            client_agreement_id=model.client_agreement_id,
            privacy_notice_id=model.privacy_notice_id,
            chatbot_consent_granted=model.chatbot_consent_granted,
            chatbot_consent_timestamp=model.chatbot_consent_timestamp,
        )


class ClientSummaryDTOMapper(BaseDTOMapper):
    @staticmethod
    def to_dto(model: ClientModel) -> ClientSummaryDTO:
        return ClientSummaryDTO(
            id=model.id,
            email=model.email,
            first_name=model.first_name,
            last_name=model.last_name,
            client_status_id=model.client_status_id,
            client_type_id=model.client_type_id,
            accounts=model.account_count,
            links=model.link_count,
            owner_id=model.owner.id if model.owner else None,
            owner_first_name=model.owner.first_name if model.owner else None,
            owner_last_name=model.owner.last_name if model.owner else None,
            chatbot_consent_granted=model.chatbot_consent_granted,
        )
