from abc import ABC, abstractmethod
from datetime import datetime, timezone

from sqlalchemy import update

from api.enums import ClientStatus
from app.shared_kernel.dtos.client_dtos import ClientDetailDTO, ClientSummaryDTO
from database.models.client_models import ClientModel
from database.query_builders.client_query_builder import ClientQueryBuilder
from database.repositories.base.base_db_operations import BaseSqlAlchemyOperations
from database.repositories.clients.client_mapper import Client<PERSON><PERSON><PERSON>apper, ClientSummaryDTOMapper


class AbstractClientRepository(ABC):
    @abstractmethod
    async def get_by_id(self, client_id: int) -> ClientDetailDTO | None:
        raise NotImplementedError

    @abstractmethod
    async def get_by_filter(
        self,
        *,
        client_id: int | None,
        client_type_id: int | None,
        adviser_id: int | None,
        client_status_id: ClientStatus | None,
        text: str | None,
        page_size: int = 10,
        offset: int = 0,
    ) -> list[ClientSummaryDTO]:
        raise NotImplementedError

    @abstractmethod
    async def get_by_filter_count(
        self,
        *,
        client_id: int | None = None,
        client_type_id: int | None = None,
        adviser_id: int | None = None,
        client_status_id: ClientStatus | None = None,
        text: str | None = None,
    ) -> int:
        raise NotImplementedError

    @abstractmethod
    async def get_clients_missing_aml_monitoring(self) -> list[ClientSummaryDTO]:
        raise NotImplementedError

    @abstractmethod
    async def grant_chatbot_consent(self, client_id: int) -> None:
        raise NotImplementedError

    @abstractmethod
    async def withdraw_chatbot_consent(self, client_id: int) -> None:
        raise NotImplementedError


class SQLAlchemyClientRepository(BaseSqlAlchemyOperations, AbstractClientRepository):
    async def get_by_id(self, client_id: int) -> ClientDetailDTO | None:
        statement = (
            ClientQueryBuilder()
            .with_owner()
            .with_addresses()
            .with_gender()
            .with_links()
            .where_client_id(client_id)
            .build()
        )

        return await self._one_or_none(statement, ClientDTOMapper)

    async def get_by_filter(
        self,
        *,
        client_id: int | None,
        client_type_id: int | None,
        adviser_id: int | None,
        client_status_id: ClientStatus | None,
        text: str | None,
        page_size: int = 10,
        offset: int = 0,
    ) -> list[ClientSummaryDTO]:
        statement = (
            ClientQueryBuilder()
            .with_owner()
            .with_addresses()
            .with_links()
            .filter(
                client_id=client_id,
                client_type_id=client_type_id,
                adviser_id=adviser_id,
                client_status_id=client_status_id,
                text=text,
            )
            .order_by_newest_first()
            .build(page_size=page_size, offset=offset)
        )

        return await self._all(statement, ClientSummaryDTOMapper)

    async def get_by_filter_count(
        self,
        *,
        client_id: int | None = None,
        client_type_id: int | None = None,
        adviser_id: int | None = None,
        client_status_id: ClientStatus | None = None,
        text: str | None = None,
    ) -> int:
        statement = (
            ClientQueryBuilder()
            .with_owner()
            .with_addresses()
            .with_links()
            .filter(
                client_id=client_id,
                client_type_id=client_type_id,
                adviser_id=adviser_id,
                client_status_id=client_status_id,
                text=text,
            )
            .count()
        )

        return await self._value(statement)

    async def get_clients_missing_aml_monitoring(self) -> list[ClientSummaryDTO]:
        statement = (
            ClientQueryBuilder()
            .where_client_status(ClientStatus.ActiveOngoing)
            .where_missing_aml_session()
            .build()
        )

        return await self._all(statement, ClientSummaryDTOMapper)

    async def grant_chatbot_consent(self, client_id: int) -> None:
        stmt = (
            update(ClientModel)
            .where(ClientModel.id == client_id)
            .values(
                chatbot_consent_granted=True,
                chatbot_consent_timestamp=datetime.now(timezone.utc).replace(tzinfo=None),
            )
        )
        await self.session.execute(stmt)

    async def withdraw_chatbot_consent(self, client_id: int) -> None:
        stmt = (
            update(ClientModel)
            .where(ClientModel.id == client_id)
            .values(chatbot_consent_granted=False, chatbot_consent_timestamp=None)
        )
        await self.session.execute(stmt)
