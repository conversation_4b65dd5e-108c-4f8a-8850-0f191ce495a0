from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String, func
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship

from database.models import BaseDBModel


class AMLSearchResultModel(BaseDBModel):
    __tablename__ = "aml_searches"

    id = Column(Integer, primary_key=True)
    client_id = Column(Integer, ForeignKey("client.id"), index=True)
    created_at = Column(DateTime, nullable=False, server_default=func.now())

    search_criteria = Column(JSONB)
    found = Column(Boolean)
    match_level = Column(String)
    full_results = Column(JSONB)

    client = relationship("ClientModel", back_populates="aml_searches")


class AMLMonitoringSessionModel(BaseDBModel):
    __tablename__ = "aml_monitoring_sessions"

    id = Column(Integer, primary_key=True)
    client_id = Column(Integer, Foreign<PERSON>ey("client.id"), index=True)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=False, server_default=func.now(), onupdate=datetime.utcnow
    )
    raw_data = Column(JSONB)

    zignsec_session_id = Column(UUID(as_uuid=True), nullable=False, unique=True)

    matches = relationship(
        "AMLMonitoringSessionMatchModel",
        back_populates="session",
        cascade="all, delete-orphan",
    )


class AMLMonitoringSessionMatchModel(BaseDBModel):
    __tablename__ = "aml_monitoring_session_match"

    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey("aml_monitoring_sessions.id"), index=True)

    match_id = Column(UUID(as_uuid=True), nullable=False)

    match_detail = Column(JSONB, nullable=False)

    match_status = Column(Integer, nullable=False)

    session = relationship("AMLMonitoringSessionModel", back_populates="matches")
