from __future__ import annotations

from typing import Any

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    Date,
    DateTime,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    func,
    select,
)
from sqlalchemy.orm import column_property, relationship

from api.common import EnumFromInt, EnumFromString
from api.common.months import Month
from api.enums import (
    ClientAgreement,
    ClientLinkRelationship,
    ClientSource,
    ClientStatus,
    ClientType,
    PrivacyNotice,
    Relationship,
)
from app.clients.domain.enums import ReviewFrequency
from database.models import BaseDBModel, UserModel
from database.models.clients_links_tables import (
    client_case_link,
    client_goals_link,
    client_review_group_link,
)
from database.models.holdings_models import ClientHoldingModel, ClientHoldingsLinkModel
from database.models.product_models import ProductModel, ProductTypeModel


class InvalidMonth(Exception):
    pass


class InvalidReviewFrequency(Exception):
    pass


class ReviewGroupModel(BaseDBModel):
    __tablename__ = "review_group"

    id = Column(Integer, primary_key=True)
    description = Column(String)
    review_frequency = Column(EnumFromString(ReviewFrequency), nullable=True)
    review_month = Column(EnumFromInt(Month, nullable=True))
    is_active = Column(Boolean)

    review_slots = relationship(
        "ReviewSlotModel",
        order_by="asc(ReviewSlotModel.slot_year), asc(ReviewSlotModel.slot_month)",
        back_populates="review_group",
    )

    clients = relationship(
        "ClientModel",
        secondary=client_review_group_link,
        back_populates="review_groups",
    )


class ClientLinkModel(BaseDBModel):
    __tablename__ = "client_links"
    __versioned__ = {}

    client_id = Column(ForeignKey("client.id"), primary_key=True)
    linked_client_id = Column(ForeignKey("client.id"), primary_key=True)
    link_relationship = Column(EnumFromInt(ClientLinkRelationship), nullable=False)

    def __eq__(self, other: Any) -> bool:
        if not isinstance(other, ClientLinkModel):
            return False
        return self.client_id == other.client_id and self.linked_client_id == other.linked_client_id

    def __repr__(self) -> str:
        return (
            f"ClientLink(client_id={self.client_id!r}, linked_client_id={self.linked_client_id!r})"
        )

    clients = relationship(
        "ClientModel",
        foreign_keys=[linked_client_id],
    )


class ClientModel(UserModel):
    __tablename__ = "client"
    __versioned__ = {}

    id = Column(Integer, ForeignKey("users.id"), primary_key=True)
    client_type_id = Column(EnumFromInt(ClientType), nullable=False, default=ClientType.Individual)
    org_name = Column(String)
    owner_id = Column(Integer, ForeignKey("owners.id"))
    title_id = Column(Integer, ForeignKey("personal_titles.id"))
    date_of_birth = Column(Date)
    marital_status_id = Column(Integer, ForeignKey("marital_statuses.id"))
    nationality_id = Column(Integer, ForeignKey("nationalities.id"))
    birth_country_id = Column(Integer, ForeignKey("countries.id"))
    primary_country_id = Column(Integer, ForeignKey("countries.id"))
    secondary_country_id = Column(Integer, ForeignKey("countries.id"), nullable=True)
    currency = Column(String, nullable=False, default="GBP")
    phone_number = Column(String)
    mobile_number = Column(String)
    no_email_reason_id = Column(Integer, ForeignKey("no_email_reasons.id"), nullable=True)
    is_mobile_available = Column(Boolean, default=True)
    annual_review_date = Column(Date)  # Move to Case???
    client_source_id = Column(
        EnumFromInt(ClientSource),
        default=ClientSource.NotSet,
        nullable=False,
    )
    client_status_id = Column(
        EnumFromInt(ClientStatus), default=ClientStatus.LeadLive, nullable=False
    )
    gender_id = Column(Integer, ForeignKey("genders.id"), nullable=True)
    review_frequency = Column(EnumFromString(ReviewFrequency))
    review_month = Column(EnumFromInt(Month, nullable=True))
    original_id = Column(String, nullable=True)
    client_agreement_id = Column(EnumFromInt(ClientAgreement, nullable=True))
    privacy_notice_id = Column(EnumFromInt(PrivacyNotice, nullable=True))
    chatbot_consent_granted = Column(Boolean, default=False, nullable=False)
    chatbot_consent_timestamp = Column(DateTime, nullable=True)

    def set_review_date(self, month: int, frequency: str) -> None:
        try:
            self.review_month = Month(month)
        except ValueError:
            raise InvalidMonth()

        try:
            self.review_frequency = ReviewFrequency(frequency)
        except ValueError:
            raise InvalidReviewFrequency()

    def clear_review_date(self) -> None:
        self.review_month = None
        self.review_frequency = None

    owner = relationship(
        "OwnerModel",
        foreign_keys=[owner_id],
    )
    factfind = relationship(
        "ClientFactfindModel",
        back_populates="client",
        uselist=False,
        cascade="all, delete, delete-orphan",
    )
    addresses = relationship(
        "ClientAddressModel",
        back_populates="client",
        order_by="asc(ClientAddressModel.id)",
    )
    relations = relationship(
        "ClientRelationsModel",
        back_populates="client",
        order_by="asc(ClientRelationsModel.id)",
    )
    cashflows = relationship(
        "ClientIncomeExpenditureModel",
        back_populates="client",
        order_by="ClientIncomeExpenditureModel.id",
    )
    holdings = relationship(
        "ClientHoldingsLinkModel",
        back_populates="client",
    )
    cases = relationship(
        "ClientCaseModel",
        secondary=client_case_link,
        back_populates="clients",
    )
    goals = relationship(
        "ClientGoalsModel",
        secondary=client_goals_link,
        viewonly=True,
        uselist=True,
    )
    links = relationship(
        "ClientLinkModel",
        foreign_keys=[ClientLinkModel.client_id],
        cascade="save-update, delete, delete-orphan",
    )
    aml_searches = relationship(
        "AMLSearchResultModel",
        back_populates="client",
        order_by="desc(AMLSearchResultModel.created_at)",
    )
    gender = relationship("GenderModel")
    marital_status = relationship("MaritalStatusModel")
    no_email_reason = relationship("NoEmailReasonModel")
    title = relationship("PersonalTitleModel")

    review_groups = relationship(
        "ReviewGroupModel", secondary=client_review_group_link, back_populates="clients"
    )
    current_review_group = relationship(
        "ReviewGroupModel",
        secondary=client_review_group_link,
        secondaryjoin="and_(ReviewGroupModel.id == client_review_group_link.c.client_review_group_id, ReviewGroupModel.is_active == True)",
        uselist=False,
        viewonly=True,
    )
    birth_country = relationship("CountryModel", foreign_keys=[birth_country_id])
    primary_country = relationship("CountryModel", foreign_keys=[primary_country_id])
    secondary_country = relationship("CountryModel", foreign_keys=[secondary_country_id])

    link_count = column_property(
        select(func.count())
        .where(ClientLinkModel.client_id == id)
        .correlate_except(ClientLinkModel)
        .scalar_subquery()
    )

    account_count = column_property(
        select(func.count())
        .where(
            ClientHoldingsLinkModel.client_id == id,
            ProductTypeModel.product_type_group_id.notin_([8, 9]),
        )
        .join(ClientHoldingsLinkModel.holding)
        .join(ClientHoldingModel.product)
        .join(ProductModel.product_type)
        # .correlate_except(ClientHoldingsLinkModel)
        .scalar_subquery()
    )

    @property
    def primary_address(self) -> ClientAddressModel | None:
        for address in self.addresses:
            if address.is_primary:
                return address
        return None

    @property
    def validation_errors(self) -> list[str]:
        errors = []
        if (
            self.client_status_id == ClientStatus.ActiveOngoing
            and self.current_review_group is None
        ):
            errors.append("Missing Review setup")
        return errors

    __mapper_args__ = {"polymorphic_identity": "client", "polymorphic_load": "inline"}

    def __repr__(self) -> str:
        return f"{self.id} {self.first_name} {self.last_name} {self.client_status_id}"


class ClientAddressModel(BaseDBModel):
    __tablename__ = "client_addresses"
    __versioned__ = {}

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("client.id"))
    address_line_one = Column(String)
    address_line_two = Column(String)
    address_line_three = Column(String)
    address_line_four = Column(String)
    city = Column(String)
    post_code = Column(String)
    country_id = Column(Integer, ForeignKey("countries.id"))
    moved_in_date = Column(Date)
    moved_out_date = Column(Date)
    is_primary = Column(Boolean)

    client = relationship("ClientModel", back_populates="addresses")
    country = relationship("CountryModel")


class ClientRelationsModel(BaseDBModel):
    __tablename__ = "client_relations"
    __versioned__ = {}

    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("client.id"))
    first_name = Column(String)
    last_name = Column(String)
    date_of_birth = Column(Date)
    relationship_type = Column(EnumFromString(Relationship))

    client = relationship("ClientModel", back_populates="relations")
