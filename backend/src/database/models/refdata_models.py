from typing import Literal

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, Numeric, String
from sqlalchemy.orm import relationship

from api.common import EnumFromInt, EnumFromString
from api.enums import CashflowType, HoldingCategory, enums
from app.shared_kernel.vos.holding_vos import HoldingStatusDTO
from database.models import BaseDBModel


class IncomeExpenditureGroupsModel(BaseDBModel):
    __tablename__ = "incomes_expenditure_groups"

    id = Column(Integer, primary_key=True, index=True)
    type = Column(EnumFromString(CashflowType))
    name = Column(String)
    description = Column(String)


class IncomeExpenditureTypesModel(BaseDBModel):
    __tablename__ = "incomes_expenditure_types"

    id = Column(Integer, primary_key=True, index=True)
    income_expenditure_group_id = Column(Integer, ForeignKey("incomes_expenditure_groups.id"))
    name = Column(String)
    sort_order = Column(Integer)
    is_active = Column(Boolean, default=True)
    is_essential = Column(Boolean, default=None)

    flows = relationship("ClientIncomeExpenditureModel", backref="income_expenditure_type")
    income_expenditure_group = relationship(
        "IncomeExpenditureGroupsModel", backref="incomes_expenditure_type"
    )

    @property
    def type(self) -> Literal["income", "expenditure"] | None:
        if self.income_expenditure_group is None:
            return None
        else:
            match self.income_expenditure_group.type:
                case enums.CashflowType.Income:
                    return "income"
                case enums.CashflowType.Expenditure:
                    return "expenditure"
                case _:
                    return None


class GoalTypeModel(BaseDBModel):
    __tablename__ = "goal_types"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    code = Column(String)
    is_default = Column(Boolean, nullable=False)
    description = Column(String)
    client_goal = Column(Boolean, nullable=False)


class HoldingTypeModel(BaseDBModel):
    __tablename__ = "holding_types"

    id = Column(Integer, primary_key=True, index=True)
    holding_category = Column(EnumFromInt(HoldingCategory))
    name = Column(String)


class HoldingStatusModel(BaseDBModel):
    __tablename__ = "holding_status"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)

    def to_dto(self) -> HoldingStatusDTO:
        return HoldingStatusDTO(
            id=self.id,
            name=self.name,
        )


class ProviderModel(BaseDBModel):
    __tablename__ = "providers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    is_active = Column(Boolean)


class NationalitiesModel(BaseDBModel):
    __tablename__ = "nationalities"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)

    clients = relationship(
        "ClientModel", backref="nationalities", foreign_keys="ClientModel.nationality_id"
    )


class AdviceGroupModel(BaseDBModel):
    __tablename__ = "advice_group"
    id = Column(Integer, primary_key=True)
    name = Column(String, default="", nullable=False)


class AdviceTypesModel(BaseDBModel):
    __tablename__ = "advice_types"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    proposed = Column(Boolean, nullable=False)
    existing = Column(Boolean, nullable=False)
    advice_group_id = Column(Integer, ForeignKey("advice_group.id"))
    requires_amount = Column(Boolean, nullable=False)
    requires_frequency = Column(Boolean, nullable=False)
    requires_portfolio = Column(Boolean, nullable=False)
    requires_account = Column(Boolean, nullable=False)
    complementary_advice_type_id = Column(Integer, ForeignKey("advice_types.id"), nullable=True)

    advice_group = relationship("AdviceGroupModel", backref="advice_types", uselist=False)


class GenderModel(BaseDBModel):
    __tablename__ = "genders"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)


class MaritalStatusModel(BaseDBModel):
    __tablename__ = "marital_statuses"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)


class PersonalTitleModel(BaseDBModel):
    __tablename__ = "personal_titles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)


class CountryModel(BaseDBModel):
    __tablename__ = "countries"

    id = Column(Integer, primary_key=True, index=True)
    iso_alpha2 = Column(String, nullable=False)
    iso_alpha3 = Column(String, nullable=False)
    name = Column(String, nullable=False)


class NoEmailReasonModel(BaseDBModel):
    __tablename__ = "no_email_reasons"

    id = Column(Integer, primary_key=True, index=True)
    reason = Column(String)


class PortfolioModelModel(BaseDBModel):
    __tablename__ = "portfolio_models"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)


class FundChargeModel(BaseDBModel):
    __tablename__ = "fund_charges"

    isin = Column(String, primary_key=True, index=True)
    charge_percent = Column(Numeric(5, 2), nullable=False)
