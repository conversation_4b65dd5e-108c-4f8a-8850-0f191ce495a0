from typing import Self
from uuid import UUID

from sqlalchemy import desc, select
from sqlalchemy.orm import selectinload

from database.models import (
    AMLMonitoringSessionModel,
    AMLSearchResultModel,
)
from database.query_builders.base_query_builder import BaseSqlalchemyQueryBuilder


class AMLSearchQueryBuilder(BaseSqlalchemyQueryBuilder):
    def query(self):
        return select(AMLSearchResultModel).order_by(desc(AMLSearchResultModel.created_at))

    def where_client_id(self, client_id: int) -> Self:
        return self.where(AMLSearchResultModel.client_id == client_id)


class AMLMonitoringQueryBuilder(BaseSqlalchemyQueryBuilder):
    def query(self):
        return select(AMLMonitoringSessionModel).options(
            selectinload(AMLMonitoringSessionModel.matches)
        )

    def where_client_id(self, client_id: int) -> Self:
        return self.where(AMLMonitoringSessionModel.client_id == client_id)

    def where_session_id(self, session_id: UUID) -> Self:
        return self.where(AMLMonitoringSessionModel.zignsec_session_id == session_id)
