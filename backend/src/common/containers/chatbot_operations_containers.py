from dependency_injector import containers, providers

from app.chatbot_operations.services.get_client import GetClientService
from app.chatbot_operations.services.get_client_addresses import GetClientAddressesService
from app.chatbot_operations.services.update_client import UpdateClientService
from app.company_data.services.company_data import CompanyDataService


class ChatbotOperationsServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()
    client_services = providers.DependenciesContainer()

    get_company_data_service = providers.Factory(
        CompanyDataService,
        logger=core.logger,
    )

    get_client_service = providers.Factory(
        GetClientService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    update_client_service = providers.Factory(
        UpdateClientService,
        logger=core.logger,
        client_query_services=client_services.client_query_services,
        update_client_service=client_services.update_client,
    )

    get_client_addresses_service = providers.Factory(
        GetClientAddressesService,
        session=database.session,
        logger=core.logger,
    )
