from dependency_injector import containers, providers

from app.chatbot.interface.conversation_history_interface import (
    ConversationHistoryStore,
    RedisConversationStore,
    S3ConversationStore,
)
from app.chatbot.services.get_messages import GetMessages
from app.chatbot.services.internal_client import InternalClient
from app.chatbot.services.new_conversation import NewConversation
from app.chatbot.services.post_message.post_message import PostMessage


class ChatbotServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()

    internal_client = providers.Factory(
        InternalClient,
        logger=core.logger,
    )

    redis_store = providers.Factory(
        RedisConversationStore,
        redis_gateway=gateway.redis_gateway,
    )

    s3_store = providers.Factory(
        S3ConversationStore,
        s3_gateway=gateway.s3_gateway,
    )

    conversation_history_store = providers.Factory(
        ConversationHistoryStore,
        logger=core.logger,
        cache=redis_store,
        storage=s3_store,
    )

    get_messages = providers.Factory(
        GetMessages,
        logger=core.logger,
        conversation_history_store=conversation_history_store,
    )

    post_message = providers.Factory(
        PostMessage,
        logger=core.logger,
        internal_client=internal_client,
        llm_gateway=gateway.bedrock_gateway,
        conversation_history_store=conversation_history_store,
    )

    new_conversation = providers.Factory(
        NewConversation,
        logger=core.logger,
        llm_gateway=gateway.bedrock_gateway,
        conversation_history_store=conversation_history_store,
    )
