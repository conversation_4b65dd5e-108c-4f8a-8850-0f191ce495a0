from dependency_injector import containers, providers

from app.clients.services.add_client import AddClientService
from app.clients.services.client_address_services import (
    AddClientAddressService,
    UpdateClientAddressService,
)
from app.clients.services.client_chatbot_permissions import (
    ClientChatbotPermissionsQueryService,
    ClientChatbotPermissionsService,
)
from app.clients.services.client_goals import ClientGoalsService
from app.clients.services.client_holdings import ClientHoldingsQueryService
from app.clients.services.client_note_services import ClientNotesService
from app.clients.services.client_query_services import ClientQueryServices
from app.clients.services.client_relations_service import (
    AddClientRelationService,
    GetClientRelationsService,
    UpdateClientRelationService,
)
from app.clients.services.client_snapshot import ClientSnapshotService
from app.clients.services.update_client import UpdateClientService
from app.clients.services.update_factfind import FactfindServices
from app.clients.services.view_factfind import GetFactfindDataService
from app.health_score.services.health_score_service import HealthScoreService
from app.profile_completeness.service import ClientProfileCompletenessService


class ClientServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()
    timestream_database = providers.DependenciesContainer()
    user_service = providers.DependenciesContainer()
    query_repositories = providers.DependenciesContainer()
    client_access_services = providers.DependenciesContainer()
    chatbot_permissions_service = providers.DependenciesContainer()

    notes_service = providers.Factory(
        ClientNotesService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    factfind_services = providers.Factory(
        FactfindServices,
        uow=database.unit_of_work,
        logger=core.logger,
        user=user_service.user_entity,
        ts_query_session=timestream_database.query_session,
    )

    update_client = providers.Factory(
        UpdateClientService,
        uow=database.unit_of_work,
        logger=core.logger,
        client_access=client_access_services.set_client_access,
    )

    add_client = providers.Factory(
        AddClientService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    add_client_address_service = providers.Factory(
        AddClientAddressService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    update_client_address_service = providers.Factory(
        UpdateClientAddressService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    client_query_services = providers.Factory(
        ClientQueryServices,
        uow=database.unit_of_work,
        cognito=gateway.cognito,
        logger=core.logger,
    )

    client_holdings_query_services = providers.Factory(
        ClientHoldingsQueryService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    client_goals_service = providers.Factory(
        ClientGoalsService,
        uow=database.unit_of_work,
        logger=core.logger,
        user=user_service.user_entity,
    )

    get_factfind_data_service = providers.Factory(
        GetFactfindDataService,
        uow=database.unit_of_work,
        logger=core.logger,
        ts_query_session=timestream_database.query_session,
    )

    client_snapshot_service = providers.Factory(
        ClientSnapshotService,
        uow=database.unit_of_work,
        logger=core.logger,
        ts_query_session=timestream_database.query_session,
    )

    health_score_service = providers.Factory(
        HealthScoreService,
        uow=database.unit_of_work,
        ts_query_session=timestream_database.query_session,
        logger=core.logger,
    )

    get_client_relations_service = providers.Factory(
        GetClientRelationsService,
        session_factory=database.session,
        logger=core.logger,
    )

    add_client_relation_service = providers.Factory(
        AddClientRelationService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    update_client_relation_service = providers.Factory(
        UpdateClientRelationService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    profile_completeness_service = providers.Factory(
        ClientProfileCompletenessService,
        client_repo=query_repositories.client_repository,
        logger=core.logger,
    )

    chatbot_permissions_query_service = providers.Factory(
        ClientChatbotPermissionsQueryService,
        logger=core.logger,
        client_repository=query_repositories.client2_repository,
    )

    chatbot_permissions_service = providers.Factory(
        ClientChatbotPermissionsService,
        uow=database.unit_of_work,
        logger=core.logger,
        cognito_gateway=gateway.cognito,
    )
