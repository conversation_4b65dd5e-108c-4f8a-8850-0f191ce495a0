import functools
import inspect
from typing import Any, Awaitable, Callable, Protocol, Type

from dependency_injector import containers, providers
from sqlalchemy.ext.asyncio import AsyncSession

from database.repositories import (
    AdviceRepository,
    CaseEventRepository,
    ClientCaseGoalRepository,
    ClientGoalsRepository,
    ClientHoldingExpectedFeeRepository,
    ProductTypeRepository,
    UserRepository,
)
from database.repositories.aml.aml_monitoring_repository import SQLAlchemyAMLMonitoringRepository
from database.repositories.aml.aml_search_repository import SQLAlchemyAMLSearchRepository
from database.repositories.case.case_repository import SQLAlchemyCaseRepository
from database.repositories.case.review_repository import SQLAlchemyReviewRepository
from database.repositories.case.task_repository import SQLAlchemyTaskRepository
from database.repositories.client_repository import ClientRepository
from database.repositories.clients.client_repository import SQLAlchemyClientRepository
from database.repositories.documents.document_respository import SQLAlchemyDocumentRepository
from database.repositories.holdings.plan_information_repository import SQLAlchemyPlanInfoRepository
from database.session.Session import AsyncSessionFactory


class HasSession(Protocol):
    def __init__(self, session: AsyncSession) -> None: ...


class ManagedSessionRepositoryWrapper:
    """Wrapper that provides session-per-operation semantics for repository classes.

    This class solves the "Idle in transaction" problem by ensuring that each
    repository method call uses its own database session that is properly opened
    and closed. This is particularly important for read-only QueryRepositories
    where sessions should not remain open between operations.

    How it works:
    1. Wraps all public methods of the target repository class.
    2. For each method call, creates a new session using the session factory.
    3. Creates a repository instance with that session.
    4. Executes the method on the repository instance.
    5. Automatically closes the session when the method completes.

    This ensures proper session lifecycle management while maintaining
    the original repository API from the caller's perspective.

    Args:
        repository_class: The repository class to wrap (e.g., ClientRepository).
        session_factory: Factory function that returns an async context manager
                         for database sessions (e.g., database.session).

    Example:
        wrapped_repo = ManagedSessionRepositoryWrapper(ClientRepository, session_factory)
        result = await wrapped_repo.get_by_email("<EMAIL>")  # Session auto-closes

    """

    def __init__(
        self, repository_class: Type[HasSession], session_factory: AsyncSessionFactory
    ) -> None:
        self.repository_class = repository_class
        self.session_factory = session_factory
        self._wrap_methods()

    def _wrap_methods(self) -> None:
        for name in dir(self.repository_class):
            if not name.startswith("_"):
                attr = getattr(self.repository_class, name)
                if inspect.isfunction(attr) or inspect.ismethod(attr):
                    wrapped_method = self._create_wrapped_method(name)
                    setattr(self, name, wrapped_method)

    def _create_wrapped_method(self, method_name: str) -> Callable[..., Awaitable[Any]]:
        # Get the original method for metadata preservation
        original_method = getattr(self.repository_class, method_name)

        async def wrapped_method(*args: Any, **kwargs: Any) -> Any:
            async with self.session_factory() as session:
                repo_instance = self.repository_class(session)
                method = getattr(repo_instance, method_name)
                return await method(*args, **kwargs)

        # Use functools.wraps to properly preserve metadata from the original method
        # This preserves __name__, __doc__, __annotations__, __module__, etc.
        wrapped_method = functools.wraps(original_method)(wrapped_method)

        # Add additional context about the wrapping
        original_doc = wrapped_method.__doc__ or ""
        wrapped_method.__doc__ = f"""ManagedSessionRepositoryWrapper for {self.repository_class.__name__}.{method_name}
        
        This method automatically manages database session lifecycle:
        - Creates a new session for each call
        - Ensures session is properly closed after operation
        - Preserves original method behaviour and signature
        
        Original documentation:
        {original_doc}
        """  # noqa: W293

        return wrapped_method


class QueryRepositories(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()

    client_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=ClientRepository,
        session_factory=database.session,
    )
    client2_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyClientRepository,
        session_factory=database.session,
    )
    plan_info_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyPlanInfoRepository,
        session_factory=database.session,
    )
    advice_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=AdviceRepository,
        session_factory=database.session,
    )
    case_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyCaseRepository,
        session_factory=database.session,
    )
    case_events_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=CaseEventRepository,
        session_factory=database.session,
    )
    client_case_goal_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=ClientCaseGoalRepository,
        session_factory=database.session,
    )
    client_fees_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=ClientHoldingExpectedFeeRepository,
        session_factory=database.session,
    )
    client_goals_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=ClientGoalsRepository,
        session_factory=database.session,
    )
    product_type_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=ProductTypeRepository,
        session_factory=database.session,
    )
    review_calendar_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyReviewRepository,
        session_factory=database.session,
    )
    document_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyDocumentRepository,
        session_factory=database.session,
    )
    task_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyTaskRepository,
        session_factory=database.session,
    )
    aml_search_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyAMLSearchRepository,
        session_factory=database.session,
    )
    aml_monitoring_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=SQLAlchemyAMLMonitoringRepository,
        session_factory=database.session,
    )
    user_repository = providers.Factory(
        ManagedSessionRepositoryWrapper,
        repository_class=UserRepository,
        session_factory=database.session,
    )
