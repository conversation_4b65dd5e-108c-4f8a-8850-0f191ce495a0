from dependency_injector import containers, providers

from api.security.token_validation import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.shared_kernel.app_session import AppSession
from common.config.settings import Settings
from common.containers.chatbot_containers import ChatbotServices
from common.containers.chatbot_operations_containers import ChatbotOperationsServices
from common.containers.client_service_containers import ClientServices
from common.containers.holdings_service_containers import HoldingsServices
from common.containers.repository_containers import QueryRepositories
from common.containers.service_containers import (
    AdminServices,
    AmlServices,
    CaseServices,
    ClientAccessServices,
    DocumentServices,
    FeeSplitServices,
    ForecasterServices,
    MarketingServices,
    RefDataServices,
    ReviewServices,
    StatisticsServices,
    UserServices,
    ValuationFeedServices,
)
from common.logging.logger import LoggingService
from database.repositories.unit_of_work import SQLAlchemyUnitOfWork
from database.session.Session import AsyncSessionFactory, PostgresSessionMaker
from gateway_accessors.aws.bedrock.accessor import BedrockGateway
from gateway_accessors.aws.s3.accessor import S3Gateway
from gateway_accessors.aws.ses.accessor import SesAccessor
from gateway_accessors.aws.sqs.accessor import SqsAccessor
from gateway_accessors.aws.sts.accessor import StsGateway
from gateway_accessors.cognito.accessor import CognitoGateway
from gateway_accessors.data_processing_api.accessor import DataProcessingApiGateway
from gateway_accessors.fidelity.accessor import FidelityGateway
from gateway_accessors.formstack.accessor import FormstackClient
from gateway_accessors.fundment.accessor import FundmentGateway
from gateway_accessors.marketing.active_campaign.accessor import (
    ActiveCampaign,
    MarketingProtocol,
)
from gateway_accessors.postmark.accessor import Postmark
from gateway_accessors.redis.accessor import RedisGateway
from gateway_accessors.transact.accessor import TransactGateway
from gateway_accessors.zignsec.accessor import ZignSecGateway
from timestream.session import (
    TimestreamQueryClient,
    TimestreamQuerySession,
    TimestreamWriteClient,
    TimestreamWriteSession,
)


class AppSessionProvider(providers.Object):
    def _provide(self, *args, **kwargs):
        return AppSession.get_app_session()


class Core(containers.DeclarativeContainer):
    config = providers.Configuration()

    app_session: AppSession = AppSessionProvider()
    logger: LoggingService = providers.Factory(LoggingService, config_file="log_conf_console.yaml")


class Security(containers.DeclarativeContainer):
    config = providers.Configuration()

    token_validator: ValidateToken = providers.Singleton(
        ValidateToken,
        jwks_endpoint=config.jwks_endpoint,
        aws_client_id=config.aws_client_id,
    )


class Database(containers.DeclarativeContainer):
    config = providers.Configuration()
    core = providers.DependenciesContainer()

    session_maker: PostgresSessionMaker = providers.Singleton(
        PostgresSessionMaker, db_url=config.db_url
    )
    unit_of_work: SQLAlchemyUnitOfWork = providers.Factory(
        SQLAlchemyUnitOfWork, session_maker=session_maker, app_session=core.app_session
    )
    session: providers.Resource[AsyncSessionFactory] = providers.Resource(
        session_maker.provided.session
    )


class GatewayContainer(containers.DeclarativeContainer):
    config: Settings = providers.Configuration()
    core = providers.DependenciesContainer()

    cognito = providers.Singleton(
        CognitoGateway,
        aws_region=config.aws_region,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        aws_user_pool_id=config.aws_user_pool_id,
        aws_endpoint_url=config.aws_cognito_endpoint_url,
        aws_send_email_on_user_creation=config.aws_send_email_on_user_creation,
    )
    sts_gateway: StsGateway = providers.Singleton(
        StsGateway,
        is_local=config.local_sts,
        aws_region=config.aws_region,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        localstack_endpoint_url=config.localstack_endpoint_url,
    )
    ses_accessor = providers.Singleton(
        SesAccessor,
        logger=core.logger,
    )  # TODO: remove settings from SesAccessor
    sqs_accessor = providers.Singleton(
        SqsAccessor,
        is_local=config.local_sqs,
        aws_region=config.aws_region,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        lambda_context=config.lambda_context,
        localstack_endpoint_url=config.localstack_endpoint_url,
        queue_name=config.sqs_queue_name,
    )
    s3_gateway: S3Gateway = providers.Singleton(
        S3Gateway,
        is_local=config.local_s3,
        aws_region=config.aws_region,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        lambda_context=config.lambda_context,
        localstack_endpoint_url=config.localstack_endpoint_url,
        aws_bucket_name=config.aws_bucket,
        sts_gateway=sts_gateway,
    )
    bedrock_gateway = providers.Singleton(
        BedrockGateway,
        aws_region=config.aws_bedrock_region,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        is_local=config.local_bedrock,
    )

    formstack_client = providers.Factory(FormstackClient)
    fidelity_gateway = providers.Factory(
        FidelityGateway,
        base_url=config.fidelity_url,
        user_id=config.fidelity_user_id,
        pin=config.fidelity_pin,
    )
    fundment_gateway = providers.Factory(
        FundmentGateway,
        url=config.fundment_url,
        username=config.fundment_username,
        password=config.fundment_password,
    )
    transact_gateway = providers.Factory(
        TransactGateway,
        access_code=config.transact_access_code,
        pin=config.transact_pin,
        provider_key=config.transact_provider_key,
    )
    active_campaign: MarketingProtocol = providers.Factory(
        ActiveCampaign,
        activecampaign_account=config.activecampaign_account,
        activecampaign_api_token=config.activecampaign_api_token,
    )
    postmark = providers.Factory(
        Postmark,
        postmark_base_url=config.postmark_base_url,
        postmark_api_token=config.postmark_api_token,
        postmark_from_address=config.postmark_from_address,
        logger=core.logger,
    )
    email_gateway = providers.Factory(postmark)

    redis_gateway: providers.Singleton[RedisGateway] = providers.Singleton(
        RedisGateway,
        redis_host=config.redis_host,
        redis_port=config.redis_port,
    )

    data_processing_api_gateway: providers.Factory[DataProcessingApiGateway] = providers.Factory(
        DataProcessingApiGateway, base_url=config.data_processing_base_url
    )
    zignsec_accessor = providers.Factory(
        ZignSecGateway,
        zignsec_gateway_env=config.zignsec_gateway_env,
        zignsec_gateway_id=config.zignsec_gateway_id,
        zignsec_env=config.zignsec_env,
        zignsec_id=config.zignsec_id,
        zignsec_validation_method=config.zignsec_validation_method,
        url=config.url,
        user_agent=f"{config.app_name}/v{config.app_version}",
    )


class TimestreamDatabase(containers.DeclarativeContainer):
    config = providers.Configuration()

    query_client: providers.Resource[TimestreamQueryClient] = providers.Resource(
        TimestreamQueryClient,
        lambda_context=config.lambda_context,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        region_name=config.aws_timestream_region,
    )

    query_session: providers.Resource[TimestreamQuerySession] = providers.Resource(
        TimestreamQuerySession,
        client=query_client,
    )

    write_client: providers.Resource[TimestreamWriteClient] = providers.Resource(
        TimestreamWriteClient,
        lambda_context=config.lambda_context,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        region_name=config.aws_timestream_region,
    )

    write_session: providers.Resource[TimestreamWriteSession] = providers.Resource(
        TimestreamWriteSession,
        client=write_client,
    )


class Container(containers.DeclarativeContainer):
    config = providers.Configuration()
    core: Core = providers.Container(Core)

    database: Database = providers.Container(Database, config=config, core=core)
    query_repositories: QueryRepositories = providers.Container(
        QueryRepositories, database=database, core=core
    )
    timestream_database: providers.Container[TimestreamDatabase] = providers.Container(
        TimestreamDatabase, config=config
    )
    security: Security = providers.Container(Security, config=config)
    gateway: GatewayContainer = providers.Container(GatewayContainer, config=config, core=core)

    user_service: UserServices = providers.Container(
        UserServices,
        core=core,
        database=database,
        gateway=gateway,
        query_repositories=query_repositories,
    )
    case_service: CaseServices = providers.Container(
        CaseServices, core=core, database=database, query_repositories=query_repositories
    )
    ref_data_service: RefDataServices = providers.Container(
        RefDataServices, core=core, database=database, gateway=gateway
    )
    holdings_service: HoldingsServices = providers.Container(
        HoldingsServices,
        core=core,
        database=database,
        timestream_database=timestream_database,
        gateway=gateway,
        query_repositories=query_repositories,
    )
    fee_split_service: FeeSplitServices = providers.Container(
        FeeSplitServices, core=core, database=database
    )
    forecaster_services: ForecasterServices = providers.Container(
        ForecasterServices, core=core, database=database
    )
    document_services: DocumentServices = providers.Container(
        DocumentServices,
        database=database,
        core=core,
        config=config,
        gateway=gateway,
        user_service=user_service,
    )
    client_access_services: providers.Container[ClientAccessServices] = providers.Container(
        ClientAccessServices,
        core=core,
        database=database,
        gateway=gateway,
        user_service=user_service,
    )
    admin_services: AdminServices = providers.Container(
        AdminServices,
        database=database,
        core=core,
        config=config,
        gateway=gateway,
    )
    chatbot_services: providers.Container[ChatbotServices] = providers.Container(
        ChatbotServices,
        database=database,
        core=core,
        gateway=gateway,
    )
    client_services: ClientServices = providers.Container(
        ClientServices,
        core=core,
        database=database,
        gateway=gateway,
        user_service=user_service,
        timestream_database=timestream_database,
        query_repositories=query_repositories,
        client_access_services=client_access_services,
    )
    chatbot_operations_services: providers.Container[ChatbotOperationsServices] = (
        providers.Container(
            ChatbotOperationsServices,
            database=database,
            core=core,
            gateway=gateway,
            client_services=client_services,
        )
    )
    valuation_feed_services: providers.Container[ValuationFeedServices] = providers.Container(
        ValuationFeedServices,
        core=core,
        gateway=gateway,
        database=database,
        timestream_database=timestream_database,
    )
    marketing_services: MarketingServices = providers.Container(
        MarketingServices,
        core=core,
        database=database,
        marketing_client=gateway.active_campaign,
    )
    review_services: providers.Container[ReviewServices] = providers.Container(
        ReviewServices, core=core, database=database
    )
    statistics_services: providers.Container[StatisticsServices] = providers.Container(
        StatisticsServices,
        core=core,
        config=config,
        database=database,
    )
    aml_services: providers.Container[AmlServices] = providers.Container(
        AmlServices,
        core=core,
        config=config,
        database=database,
        query_repositories=query_repositories,
        gateway=gateway,
    )
