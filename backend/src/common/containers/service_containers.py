from dependency_injector import containers, providers

from app.aml.services.aml_service import AMLCommandService, AMLQueryService
from app.cases.services.case_manager_service import CaseCommandService
from app.cases.services.case_query_service import CaseQueryService
from app.clients.services.client_access import ClientAccessService
from app.clients.services.client_data_manager import ClientDataCleanupService
from app.documents.document_storage_services import DocumentStorageService
from app.documents.generation_services import RegisterDocumentTemplateService
from app.fees.commands.fee_split_templates import (
    AddFeeSplitTemplate,
    UpdateFeeSplitTemplate,
)
from app.fees.views.expected_fees_forecast import ExpectedFeesForecastViewService
from app.forecaster.queries.atr_profile_queries import GetCaseGoalRiskProfile
from app.forecaster.queries.forecaster_queries import GetCaseGoalForecasterPlan
from app.forecaster.services import (
    CalculateRiskProfileService,
    GenerateForecasterPlanService,
)
from app.holdings.services.portfolio_valuation_service import GetClientPortfolioValuationsService
from app.marketing.services import (
    CreateMarketingContact,
    GetContacts,
    GetMarketingDetails,
    GetSubscriptionLists,
    GetTemplate,
    SubscribeMarketingContact,
    UnsubscribeMarketingContact,
    UpdateMarketingContact,
)
from app.ref_data.services.ref_data_services import (
    HoldingStatusRefDataService,
    RefDataService,
)
from app.review_slots.queries.get_review_slot_assigned_to_case import (
    GetReviewSlotAssignedToCase,
)
from app.review_slots.services.review_services import (
    AvailableReviewSlots,
    ClientReviewSlots,
    ReviewGroupServices,
)
from app.users.services.cognito_custom_email_sender import CognitoCustomEmailSenderService
from app.users.services.user_service import (
    FindUserService,
    get_user_entity_from_session,
    get_user_id_from_session,
    sync_cognito_user,
)
from gateway_accessors.marketing.marketing_protocol import MarketingProtocol
from lambda_handlers.services.document_generation.document_generation_service import (
    DocumentGenerationService,
)
from lambda_handlers.services.marketing.active_campaign_service import (
    ImportActiveCampaignContactsService,
)
from lambda_handlers.services.valuation_feed.fetched_valuation_service import (
    AddFetchedHoldingValuationsService,
)
from lambda_handlers.services.valuation_feed.forward_fill_valuation_service import (
    ForwardFillFetchedValuationService,
    ScheduleForwardFillFetchedValuationService,
)
from lambda_handlers.services.valuation_feed.manual_valuation_service import (
    AddManualHoldingValuationService,
    AddUploadedHoldingValuationsService,
)
from lambda_handlers.services.valuation_feed.provider_handlers.base_pricing_handler import (
    SqsDestination,
)
from lambda_handlers.services.valuation_feed.provider_handlers.fidelity_handler import (
    FideltiyValuationFeedHanlder,
)
from lambda_handlers.services.valuation_feed.provider_handlers.fundment_handler import (
    FundmentValuationFeedHandler,
)
from lambda_handlers.services.valuation_feed.provider_handlers.transact_handler import (
    TransactValuationFeedHandler,
)


class UserServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()
    query_repositories = providers.DependenciesContainer()

    user_id = providers.Callable(get_user_id_from_session, app_session=core.app_session)
    user_entity = providers.Callable(get_user_entity_from_session, app_session=core.app_session)

    find_user_service = providers.Factory(
        FindUserService,
        logger=core.logger,
        user_repository=query_repositories.user_repository,
    )
    cognito_custom_email_sender = providers.Factory(
        CognitoCustomEmailSenderService, logger=core.logger, mailer=gateway.email_gateway
    )

    # service methods
    sync_cognito = providers.Callable(sync_cognito_user)


class RefDataServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()

    service = providers.Factory(
        RefDataService,
        session=database.session,
        redis=gateway.redis_gateway,
        logger=core.logger,
    )

    holding_status = providers.Factory(
        HoldingStatusRefDataService,
        uow=database.unit_of_work,
        logger=core.logger,
    )


class FeeSplitServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()

    add_fee_split = providers.Factory(
        AddFeeSplitTemplate, uow=database.unit_of_work, logger=core.logger
    )

    update_fee_split = providers.Factory(
        UpdateFeeSplitTemplate, uow=database.unit_of_work, logger=core.logger
    )


class ForecasterServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()

    get_risk_profile = providers.Factory(
        GetCaseGoalRiskProfile, session=database.session, logger=core.logger
    )
    calculate_risk_profile = providers.Factory(
        CalculateRiskProfileService, uow=database.unit_of_work, logger=core.logger
    )

    get_forecaster_plan = providers.Factory(
        GetCaseGoalForecasterPlan, session=database.session, logger=core.logger
    )
    generate_forecaster_plan = providers.Factory(
        GenerateForecasterPlanService, uow=database.unit_of_work, logger=core.logger
    )


class ReviewSlotServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()

    review_slot_assigned_to_case = providers.Factory(
        GetReviewSlotAssignedToCase, session=database.session, logger=core.logger
    )


class CaseServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    query_repositories = providers.DependenciesContainer()

    case_commands = providers.Factory(
        CaseCommandService, uow=database.unit_of_work, logger=core.logger
    )

    case_queries = providers.Factory(
        CaseQueryService,
        logger=core.logger,
        advice_repo=query_repositories.advice_repository,
        case_repo=query_repositories.case_repository,
        case_events_repo=query_repositories.case_events_repository,
        client_case_goal_repo=query_repositories.client_case_goal_repository,
        client_fees_repo=query_repositories.client_fees_repository,
        client_goals_repo=query_repositories.client_goals_repository,
        product_type_repo=query_repositories.product_type_repository,
        review_calendar_repo=query_repositories.review_calendar_repository,
        documents_repo=query_repositories.document_repository,
        task_repo=query_repositories.task_repository,
    )


class DocumentServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    config = providers.Configuration()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()
    user_service = providers.DependenciesContainer()

    register_document_template_service = providers.Factory(
        RegisterDocumentTemplateService,
        uow=database.unit_of_work,
        logger=core.logger,
    )

    document_generation_service = providers.Factory(
        DocumentGenerationService,
        uow=database.unit_of_work,
        logger=core.logger,
        formstack_client=gateway.formstack_client,
        s3_gateway=gateway.s3_gateway,
        email_gateway=gateway.email_gateway,
    )

    document_storage_service = providers.Factory(
        DocumentStorageService,
        uow=database.unit_of_work,
        logger=core.logger,
        s3_gateway=gateway.s3_gateway,
        user_id=user_service.user_id,
    )


class AdminServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    config = providers.Configuration()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()

    data_cleanup_service = providers.Factory(
        ClientDataCleanupService,
        uow=database.unit_of_work,
        logger=core.logger,
        email_gateway=gateway.email_gateway,
        adviser_group_email_address=config.adviser_group_email_address,
        url=config.url,
    )


class ValuationFeedServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    timestream_database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()

    destination: providers.Factory[SqsDestination] = providers.Factory(
        SqsDestination, sqs_accessor=gateway.sqs_accessor
    )

    fundment_pricing_service: providers.Factory[FundmentValuationFeedHandler] = providers.Factory(
        FundmentValuationFeedHandler,
        destination=destination,  # type: ignore
        logger=core.logger,
        fundment_gateway=gateway.fundment_gateway,
        session=database.session,
        ts_query_session=timestream_database.query_session,
    )

    transact_pricing_service: providers.Factory[TransactValuationFeedHandler] = providers.Factory(
        TransactValuationFeedHandler,
        destination=destination,  # type: ignore
        logger=core.logger,
        transact_gateway=gateway.transact_gateway,
        session=database.session,
        ts_query_session=timestream_database.query_session,
    )

    fidelity_pricing_service: providers.Factory[FideltiyValuationFeedHanlder] = providers.Factory(
        FideltiyValuationFeedHanlder,
        destination=destination,
        logger=core.logger,
        fidelity_gateway=gateway.fidelity_gateway,
        session=database.session,
        ts_query_session=timestream_database.query_session,
    )

    add_fetched_holding_valuations_service: providers.Factory[
        AddFetchedHoldingValuationsService
    ] = providers.Factory(
        AddFetchedHoldingValuationsService,
        session=timestream_database.write_session,
        logger=core.logger,
    )

    add_manual_holding_valuation_service: providers.Factory[AddManualHoldingValuationService] = (
        providers.Factory(
            AddManualHoldingValuationService,
            ts_query_session=timestream_database.query_session,
            ts_write_session=timestream_database.write_session,
            logger=core.logger,
        )
    )

    add_uploaded_holding_valuations_service: providers.Factory[
        AddUploadedHoldingValuationsService
    ] = providers.Factory(
        AddUploadedHoldingValuationsService,
        ts_query_session=timestream_database.query_session,
        ts_write_session=timestream_database.write_session,
        s3_gateway=gateway.s3_gateway,
        logger=core.logger,
    )

    get_client_portfolio_valuations = providers.Factory(
        GetClientPortfolioValuationsService,
        db_session=database.session,
        ts_query_session=timestream_database.query_session,
        logger=core.logger,
    )

    forward_fill_valuation_service: providers.Factory[ForwardFillFetchedValuationService] = (
        providers.Factory(
            ForwardFillFetchedValuationService,
            ts_query_session=timestream_database.query_session,
            ts_write_session=timestream_database.write_session,
            logger=core.logger,
        )
    )

    schedule_forward_fill_valuation_service: providers.Factory[
        ScheduleForwardFillFetchedValuationService
    ] = providers.Factory(
        ScheduleForwardFillFetchedValuationService,
        sqs_accessor=gateway.sqs_accessor,
        logger=core.logger,
    )


class MarketingServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    marketing_client: providers.Factory[MarketingProtocol] = providers.DelegatedFactory()

    import_contacts = providers.Factory(
        ImportActiveCampaignContactsService,
        uow=database.unit_of_work,
        logger=core.logger,
        active_campaign_client=marketing_client.provided,
    )

    get_contacts = providers.Factory(
        GetContacts,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )

    get_marketing_details = providers.Factory(
        GetMarketingDetails,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )

    create_marketing_contact = providers.Factory(
        CreateMarketingContact,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )

    update_marketing_contact = providers.Factory(
        UpdateMarketingContact,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )

    subscribe_contact = providers.Factory(
        SubscribeMarketingContact,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )

    unsubscribe_contact = providers.Factory(
        UnsubscribeMarketingContact,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )

    get_subscription_lists = providers.Factory(
        GetSubscriptionLists,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )

    get_template = providers.Factory(
        GetTemplate,
        uow=database.unit_of_work,
        logger=core.logger,
        marketing_client=marketing_client.provided,
    )


class ReviewServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()

    review_group_services = providers.Factory(
        ReviewGroupServices,
        logger=core.logger,
        uow=database.unit_of_work,
    )

    review_slots = providers.Factory(
        ClientReviewSlots,
        logger=core.logger,
        uow=database.unit_of_work,
    )

    available_review_slot = providers.Factory(
        AvailableReviewSlots,
        logger=core.logger,
        uow=database.unit_of_work,
    )


class StatisticsServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    config = providers.Configuration()
    database = providers.DependenciesContainer()

    expected_fees_forecast_view_service = providers.Factory(
        ExpectedFeesForecastViewService,
        uow=database.unit_of_work,
        logger=core.logger,
    )


class AmlServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    config = providers.Configuration()
    database = providers.DependenciesContainer()
    query_repositories = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()

    aml_query_service = providers.Factory(
        AMLQueryService,
        logger=core.logger,
        aml_search_repo=query_repositories.aml_search_repository,
        aml_monitoring_repo=query_repositories.aml_monitoring_repository,
        zignsec_accessor=gateway.zignsec_accessor,
    )

    aml_command_service = providers.Factory(
        AMLCommandService,
        logger=core.logger,
        uow=database.unit_of_work,
        zignsec_accessor=gateway.zignsec_accessor,
    )


class ClientAccessServices(containers.DeclarativeContainer):
    core = providers.DependenciesContainer()
    database = providers.DependenciesContainer()
    gateway = providers.DependenciesContainer()
    user_service = providers.DependenciesContainer()

    set_client_access = providers.Factory(
        ClientAccessService,
        uow=database.unit_of_work,
        cognito=gateway.cognito,
        logger=core.logger,
        mailer=gateway.email_gateway,
        sync_cognito_command=user_service.provided.sync_cognito,
    )
