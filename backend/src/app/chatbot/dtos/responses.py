from dataclasses import dataclass
from datetime import datetime
from typing import Any, Literal, Union


@dataclass
class BaseOutboundDTO:
    id: int | None = None
    timestamp: datetime | None = None


@dataclass
class NewIdDTO(BaseOutboundDTO):
    data_layout: Literal["new_id_layout"] = "new_id_layout"


@dataclass
class UserTextDTO(BaseOutboundDTO):
    data_layout: Literal["user_text_layout"] = "user_text_layout"
    message: str | None = None


@dataclass
class AssistantTextDTO(BaseOutboundDTO):
    data_layout: Literal["assistant_text_layout"] = "assistant_text_layout"
    message: str | None = None


@dataclass(kw_only=True)
class OutboundDTO(BaseOutboundDTO):
    data_layout: str | None = None
    available_fields: list[str] | None = None
    completed: bool | None = None
    data: dict[str, Any] | list[dict[str, Any]]


@dataclass
class SetIncompleteDTO(BaseOutboundDTO):
    data_layout: Literal["set_incomplete_layout"] = "set_incomplete_layout"


OutboundMessage = Union[
    NewIdDTO,
    AssistantTextDTO,
    OutboundDTO,
    SetIncompleteDTO,
]


UserFacingMessage = Union[
    UserTextDTO,
    AssistantTextDTO,
    OutboundDTO,
]
