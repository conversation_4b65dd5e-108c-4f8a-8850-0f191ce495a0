from __future__ import annotations

from dataclasses import dataclass
from datetime import date, datetime
from typing import Literal, Protocol, Union, runtime_checkable

from pydantic import BaseModel

from api.enums.enums import Relationship
from app.chatbot.constants import (
    OperationGroup,
)


class SubSchema:
    class BaseSubSchema(BaseModel):
        id: int | None = None

    class AddressSchema(BaseSubSchema):
        address_line_one: str | None = None
        address_line_two: str | None = None
        address_line_three: str | None = None
        address_line_four: str | None = None
        city: str | None = None
        post_code: str | None = None
        moved_in_date: date | None = None
        moved_out_date: date | None = None
        is_primary: bool | None = None
        country_id: int | None = None

    class RelationSchema(BaseSubSchema):
        first_name: str | None = None
        last_name: str | None = None
        date_of_birth: date | None = None
        relationship_type: Relationship | None = None


@dataclass(frozen=False, kw_only=True, slots=True)
class UserTextDTO:
    message: str


@dataclass(frozen=False, kw_only=False, slots=False)
class InboundDataDTO:
    id: int
    timestamp: datetime
    completed: bool
    available_fields: list[str] | None


@runtime_checkable
class HasCannedResponseProtocol(Protocol):
    def response_message(self) -> str | None: ...


@dataclass(frozen=False, kw_only=False, slots=False)
class User_PersonalDetailsDTO(InboundDataDTO):
    data_layout: Literal[f"{OperationGroup.User_PersonalDetails.value}_layout"]
    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    phone_number: str | None = None
    mobile_number: str | None = None
    date_of_birth: date | None = None

    def __init__(
        self,
        *,
        timestamp: datetime,
        id: int,
        completed: bool,
        available_fields: list[str] | None,
        data_layout: Literal[f"{OperationGroup.User_PersonalDetails.value}_layout"],
        first_name: str | None = None,
        last_name: str | None = None,
        email: str | None = None,
        phone_number: str | None = None,
        mobile_number: str | None = None,
        date_of_birth: date | None = None,
    ):
        super().__init__(
            timestamp=timestamp,
            id=id,
            completed=completed,
            available_fields=available_fields,
        )
        self.data_layout = data_layout
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone_number = phone_number
        self.mobile_number = mobile_number
        self.date_of_birth = date_of_birth


@dataclass(frozen=False, kw_only=False, slots=False)
class User_AddressesDTO(InboundDataDTO):
    data_layout: Literal[f"{OperationGroup.User_Addresses.value}_layout"]
    addresses: list[SubSchema.AddressSchema]

    def __init__(
        self,
        *,
        timestamp: datetime,
        id: int,
        completed: bool,
        available_fields: list[str] | None,
        data_layout: Literal[f"{OperationGroup.User_Addresses.value}_layout"],
        addresses: list[SubSchema.AddressSchema],
    ):
        super().__init__(
            timestamp=timestamp,
            id=id,
            completed=completed,
            available_fields=available_fields,
        )
        self.data_layout = data_layout
        self.addresses = addresses


@dataclass(frozen=False, kw_only=False, slots=False)
class User_RelationsDTO(InboundDataDTO):
    data_layout: Literal[f"{OperationGroup.User_Relations.value}_layout"]
    relations: list[SubSchema.RelationSchema]

    def __init__(
        self,
        *,
        timestamp: datetime,
        id: int,
        completed: bool,
        available_fields: list[str] | None,
        data_layout: Literal[f"{OperationGroup.User_Relations.value}_layout"],
        relations: list[SubSchema.RelationSchema],
    ):
        super().__init__(
            timestamp=timestamp,
            id=id,
            completed=completed,
            available_fields=available_fields,
        )
        self.data_layout = data_layout
        self.relations = relations


@dataclass(frozen=False, kw_only=False, slots=False)
class User_WithdrawConsentDTO(InboundDataDTO, HasCannedResponseProtocol):
    data_layout: Literal[f"{OperationGroup.User_Consent_Withdrawal.value}_layout"]
    withdrawn: bool

    def __init__(
        self,
        *,
        timestamp: datetime,
        id: int,
        completed: bool,
        available_fields: list[str] | None = None,
        data_layout: Literal[f"{OperationGroup.User_Consent_Withdrawal.value}_layout"],
        withdrawn: bool,
        **kwargs,
    ):
        super().__init__(
            timestamp=timestamp,
            id=id,
            completed=completed,
            available_fields=available_fields,
        )
        self.data_layout = data_layout
        self.withdrawn = withdrawn

    def response_message(self):
        if self.withdrawn:
            return "Your consent has been withdrawn."
        else:
            return (
                "You've decided not to withdraw your consent at this time. How else can I help you?"
            )


MessageDTO = Union[
    UserTextDTO,
    User_PersonalDetailsDTO,
    User_AddressesDTO,
    User_RelationsDTO,
    User_WithdrawConsentDTO,
]
