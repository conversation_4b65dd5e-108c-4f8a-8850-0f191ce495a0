from typing import Any

from app.base.base_service import BaseService
from app.chatbot.agents.conversation_labeller.agent import create_conversation_labeller_agent
from app.chatbot.dtos.responses import (
    UserFacingMessage,
)
from app.chatbot.interface.conversation_history_interface import (
    ConversationHistoryStore,
)
from common.logging.logger import LoggingService
from gateway_accessors.aws.bedrock.accessor import BedrockGateway


class NewConversation(BaseService):
    async def __call__(self, *args, **kwargs) -> Any:
        raise NotImplementedError

    def __init__(
        self,
        *,
        logger: LoggingService,
        llm_gateway: BedrockGateway,
        conversation_history_store: ConversationHistoryStore,
    ) -> None:
        super().__init__(logger=logger)
        self.llm_gateway = llm_gateway
        self.store = conversation_history_store

    async def new_conversation(self, user_id: int) -> list[UserFacingMessage]:
        if conversation := await self.store.get_latest_conversation(user_id):
            agent = await create_conversation_labeller_agent(self.llm_gateway)

            result = await agent.run(
                user_prompt="Create a label for the conversation in 10 words or fewer. Do not introduce or explain what you are doing.",
                message_history=await self.store.format_conversation_agent_facing(
                    user_id, conversation
                ),
            )

            conversation.label = result.output

            await self.store.set_conversation_to_storage(user_id, conversation)

        conversation = await self.store.create_new_conversation(user_id)

        return await self.store.format_conversation_user_facing(user_id, conversation)
