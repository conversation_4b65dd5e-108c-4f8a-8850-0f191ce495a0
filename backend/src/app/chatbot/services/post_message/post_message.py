import asyncio
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Async<PERSON><PERSON><PERSON>, Set

from pydantic_ai import Agent
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    PartDeltaEvent,
    PartStartEvent,
    TextPart,
    TextPartDelta,
    ToolReturnPart,
    UserPromptPart,
)
from pydantic_graph import GraphRunContext

from app.base.base_service import BaseService
from app.chatbot.agents.user_data_input.agent import create_data_input_agent
from app.chatbot.agents.user_text_input.agent import AgentDependencies, create_text_input_agent
from app.chatbot.constants import OperationResultText
from app.chatbot.dtos.conversation import generate_message_id
from app.chatbot.dtos.requests import (
    HasCannedResponseProtocol,
    InboundDataDTO,
    MessageDTO,
    UserTextDTO,
)
from app.chatbot.dtos.responses import (
    AssistantTextDTO,
    NewIdDTO,
    OutboundDTO,
    OutboundMessage,
    SetIncompleteDTO,
)
from app.chatbot.exceptions import (
    RunResultIsNoneException,
)
from app.chatbot.interface.conversation_history_interface import (
    ConversationHistoryStore,
    ConversationStateManager,
)
from app.chatbot.services.exceptions import AgentStreamError, MessageProcessingError, ToolQueueError
from app.chatbot.services.internal_client import InternalClient
from app.chatbot.services.post_message.async_task_manager import AsyncTaskManager
from app.chatbot.services.post_message.operations import OperationDispatcher
from common.logging.logger import LoggingService
from gateway_accessors.aws.bedrock.accessor import BedrockGateway


@dataclass(frozen=True)
class ProcessingConfig:
    """Configuration constants for message processing."""

    MAX_QUEUE_SIZE: int = 100
    MAX_RESPONSE_TIMESTAMPS: int = 50
    MAX_OUTBOUND_SCHEMAS: int = 50


class PostMessage(BaseService):
    async def __call__(self, *args, **kwargs) -> Any:
        raise NotImplementedError

    def __init__(
        self,
        *,
        logger: LoggingService,
        internal_client: InternalClient,
        llm_gateway: BedrockGateway,
        conversation_history_store: ConversationHistoryStore,
    ) -> None:
        super().__init__(logger=logger)
        self.internal_client = internal_client
        self.llm_gateway = llm_gateway
        self.store = conversation_history_store
        self.config = ProcessingConfig()

    async def _handle_agent_node_events(
        self,
        node,
        run_context_ctx: GraphRunContext[Any, Any],
        timestamp: datetime,
    ) -> AsyncIterator[AssistantTextDTO]:
        message_id = generate_message_id(timestamp)
        message = ""

        try:
            async with node.stream(run_context_ctx) as request_stream:
                async for event in request_stream:
                    if isinstance(event, PartStartEvent) and isinstance(event.part, TextPart):
                        message = event.part.content
                        yield AssistantTextDTO(
                            id=message_id,
                            timestamp=timestamp,
                            message=message,
                        )
                    elif isinstance(event, PartDeltaEvent) and isinstance(
                        event.delta, TextPartDelta
                    ):
                        message += event.delta.content_delta
                        yield AssistantTextDTO(
                            id=message_id,
                            timestamp=timestamp,
                            message=message,
                        )
        except Exception as e:
            self.logger.error(f"Error handling agent node events: {e}")
            raise AgentStreamError(f"Failed to process agent node events: {e}") from e

    async def _process_data_input_message(
        self,
        user_id: int,
        dto: InboundDataDTO,
        conversation_manager: ConversationStateManager,
        jwt_token: str,
    ) -> AsyncIterator[OutboundMessage]:
        """Process non-text messages through data input agent."""
        try:
            results = await OperationDispatcher(
                self.logger,
                self.store,
                self.internal_client,
                conversation_manager.user_id,
                jwt_token,
            ).dispatch(dto)

            if any(r != OperationResultText.SUCCESS for r in results.results):
                yield SetIncompleteDTO(id=dto.id)

            await conversation_manager.get_or_create_conversation()

            initial_timestamp = datetime.now()
            initial_id = generate_message_id(initial_timestamp)

            if isinstance(dto, HasCannedResponseProtocol):
                canned_message = dto.response_message()
                yield AssistantTextDTO(
                    id=initial_id,
                    timestamp=initial_timestamp,
                    message=canned_message,
                )

                # Save canned response to conversation history
                canned_response = ModelResponse(
                    parts=[TextPart(content=canned_message)], timestamp=initial_timestamp
                )
                await conversation_manager.save_conversation([canned_response])

            else:
                response_agent = await create_data_input_agent(user_id, self.llm_gateway, results)
                message_history = await conversation_manager.format_for_agent(user_id)

                async with response_agent.run_stream(
                    user_prompt="Continue.",
                    message_history=message_history,
                ) as result:
                    async for message in result.stream_text():
                        yield AssistantTextDTO(
                            id=initial_id,
                            timestamp=initial_timestamp,
                            message=message,
                        )

                # do not add the "Continue." message to the conversation history or it will appear on the front end
                new_messages = result.new_messages()
                new_messages.pop(0)

                await conversation_manager.save_conversation(new_messages)

        except Exception as e:
            self.logger.error(f"Error processing data input message: {e}")
            raise MessageProcessingError(f"Data input processing failed: {e}") from e

    async def _process_tool_queue_item(
        self, queue_task: asyncio.Task, outbound_schemas: list[OutboundDTO]
    ) -> OutboundDTO:
        """Process a single item from the tool queue."""
        try:
            tool_data: OutboundDTO = queue_task.result()

            current_time = datetime.now()
            message_id = generate_message_id(current_time)

            tool_data.id = message_id
            tool_data.timestamp = current_time

            if len(outbound_schemas) < self.config.MAX_OUTBOUND_SCHEMAS:
                outbound_schemas.append(tool_data)
            else:
                # TODO outbound_schemas represent completed data operations in the user's conversation history. Dropping them may be an issue for audit (as unlikely as it is). Is there a better solution?
                self.logger.warn("Outbound schemas limit reached, dropping oldest")
                outbound_schemas.pop(0)
                outbound_schemas.append(tool_data)

            return tool_data
        except Exception as e:
            self.logger.error(f"Error processing tool queue item: {e}")
            raise ToolQueueError(f"Tool queue processing failed: {e}") from e

    def _should_terminate_processing(
        self, agent_stream_exhausted: bool, tool_output_queue: asyncio.Queue
    ) -> bool:
        """Determine if message processing should terminate."""
        return agent_stream_exhausted and tool_output_queue.empty()

    def _prepare_conversation_messages(
        self,
        new_messages: list,
        response_timestamps: list[datetime],
        outbound_schemas: list[OutboundDTO],
        initial_timestamp: datetime,
    ) -> list:
        """Prepare messages for conversation storage."""
        if not new_messages:
            return []

        # Set timestamp for user message
        if isinstance(new_messages[0].parts[0], UserPromptPart):
            new_messages[0].parts[0].timestamp = initial_timestamp

        new_conversation_messages: list[ModelMessage | OutboundDTO] = []

        for msg in new_messages:
            if isinstance(msg, ModelResponse) and response_timestamps:
                msg.timestamp = response_timestamps.pop(0)

            new_conversation_messages.append(msg)

            # Handle tool returns
            if isinstance(msg, ModelRequest):
                for part in msg.parts:
                    if (
                        isinstance(part, ToolReturnPart)
                        and part.content == OperationResultText.SUCCESS.value
                        and outbound_schemas
                    ):
                        new_conversation_messages.append(outbound_schemas.pop(0))

        return new_conversation_messages

    async def _process_text_input_message(
        self,
        user_id: int,
        dto: UserTextDTO,
        conversation_manager: ConversationStateManager,
        task_manager: AsyncTaskManager,
        jwt_token: str,
    ) -> AsyncIterator[OutboundMessage]:
        """Process text input messages through text input agent."""
        initial_timestamp = datetime.now()
        initial_id = generate_message_id(initial_timestamp)

        yield NewIdDTO(id=initial_id, timestamp=initial_timestamp)

        agent = await create_text_input_agent(self.llm_gateway)
        await conversation_manager.get_or_create_conversation()

        tool_output_queue = asyncio.Queue[OutboundDTO](maxsize=self.config.MAX_QUEUE_SIZE)

        async with agent.iter(
            user_prompt=dto.message,
            deps=AgentDependencies(
                self.logger,
                self.llm_gateway,
                conversation_manager.user_id,
                jwt_token,
                self.internal_client,
                tool_output_queue,
            ),
            message_history=await conversation_manager.format_for_agent(user_id),
        ) as run_context:
            agent_iter = run_context.__aiter__()
            response_timestamps: list[datetime] = []
            outbound_schemas: list[OutboundDTO] = []

            agent_task = task_manager.create_task(agent_iter.__anext__(), "agent_iterator_task")
            queue_task = task_manager.create_task(tool_output_queue.get(), "queue_reader_task")

            pending_tasks = {agent_task, queue_task}
            agent_stream_exhausted = False

            while pending_tasks:
                done, pending_tasks = await asyncio.wait(
                    pending_tasks, return_when=asyncio.FIRST_COMPLETED
                )

                # Process completed tasks, prioritise data blocks
                for completed_task in done:
                    if completed_task.get_name() == "queue_reader_task":
                        tool_data = await self._handle_queue_task_completion(
                            completed_task,
                            outbound_schemas,
                            task_manager,
                            tool_output_queue,
                            pending_tasks,
                        )
                        if tool_data:
                            yield tool_data

                for completed_task in done:
                    if completed_task.get_name() == "agent_iterator_task":
                        async for item in self._handle_agent_task_completion(
                            completed_task,
                            agent_iter,
                            task_manager,
                            pending_tasks,
                            run_context,
                            response_timestamps,
                        ):
                            if isinstance(item, bool):
                                agent_stream_exhausted = item
                            else:
                                yield item

                # Check termination condition
                if self._should_terminate_processing(agent_stream_exhausted, tool_output_queue):
                    break

            # Finalize and save conversation
            if run_context.result is None:
                raise RunResultIsNoneException(user_id)

            new_messages = self._prepare_conversation_messages(
                run_context.result.new_messages(),
                response_timestamps,
                outbound_schemas,
                initial_timestamp,
            )

            await conversation_manager.save_conversation(new_messages)

    async def _handle_queue_task_completion(
        self,
        queue_task: asyncio.Task,
        outbound_schemas: list[OutboundDTO],
        task_manager: AsyncTaskManager,
        tool_output_queue: asyncio.Queue,
        pending_tasks: Set[asyncio.Task],
    ) -> OutboundDTO | None:
        """Handle completion of queue task. Returns processed tool data if successful."""
        try:
            tool_data = await self._process_tool_queue_item(queue_task, outbound_schemas)

            # Schedule next queue task
            new_queue_task = task_manager.create_task(tool_output_queue.get(), "queue_reader_task")
            pending_tasks.add(new_queue_task)

            return tool_data

        except ToolQueueError as e:
            self.logger.error(f"Tool queue error: {e}")
            # Reschedule queue task on error
            new_queue_task = task_manager.create_task(tool_output_queue.get(), "queue_reader_task")
            pending_tasks.add(new_queue_task)
            return None

    async def _handle_agent_task_completion(
        self,
        agent_task: asyncio.Task,
        agent_iter,
        task_manager: AsyncTaskManager,
        pending_tasks: Set[asyncio.Task],
        run_context,
        response_timestamps: list[datetime],
    ):
        """Handle completion of agent task. Yields responses and completion status."""
        try:
            agent_node = agent_task.result()

            if Agent.is_model_request_node(agent_node):
                response_timestamp = datetime.now()

                if len(response_timestamps) < self.config.MAX_RESPONSE_TIMESTAMPS:
                    response_timestamps.append(response_timestamp)
                else:
                    self.logger.warn("Response timestamps limit reached, dropping oldest")
                    # TODO this might not yield the tool call as a priority. Must make sure the agent closing message isn't yielded before the tool blob is.
                    response_timestamps.pop(0)
                    response_timestamps.append(response_timestamp)

                async for item in self._handle_agent_node_events(
                    agent_node, run_context.ctx, response_timestamp
                ):
                    yield item

            # Schedule next agent task
            new_agent_task = task_manager.create_task(agent_iter.__anext__(), "agent_iterator_task")
            pending_tasks.add(new_agent_task)

            yield False  # Stream not exhausted

        except StopAsyncIteration:
            yield True  # Stream exhausted
        except Exception as e:
            self.logger.error(f"Error processing agent stream: {e}")
            raise AgentStreamError(f"Agent stream processing failed: {e}") from e

    async def process_message(
        self, dto: MessageDTO, user_id: int, jwt_token: str
    ) -> AsyncIterator[OutboundMessage]:
        conversation_manager = ConversationStateManager(self.store, user_id, self.logger)
        task_manager = AsyncTaskManager(self.logger)

        try:
            # Handle data input messages (non-text)
            if not isinstance(dto, UserTextDTO):
                async for message in self._process_data_input_message(
                    user_id, dto, conversation_manager, jwt_token
                ):
                    yield message
                return

            # Handle text input messages
            async for message in self._process_text_input_message(
                user_id, dto, conversation_manager, task_manager, jwt_token
            ):
                yield message

        except Exception as e:
            self.logger.error(f"Fatal error in process_message: {e}")
            raise MessageProcessingError(f"Message processing failed: {e}") from e
        finally:
            await task_manager.cancel_all_tasks()
