from typing import Any

from app.base.base_service import BaseService
from app.chatbot.dtos.responses import UserFacingMessage
from app.chatbot.interface.conversation_history_interface import (
    ConversationHistoryStore,
)
from common.logging.logger import LoggingService


class GetMessages(BaseService):
    async def __call__(self, *args, **kwargs) -> Any:
        raise NotImplementedError

    def __init__(
        self,
        *,
        logger: LoggingService,
        conversation_history_store: ConversationHistoryStore,
    ) -> None:
        super().__init__(logger=logger)
        self.store = conversation_history_store

    async def get_messages(self, user_id: int) -> list[UserFacingMessage]:
        conversation = await self.store.get_latest_conversation(user_id)
        if conversation is None:
            conversation = await self.store.create_new_conversation(user_id)

        return await self.store.format_conversation_user_facing(user_id, conversation)
