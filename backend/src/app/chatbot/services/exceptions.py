from app.chatbot.exceptions import ChatbotException


class MessageProcessingError(ChatbotException):
    def __init__(self, error_message: str):
        super().__init__(error_message)


class AgentStreamError(MessageProcessingError):
    """Exception raised when agent stream processing fails."""

    def __init__(self, error_message: str):
        super().__init__(error_message)


class ToolQueueError(MessageProcessingError):
    """Exception raised when tool queue processing fails."""

    def __init__(self, error_message: str):
        super().__init__(error_message)


class ConversationStateError(MessageProcessingError):
    """Exception raised when conversation state management fails."""

    def __init__(self, error_message: str):
        super().__init__(error_message)
