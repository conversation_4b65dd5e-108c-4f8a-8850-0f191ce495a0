from datetime import date, datetime

from app.chatbot.constants import EXECUTION_RESULT_MAP, OperationResultText
from common.logging.logger import LoggingService


async def get_llm_readable_result(logger: LoggingService, status_code: int) -> OperationResultText:
    if not (result_text := EXECUTION_RESULT_MAP.get(status_code)):
        logger.warn(f"status code {status_code} not found in operation execution result map.")
        return EXECUTION_RESULT_MAP[500]

    return result_text


def json_serial_default(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    if isinstance(obj, date):
        return obj.isoformat()
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")
