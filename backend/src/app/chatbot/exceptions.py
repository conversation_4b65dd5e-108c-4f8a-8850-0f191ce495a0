from api.exceptions.base_exceptions import ErrorType, ServiceException


class ChatbotException(ServiceException):
    def __init__(self, error_message: str):
        super().__init__(error_message, ErrorType.INTERNAL_ERROR)


class NoConversationFoundException(ChatbotException):
    def __init__(self, user_id: int):
        super().__init__(f"Unable to retrieve any existing conversation for user_id: {user_id}")


class EmptyMessageHistoryException(ChatbotException):
    def __init__(self, user_id: int):
        super().__init__(
            f"The conversation retrieved for user: {user_id} had an empty message history"
        )


class RunResultIsNoneException(ChatbotException):
    def __init__(self, user_id: int):
        super().__init__(f"An agent run for user: {user_id} had a None result")


class NoOperationGroupSetException(ChatbotException):
    def __init__(self, user_id: int):
        super().__init__(
            f"The operation group was not set when processing data for user_id: {user_id}"
        )


class NoResultsProvidedException(ChatbotException):
    def __init__(self, user_id: int):
        super().__init__(
            f"No results have been retrieved from the chatbot operation for user_id: {user_id}"
        )
