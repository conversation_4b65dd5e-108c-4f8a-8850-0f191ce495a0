from datetime import datetime
from typing import Any, Protocol, runtime_checkable
from uuid import uuid4

from pydantic import BaseModel
from pydantic_ai import format_as_xml
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    SystemPromptPart,
    TextPart,
    UserPromptPart,
)

from app.chatbot.constants import OPENING_GREETING, SYSTEM_PROMPT
from app.chatbot.dtos.conversation import (
    Conversation,
    generate_message_id,
)
from app.chatbot.dtos.responses import (
    AssistantTextDTO,
    OutboundDTO,
    UserFacingMessage,
    UserTextDTO,
)
from app.chatbot.exceptions import (
    EmptyMessageHistoryException,
    NoConversationFoundException,
)
from app.chatbot.services.exceptions import ConversationStateError
from common.config.constants import ONE_DAY_SECONDS
from common.logging.logger import LoggingService
from gateway_accessors.aws.s3.accessor import S3Gateway
from gateway_accessors.aws.s3.enums import S3ContentType
from gateway_accessors.aws.s3.exceptions import S3Exception
from gateway_accessors.redis.accessor import RedisGateway


@runtime_checkable
class ConversationHistoryInterface(Protocol):
    async def get_latest_id(self, key: str) -> str | None: ...

    async def set_latest_id(self, key: str, conversation_id: str) -> None: ...

    async def get(self, key: str) -> Conversation | None: ...

    async def set(self, key: str, model: BaseModel) -> None: ...

    async def delete(self, key: str) -> None: ...


class RedisConversationStore(ConversationHistoryInterface):
    def __init__(self, redis_gateway: RedisGateway):
        self.redis = redis_gateway

    async def get_latest_id(self, key: str) -> str | None:
        return await self.redis.get(key)

    async def set_latest_id(self, key: str, conversation_id: str) -> None:
        await self.redis.set(key, conversation_id)

    async def get(self, key: str) -> Conversation | None:
        if data := await self.redis.get(key):
            return Conversation.model_validate_json(data)
        return None

    async def set(self, key: str, model: BaseModel) -> None:
        await self.redis.set(key=key, value=model.model_dump_json(), ttl=ONE_DAY_SECONDS)

    async def delete(self, key: str) -> None:
        await self.redis.delete(key)


class S3ConversationStore(ConversationHistoryInterface):
    def __init__(self, s3_gateway: S3Gateway):
        self.s3 = s3_gateway

    async def get_latest_id(self, key: str) -> str | None:
        try:
            id = self.s3.download_bytes(key)
        except S3Exception:
            return None

        if len(id) == 0:
            return None

        return id.decode()

    async def set_latest_id(self, key: str, conversation_id: str) -> None:
        self.s3.upload_bytes(
            key=key, content=conversation_id.encode(), content_type=S3ContentType.TXT
        )

    async def get(self, key: str) -> Conversation | None:
        try:
            data = self.s3.download_bytes(key)
        except S3Exception:
            return None

        if len(data) == 0:
            return None

        return Conversation.model_validate_json(data.decode())

    async def set(self, key: str, model: BaseModel) -> None:
        self.s3.upload_bytes(
            key=key, content=model.model_dump_json().encode(), content_type=S3ContentType.JSON
        )

    async def delete(self, key: str) -> None:
        self.s3.delete_file(key)


class ConversationHistoryStore:
    def __init__(
        self,
        logger: LoggingService,
        cache: ConversationHistoryInterface,
        storage: ConversationHistoryInterface,
    ):
        self.logger = logger
        self.cache = cache
        self.storage = storage

    def _create_latest_conversation_id_key(self, user_id: int) -> str:
        return f"chatbot/user:{user_id}/conversations/conversation:latest.txt"

    def _create_conversation_key(self, user_id: int, conversation_id: str) -> str:
        return f"chatbot/user:{user_id}/conversations/conversation:{conversation_id}.json"

    async def _get_latest_id(self, key: str) -> str | None:
        if id := await self.cache.get_latest_id(key):
            return id

        if id := await self.storage.get_latest_id(key):
            await self.cache.set_latest_id(key, id)
            return id

        return None

    async def get(self, key: str) -> Conversation | None:
        conversation = await self.cache.get(key)
        if conversation is None:
            conversation = await self.storage.get(key)
            if conversation is None:
                return None

            await self.cache.set(key, conversation)

        return conversation

    async def create_new_conversation(self, user_id: int) -> Conversation:
        id = uuid4()
        timenow = datetime.now()

        seed_conversation: list[ModelMessage | OutboundDTO] = [
            ModelRequest(
                parts=[
                    SystemPromptPart(
                        content=format_as_xml(
                            SYSTEM_PROMPT.model_dump(exclude_none=True), root_tag="initial_context"
                        ),
                        timestamp=timenow,
                    ),
                ]
            ),
            ModelResponse(
                parts=[
                    TextPart(
                        content=OPENING_GREETING.greeting,
                    )
                ]
            ),
        ]

        conversation = Conversation(
            id=f"{timenow.isoformat()}_{str(id)}",
            created_on=timenow,
            message_history=seed_conversation,
        )

        await self.set_latest_conversation(user_id, conversation)

        return conversation

    async def set_latest_conversation(self, user_id: int, conversation: Conversation):
        latest_id_key = self._create_latest_conversation_id_key(user_id)
        conversation_key = self._create_conversation_key(user_id, conversation.id)

        await self.storage.set(conversation_key, conversation)
        await self.storage.set_latest_id(latest_id_key, conversation.id)
        await self.cache.set(conversation_key, conversation)
        await self.cache.set_latest_id(latest_id_key, conversation.id)

    async def get_latest_conversation(self, user_id: int) -> Conversation | None:
        id_key = self._create_latest_conversation_id_key(user_id)

        id = await self._get_latest_id(id_key)
        if id is not None:
            conversation_key = self._create_conversation_key(user_id=user_id, conversation_id=id)

            conversation = await self.get(conversation_key)
            return conversation

        return None

    async def format_conversation_user_facing(
        self, user_id: int, conversation: Conversation
    ) -> list[UserFacingMessage]:
        if len(conversation.message_history) == 0:
            raise EmptyMessageHistoryException(user_id)
        user_facing_conversation: list[UserFacingMessage] = []

        for message in conversation.message_history:
            if isinstance(message, ModelRequest):
                for part in message.parts:
                    if isinstance(part, UserPromptPart):
                        user_facing_conversation.append(
                            UserTextDTO(
                                id=generate_message_id(part.timestamp),
                                timestamp=part.timestamp,
                                message=str(part.content),
                            )
                        )

            if isinstance(message, ModelResponse):
                for part in message.parts:
                    if isinstance(part, TextPart):
                        user_facing_conversation.append(
                            AssistantTextDTO(
                                id=generate_message_id(message.timestamp),
                                timestamp=message.timestamp,
                                message=part.content,
                            )
                        )

            if isinstance(message, OutboundDTO):
                user_facing_conversation.append(message)

        return user_facing_conversation

    async def format_conversation_agent_facing(
        self, user_id: int, conversation: Conversation
    ) -> list[ModelMessage]:
        if len(conversation.message_history) == 0:
            raise EmptyMessageHistoryException(user_id)
        agent_facing_conversation: list[ModelMessage] = []

        for message in conversation.message_history:
            if not isinstance(message, OutboundDTO):
                # Validate that the message has non-empty content
                if self._has_valid_content(message):
                    agent_facing_conversation.append(message)

        return agent_facing_conversation

    def _has_valid_content(self, message: ModelMessage) -> bool:
        """Check if a ModelMessage has non-empty content in its parts."""
        if isinstance(message, (ModelRequest, ModelResponse)):
            for part in message.parts:
                if isinstance(part, (TextPart, UserPromptPart, SystemPromptPart)):
                    if hasattr(part, "content") and part.content and part.content.strip():
                        return True
                # Non-text parts (like ToolReturnPart) are considered valid
                else:
                    return True
        return False

    async def overwrite_message_in_conversation(
        self,
        user_id: int,
        message_id: int,
        completed: bool,
        data: dict[str, Any] | list[dict[str, Any]],
    ) -> None:
        if not (conversation := await self.get_latest_conversation(user_id)):
            raise NoConversationFoundException(user_id)

        for msg in reversed(conversation.message_history):
            if isinstance(msg, OutboundDTO):
                if message_id == msg.id:
                    msg.completed = completed
                    msg.data = data
                    break

        await self.set_latest_conversation(user_id, conversation)

    async def set_conversation_to_storage(self, user_id: int, conversation: Conversation) -> None:
        conversation_key = self._create_conversation_key(user_id, conversation.id)
        await self.storage.set(conversation_key, conversation)

    async def delete(self, key: str):
        await self.cache.delete(key)
        await self.storage.delete(key)

    async def set(self, key: str, model: BaseModel):
        await self.cache.set(key, model)
        await self.storage.set(key, model)


class ConversationStateManager:
    """Manages conversation state during message processing."""

    def __init__(self, store: ConversationHistoryStore, user_id: int, logger: LoggingService):
        self.store = store
        self.user_id = user_id
        self.logger = logger
        self.conversation = None

    async def get_or_create_conversation(self) -> Conversation:
        """Get existing conversation or create a new one."""
        try:
            self.conversation = await self.store.get_latest_conversation(self.user_id)
            if not self.conversation:
                self.conversation = await self.store.create_new_conversation(self.user_id)
            return self.conversation
        except Exception as e:
            self.logger.error(f"Failed to get or create conversation: {e}")
            raise ConversationStateError(f"Conversation management failed: {e}") from e

    async def save_conversation(self, new_messages: list) -> None:
        """Save conversation with new messages."""
        try:
            if self.conversation is None:
                raise ConversationStateError("No active conversation to save")

            self.conversation.message_history.extend(new_messages)
            await self.store.set_latest_conversation(self.user_id, self.conversation)
        except Exception as e:
            self.logger.error(f"Failed to save conversation: {e}")
            raise ConversationStateError(f"Failed to save conversation: {e}") from e

    async def format_for_agent(self, user_id: int) -> list[ModelMessage]:
        """Format conversation history for agent consumption."""
        if self.conversation is None:
            raise ConversationStateError("No active conversation to format")
        return await self.store.format_conversation_agent_facing(user_id, self.conversation)
