import asyncio
from dataclasses import dataclass

from app.chatbot.dtos.responses import OutboundDTO
from app.chatbot.services.internal_client import InternalClient
from common.logging.logger import LoggingService
from gateway_accessors.aws.bedrock.accessor import BedrockGateway


@dataclass
class AgentDependencies:
    logger: LoggingService
    llm_gateway: BedrockGateway
    user_id: int
    jwt_token: str
    internal_client: InternalClient
    tool_output_queue: asyncio.Queue[OutboundDTO]
