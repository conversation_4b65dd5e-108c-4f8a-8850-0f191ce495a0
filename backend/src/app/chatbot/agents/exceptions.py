from app.chatbot.exceptions import ChatbotException


# These exceptions are not meant to be raised normally, rather their text is meant to be passed back to the chatbot as feedback of an unsuccessful operation / tool run.
class AgentFeedbackException(ChatbotException):
    message: str


class NoFieldsRequestedException(AgentFeedbackException):
    message = "No fields have been requested."


class NoContentException(AgentFeedbackException):
    def __init__(self, msg: str):
        self.message = msg
