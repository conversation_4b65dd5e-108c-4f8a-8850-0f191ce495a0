from pydantic_ai import RunContext

from api.schemas.requests.client_requests import AddressData as AddressDataUsingIds
from app.chatbot.agents.enum_lookup.agent import perform_lookup
from app.chatbot.agents.enum_lookup.helpers import get_ref_data
from app.chatbot.agents.exceptions import NoContentException
from app.chatbot.agents.models import AgentDependencies
from app.chatbot.agents.tools.helpers import get_data_with_readable_result
from app.chatbot.agents.user_text_input.input_schemas import (
    AddressData,
    AddressFilter,
    AddressFilterUsingIds,
)
from app.chatbot.constants import (
    OperationGroup,
    OperationResultText,
)
from app.chatbot.dtos.responses import (
    OutboundDTO,
)
from app.ref_data.ref_data_dtos import CountryDTO


async def get_country_lookup_data(ctx: RunContext[AgentDependencies]) -> list[CountryDTO]:
    country_ref_data = (await get_ref_data(ctx))["countries"]

    return [CountryDTO(**record) for record in country_ref_data]


async def convert_address_country_names_to_ids(
    ctx: RunContext[AgentDependencies], addresses: list[AddressData]
) -> list[AddressDataUsingIds]:
    names = [address.country_name for address in addresses]

    ids = await perform_lookup(ctx, "Country", await get_country_lookup_data(ctx), names)

    # Fallback default list. assumes 1 is the id of the desired default value.
    if ids is None or len(ids) != len(addresses):
        ids = [1 for _ in addresses]
    else:
        ids = [1 if id == 0 else id for id in ids]

    addresses_using_ids: list[AddressDataUsingIds] = []

    for i, address in enumerate(addresses):
        dict = address.model_dump()
        dict.pop("country_name")
        dict["country_id"] = ids[i]
        addresses_using_ids.append(AddressDataUsingIds(**dict))

    return addresses_using_ids


async def convert_address_filter_country_name_to_id(
    ctx: RunContext[AgentDependencies], filter: AddressFilter
) -> AddressFilterUsingIds:
    ids = await perform_lookup(
        ctx, "Country", await get_country_lookup_data(ctx), [filter.country_name]
    )

    # Fallback to filter of None
    if ids is None or len(ids) != 1:
        id = None
    else:
        id = None if ids[0] == 0 else ids[0]

    dict = filter.model_dump()
    dict.pop("country_name")
    dict["country_id"] = id

    return AddressFilterUsingIds(**dict)


async def post_user_addresses(
    ctx: RunContext[AgentDependencies], addresses: list[AddressData]
) -> str:
    """Populate a form with any new address details the user has provided in order to allow the user to create one or more new addresses. This form will be presented to the user separately, for validation. They will then be able to submit it programatically. This tool will NOT itself update the data. This tool can be run even if the user has not yet provided any address data."""

    addresses_using_ids = await convert_address_country_names_to_ids(ctx, addresses)

    data = OutboundDTO(
        data_layout=f"{OperationGroup.User_Addresses.value}_layout",
        data=[address.model_dump(mode="json") for address in addresses_using_ids],
    )

    await ctx.deps.tool_output_queue.put(data)

    return OperationResultText.SUCCESS.value


async def get_user_addresses(
    ctx: RunContext[AgentDependencies], filter_config: AddressFilter
) -> str:
    """Get one or more of the user's addresses and return whether the operation was successful."""

    filter_using_id = await convert_address_filter_country_name_to_id(ctx, filter_config)

    result_text, data = await get_data_with_readable_result(
        ctx,
        f"/operations/client/{ctx.deps.user_id}/addresses",
        params=filter_using_id.model_dump(exclude_none=True),
    )

    if data is not None:
        data.data_layout = f"{OperationGroup.User_Addresses.value}_layout"

        await ctx.deps.tool_output_queue.put(data)

    return result_text.value


async def put_user_addresses(
    ctx: RunContext[AgentDependencies],
    filter_config: AddressFilter,
    new_values: list[AddressData] | None = None,
) -> str:
    """Populate a form with one or more existing addresses, with any new values the user has already provided filled in advance. This form will be presented to the user separately, for validation. They will then be able to submit it programatically. This tool will NOT itself update the data. This tool can be run even if the user has not yet provided any address data. It is *NOT* necessary to run a GET prior to running this tool."""

    filter_using_id = await convert_address_filter_country_name_to_id(ctx, filter_config)

    result_text, data = await get_data_with_readable_result(
        ctx,
        f"/operations/client/{ctx.deps.user_id}/addresses",
        params=filter_using_id.model_dump(exclude_none=True),
    )

    if data is None:
        return NoContentException(
            f"Unable to edit addresses data due to an inability to get existing values: {result_text.value}"
        ).message

    data.data_layout = f"{OperationGroup.User_Addresses.value}_layout"

    if not isinstance(data.data, list):
        ctx.deps.logger.error("Unexpected form of returned address data. Expected List.")
        return OperationResultText.SERVER_ERROR.value

    # not currently handling scenarios where the user attempts to provide new values for multiple addresses at once
    if new_values is None or len(new_values) != 1 or len(data.data) != 1:
        await ctx.deps.tool_output_queue.put(data)

        return OperationResultText.SUCCESS.value

    for field, value in new_values[0]:
        if value is not None:
            if field == "country_name":
                addresses_using_ids = await convert_address_country_names_to_ids(ctx, new_values)
                data.data[0]["country_id"] = addresses_using_ids[0].country_id
                continue

            data.data[0][field] = getattr(new_values[0], field)

    await ctx.deps.tool_output_queue.put(data)

    return OperationResultText.SUCCESS.value


ADDRESS_TOOLS = [
    get_user_addresses,
    post_user_addresses,
    put_user_addresses,
]
