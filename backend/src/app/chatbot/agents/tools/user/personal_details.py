from typing import Dict

from pydantic_ai import Run<PERSON>ontext

from app.chatbot.agents.exceptions import NoFieldsRequestedException
from app.chatbot.agents.models import AgentDependencies
from app.chatbot.agents.tools.helpers import get_data_with_readable_result
from app.chatbot.agents.user_text_input.input_schemas import (
    FieldsRequested,
)
from app.chatbot.constants import (
    OperationGroup,
    OperationResultText,
)
from app.chatbot_operations.dtos.operations_responses import User


async def get_user_personal_info(
    ctx: RunContext[AgentDependencies], requested_fields: FieldsRequested.User.PersonalDetails
) -> str:
    """Get data about the user and return whether it was retrieved successfully."""

    requested_fields_list = [field for field, requested in requested_fields if requested]
    if len(requested_fields_list) == 0:
        return NoFieldsRequestedException.message

    result_text, data = await get_data_with_readable_result(
        ctx,
        f"/operations/client/{ctx.deps.user_id}",
        requested_fields=requested_fields_list,
    )

    if data is not None:
        data.data_layout = f"{OperationGroup.User_PersonalDetails.value}_layout"

        await ctx.deps.tool_output_queue.put(data)

    return result_text.value


async def update_user_personal_info(
    ctx: RunContext[AgentDependencies],
    fields_for_edit: FieldsRequested.User.PersonalDetails,
    new_values: User.PersonalDetails | None = None,
) -> str:
    """Populate a form with the updated personal details the user has provided. This form will be presented to the user separately, for validation. They will then be able to submit it programatically. This tool will NOT itself update the data."""

    requested_fields_list = [field for field, requested in fields_for_edit if requested]
    if len(requested_fields_list) == 0:
        return NoFieldsRequestedException.message

    result_text, data = await get_data_with_readable_result(
        ctx,
        f"/operations/client/{ctx.deps.user_id}",
        requested_fields=requested_fields_list,
    )

    if data is None:
        msg = f"Unable to edit user data due to an inability to get existing values: {result_text.value}"
        ctx.deps.logger.error(msg)
        return msg

    data.data_layout = f"{OperationGroup.User_PersonalDetails.value}_layout"

    if new_values is None:
        await ctx.deps.tool_output_queue.put(data)

        return OperationResultText.SUCCESS.value

    if not isinstance(data.data, Dict):
        ctx.deps.logger.error("Unexpected form of returned personal details data. Expected Dict.")
        return OperationResultText.SERVER_ERROR.value

    for field, value in new_values.__dict__.items():
        if value is not None:
            data.data[field] = getattr(new_values, field)

    await ctx.deps.tool_output_queue.put(data)

    return OperationResultText.SUCCESS.value


PERSONAL_INFO_TOOLS = [
    get_user_personal_info,
    update_user_personal_info,
]
