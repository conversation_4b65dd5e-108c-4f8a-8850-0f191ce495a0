from pydantic_ai import Run<PERSON>ontext

from app.chatbot.agents.exceptions import NoContentException
from app.chatbot.agents.models import AgentDependencies
from app.chatbot.agents.tools.helpers import get_data_with_readable_result
from app.chatbot.agents.user_text_input.input_schemas import (
    <PERSON>lationData,
    RelationFilter,
)
from app.chatbot.constants import (
    OperationGroup,
    OperationResultText,
)
from app.chatbot.dtos.responses import (
    OutboundDTO,
)


async def post_user_relations(
    ctx: RunContext[AgentDependencies], relations: list[RelationData]
) -> str:
    """Populate a form with any new relations details the user has provided in order to allow the user to create one or more new relations. This form will be presented to the user separately, for validation. They will then be able to submit it programatically. This tool will NOT itself update the data. This tool can be run even if the user has not yet provided any relation data."""

    data = OutboundDTO(
        data_layout=f"{OperationGroup.User_Relations.value}_layout",
        data=[relation.model_dump(mode="json") for relation in relations],
    )

    await ctx.deps.tool_output_queue.put(data)

    return OperationResultText.SUCCESS.value


async def get_user_relations(
    ctx: RunContext[AgentDependencies], filter_config: RelationFilter
) -> str:
    """Get one or more of the user's relations and return whether the operation was successful."""

    result_text, data = await get_data_with_readable_result(
        ctx,
        f"/operations/client/{ctx.deps.user_id}/relations",
        params=filter_config.model_dump(exclude_none=True),
    )

    if data is not None:
        data.data_layout = f"{OperationGroup.User_Relations.value}_layout"

        await ctx.deps.tool_output_queue.put(data)

    return result_text.value


async def put_user_relations(
    ctx: RunContext[AgentDependencies],
    filter_config: RelationFilter,
    new_values: list[RelationData] | None = None,
) -> str:
    """Populate a form with one or more existing relations, with any new values the user has already provided filled in advance. This form will be presented to the user separately, for validation. They will then be able to submit it programatically. This tool will NOT itself update the data. This tool can be run even if the user has not yet provided any relation data. It is *NOT* necessary to run a GET prior to running this tool."""

    result_text, data = await get_data_with_readable_result(
        ctx,
        f"/operations/client/{ctx.deps.user_id}/relations",
        params=filter_config.model_dump(exclude_none=True),
    )

    if data is None:
        return NoContentException(
            f"Unable to edit relations data due to an inability to get existing values: {result_text.value}"
        ).message

    data.data_layout = f"{OperationGroup.User_Relations.value}_layout"

    if not isinstance(data.data, list):
        ctx.deps.logger.error("Unexpected form of returned relation data. Expected List.")
        return OperationResultText.SERVER_ERROR.value

    # not currently handling scenarios where the user attempts to provide new values for multiple relations at once
    if new_values is None or len(new_values) != 1 or len(data.data) != 1:
        await ctx.deps.tool_output_queue.put(data)

        return OperationResultText.SUCCESS.value

    for field, value in new_values[0]:
        if value is not None:
            data.data[0][field] = getattr(new_values[0], field)

    await ctx.deps.tool_output_queue.put(data)

    return OperationResultText.SUCCESS.value


RELATIONS_TOOLS = [
    get_user_relations,
    post_user_relations,
    put_user_relations,
]
