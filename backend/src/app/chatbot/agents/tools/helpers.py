from typing import Any, Dict, List, <PERSON><PERSON>

import httpx
from pydantic_ai import Run<PERSON>ontext

from app.chatbot.agents.models import AgentDependencies
from app.chatbot.constants import OperationResultText
from app.chatbot.dtos.responses import OutboundDTO
from app.chatbot.helpers import get_llm_readable_result


async def get_data(
    ctx: RunContext[AgentDependencies],
    endpoint: str,
    params: dict[str, Any],
) -> httpx.Response:
    response: httpx.Response | None = None

    params["client_id"] = ctx.deps.user_id

    # returns dict containing each field asked for and the associated value
    async with ctx.deps.internal_client.get_internal_client() as client:
        response = await client.get(
            f"/api/v2{endpoint}",
            params=params,
            headers={"Authorization": ctx.deps.jwt_token},
        )

    return response


async def get_data_with_readable_result(
    ctx: RunContext[AgentDependencies],
    endpoint: str,
    params: Dict[str, Any] | None = None,
    requested_fields: list[str] | None = None,
) -> Tuple[OperationResultText, OutboundDTO | None]:
    if params is None:
        params = {}

    if requested_fields is not None:
        params["fields"] = requested_fields

    response = await get_data(ctx, endpoint, params)

    result_text = await get_llm_readable_result(ctx.deps.logger, response.status_code)

    if response.status_code != 200:
        return result_text, None

    data = response.json()

    if (
        data is None
        or (isinstance(data, List) and len(data) == 0)
        or (isinstance(data, Dict) and data == {})
    ):
        return OperationResultText.NO_CONTENT, None

    model = OutboundDTO(
        available_fields=requested_fields,
        data=data,
    )

    return result_text, model
