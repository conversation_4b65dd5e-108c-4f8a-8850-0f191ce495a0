from pydantic_ai import RunContext

from app.chatbot.agents.exceptions import NoFieldsRequestedException
from app.chatbot.agents.models import AgentDependencies
from app.chatbot.agents.tools.helpers import get_data_with_readable_result
from app.chatbot.agents.user_text_input.input_schemas import (
    FieldsRequested,
)
from app.chatbot.constants import (
    OperationGroup,
)


async def get_aventur_company_data(
    ctx: RunContext[AgentDependencies], requested_fields: FieldsRequested.Company
) -> str:
    """Get data about <PERSON>nt<PERSON> and return whether it was retrieved successfully."""

    requested_fields_list = [field for field, requested in requested_fields if requested]
    if len(requested_fields_list) == 0:
        return NoFieldsRequestedException.message

    result_text, data = await get_data_with_readable_result(
        ctx,
        "/operations/company",
        requested_fields=requested_fields_list,
    )

    if data is not None:
        data.data_layout = f"{OperationGroup.Company.value}_layout"

        await ctx.deps.tool_output_queue.put(data)

    return result_text.value


COMPANY_TOOLS = [
    get_aventur_company_data,
]
