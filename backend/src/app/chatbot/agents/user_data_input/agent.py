from typing import Any

from pydantic_ai import Agent
from pydantic_ai.settings import ModelSettings

from app.chatbot.agents.tools.null_tool import null_tool
from app.chatbot.exceptions import NoOperationGroupSetException, NoResultsProvidedException
from app.chatbot.services.post_message.operations import OperationResults
from gateway_accessors.aws.bedrock.accessor import BedrockGateway
from gateway_accessors.aws.bedrock.constants import Model<PERSON>ame


def generate_readable_result_list(user_id: int, results: OperationResults) -> str:
    if results.operation_group is None:
        raise NoOperationGroupSetException(user_id)

    if len(results.results) == 0:
        raise NoResultsProvidedException(user_id)

    s = f"{results.operation_type.value}: {results.operation_group.value}\n"
    s = s + "\n".join(f"{i}: {res.value}" for i, res in enumerate(results.results, 1)) + "\n"
    if results.additional_context is not None:
        s = s + f"NOTE: {results.additional_context}\n"

    return s


async def create_data_input_agent(
    user_id: int,
    llm_gateway: BedrockGateway,
    results: OperationResults,
) -> Agent[None, Any]:
    model = await llm_gateway.get_model(ModelName.claude_3_7_sonnet_20250219_inference_eu)

    instructions = f"""The user has submitted data, so you have just executed the following operation on the their behalf. Let the user know if the operation has been successful, or if there have been failures, and then continue the conversation appropriately:

{generate_readable_result_list(user_id, results)}"""

    return Agent(
        instructions=instructions,
        model=model,
        output_type=str,
        model_settings=ModelSettings(temperature=0.0),
        tools=[null_tool],
    )
