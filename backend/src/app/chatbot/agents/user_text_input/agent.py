from pydantic_ai import Agent
from pydantic_ai.settings import ModelSettings

from app.chatbot.agents.models import AgentDependencies
from app.chatbot.agents.tools.company.company import COMPANY_TOOLS
from app.chatbot.agents.tools.user.addresses import (
    ADDRESS_TOOLS,
)
from app.chatbot.agents.tools.user.consent import prompt_for_chatbot_consent_withdrawal
from app.chatbot.agents.tools.user.health_score import HEALTH_SCORE_TOOLS
from app.chatbot.agents.tools.user.personal_details import (
    PERSONAL_INFO_TOOLS,
)
from app.chatbot.agents.tools.user.relations import (
    RELATIONS_TOOLS,
)
from gateway_accessors.aws.bedrock.accessor import BedrockGateway
from gateway_accessors.aws.bedrock.constants import ModelName

Instructions = """"Respond succinctly. Ensure you select all relevant tools at once, rather than selecting tools over the course of multiple messages. DO NOT explicitly state the value of data the user has provided to you in conversation."""


async def create_text_input_agent(
    llm_gateway: BedrockGateway,
) -> Agent[AgentDependencies]:
    model = await llm_gateway.get_model(ModelName.claude_3_7_sonnet_20250219_inference_eu)

    agent = Agent(
        instructions=Instructions,
        model=model,
        deps_type=AgentDependencies,
        output_type=str,
        model_settings=ModelSettings(temperature=0.0, parallel_tool_calls=True),
        tools=[
            *COMPANY_TOOLS,
            *PERSONAL_INFO_TOOLS,
            *HEALTH_SCORE_TOOLS,
            *ADDRESS_TOOLS,
            *RELATIONS_TOOLS,
            prompt_for_chatbot_consent_withdrawal,
        ],
    )

    return agent
