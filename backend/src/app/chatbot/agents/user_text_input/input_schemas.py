from datetime import date
from typing import Union

from pydantic import BaseModel, Field

from api.enums.enums import Relationship


class FieldsRequested:
    class Company(BaseModel):
        email: bool = False
        phone_number: bool = False
        address: bool = False
        website: bool = False
        documentation_address: bool = False

    class User:
        class PersonalDetails(BaseModel):
            first_name: bool = False
            last_name: bool = False
            email: bool = False
            phone_number: bool = False
            mobile_number: bool = False
            date_of_birth: bool = False


FieldsRequestedModels = Union[
    FieldsRequested.Company,
    FieldsRequested.User.PersonalDetails,
]


class AddressData(BaseModel):
    address_line_one: str | None = None
    address_line_two: str | None = None
    address_line_three: str | None = None
    address_line_four: str | None = None
    city: str | None = None
    post_code: str | None = None
    country_name: str | None = None  # must be translated to the corresponding id by llm
    moved_in_date: date | None = None
    moved_out_date: date | None = None
    is_primary: bool | None = None


class AddressFilter(BaseModel):
    address_line_one: str | None = Field(None, description="Filter by address line one")
    address_line_two: str | None = Field(None, description="Filter by address line two")
    address_line_three: str | None = Field(None, description="Filter by address line three")
    address_line_four: str | None = Field(None, description="Filter by address line four")
    city: str | None = Field(None, description="Filter by city")
    post_code: str | None = Field(None, description="Filter by post code")
    country_name: str | None = Field(None, description="Filter by country name")
    moved_in_date_from: date | None = Field(None, description="Filter by moved in date (from)")
    moved_in_date_to: date | None = Field(None, description="Filter by moved in date (to)")
    moved_out_date_from: date | None = Field(None, description="Filter by moved out date (from)")
    moved_out_date_to: date | None = Field(None, description="Filter by moved out date (to)")
    is_primary: bool | None = Field(None, description="Filter by primary address status")
    exact_match: bool = Field(
        False, description="Use exact matching instead of partial matching for text fields"
    )


class AddressFilterUsingIds(BaseModel):
    address_line_one: str | None
    address_line_two: str | None
    address_line_three: str | None
    address_line_four: str | None
    city: str | None
    post_code: str | None
    country_id: int | None
    moved_in_date_from: date | None
    moved_in_date_to: date | None
    moved_out_date_from: date | None
    moved_out_date_to: date | None
    is_primary: bool | None
    exact_match: bool


class RelationFilter(BaseModel):
    relationship_type: Relationship | None = Field(None, description="Filter by relationship type")


class RelationData(BaseModel):
    first_name: str | None = None
    last_name: str | None = None
    date_of_birth: date | None = None
    relationship_type: Relationship | None = None
