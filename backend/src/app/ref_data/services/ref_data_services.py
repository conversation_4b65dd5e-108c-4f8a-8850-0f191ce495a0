import asyncio
from typing import Any, Mapping, Type, TypeVar

from dacite import from_dict
from sqlalchemy import inspect

from api import enums
from app.base.base_service import BaseCommandService, BaseService
from app.documents.enums import DocumentType
from app.holdings.queries.holding_queries import (
    HoldingStatusCategory,
    HoldingStatusQuery,
)
from app.ref_data.ref_data_dtos import (
    AdviceTypeDTO,
    AssigneeGroupDTO,
    CountryDTO,
    GoalDTO,
    IdNameDTO,
    IncomeExpenditureTypeDTO,
    NoEmailReasonDTO,
    ProductTypeDTO,
    ProductTypeGroupDTO,
    ProviderDTO,
    RefDataBlob,
)
from app.shared_kernel.type_defs import DataclassType
from app.shared_kernel.vos.holding_vos import HoldingStatusDTO
from common.config.constants import ONE_DAY_SECONDS
from common.logging.logger import LoggingService
from database.models import (
    AdviceGroupModel,
    AdviceTypesModel,
    BaseDBModel,
    CountryModel,
    GenderModel,
    GoalTypeModel,
    HoldingStatusModel,
    IncomeExpenditureTypesModel,
    MaritalStatusModel,
    NationalitiesModel,
    NoEmailReasonModel,
    PersonalTitleModel,
    PortfolioModelModel,
    ProductTypeGroupsModel,
    ProductTypeModel,
    ProviderModel,
)
from database.query_builders.ref_data_query_builders import (
    AdviceTypesQueryBuilder,
    CashflowsQueryBuilder,
    ClientGoalsQueryBuilder,
    NationalityQueryBuilder,
    ProductTypeGroupQueryBuilder,
    ProductTypeQueryBuilder,
    SelectAllQueryBuilder,
)
from database.session import AsyncSessionFactory
from gateway_accessors.redis.accessor import RedisDataKeys, RedisGateway

DtoType = TypeVar("DtoType", bound=DataclassType)
OrmEntity = TypeVar("OrmEntity", bound=BaseDBModel)


class RefDataService(BaseService):
    refdata_mapping = {
        "cashflows": {
            "orm_model": IncomeExpenditureTypesModel,
            "dto_type": IncomeExpenditureTypeDTO,
            "query_builder_type": CashflowsQueryBuilder,
        },
        "countries": {
            "orm_model": CountryModel,
            "dto_type": CountryDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "providers": {
            "orm_model": ProviderModel,
            "dto_type": ProviderDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "nationalities": {
            "orm_model": NationalitiesModel,
            "dto_type": IdNameDTO,
            "query_builder_type": NationalityQueryBuilder,
        },
        "goals": {
            "orm_model": GoalTypeModel,
            "dto_type": GoalDTO,
            "query_builder_type": ClientGoalsQueryBuilder,
        },
        "advice_groups": {
            "orm_model": AdviceGroupModel,
            "dto_type": IdNameDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "advice_types": {
            "orm_model": AdviceTypesModel,
            "dto_type": AdviceTypeDTO,
            "query_builder_type": AdviceTypesQueryBuilder,
        },
        "product_types": {
            "orm_model": ProductTypeModel,
            "dto_type": ProductTypeDTO,
            "query_builder_type": ProductTypeQueryBuilder,
        },
        "product_type_groups": {
            "orm_model": ProductTypeGroupsModel,
            "dto_type": ProductTypeGroupDTO,
            "query_builder_type": ProductTypeGroupQueryBuilder,
        },
        "holding_statuses": {
            "orm_model": HoldingStatusModel,
            "dto_type": IdNameDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "no_email_reasons": {
            "orm_model": NoEmailReasonModel,
            "dto_type": NoEmailReasonDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "genders": {
            "orm_model": GenderModel,
            "dto_type": IdNameDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "marital_statuses": {
            "orm_model": MaritalStatusModel,
            "dto_type": IdNameDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "personal_titles": {
            "orm_model": PersonalTitleModel,
            "dto_type": IdNameDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
        "portfolio_models": {
            "orm_model": PortfolioModelModel,
            "dto_type": IdNameDTO,
            "query_builder_type": SelectAllQueryBuilder,
        },
    }

    def __init__(
        self, session: AsyncSessionFactory, redis: RedisGateway, logger: LoggingService
    ) -> None:
        super().__init__(logger)
        self.session = session
        self.redis = redis

    async def __call__(self, *args, **kwargs) -> Any:
        raise NotImplementedError

    async def get(self) -> RefDataBlob:
        if not (refdata := await self.redis.get(key=RedisDataKeys.REF_DATA_ALL)):
            refdata = await self._build_refdata()
            await self.redis.set(RedisDataKeys.REF_DATA_ALL, refdata, ttl=ONE_DAY_SECONDS)
        return refdata

    async def delete(self) -> None:
        await self.redis.delete(RedisDataKeys.REF_DATA_ALL)

    async def _build_refdata(self) -> RefDataBlob:
        query_data = await self._run_queries()
        return RefDataBlob(
            **query_data,
            # Add enums
            assignee_groups=[
                AssigneeGroupDTO.from_role(role) for role in enums.UserRole.assignable_roles()
            ],
            client_sources=[IdNameDTO.from_int_enum(type_) for type_ in enums.ClientSource],
            client_statuses=[IdNameDTO.from_int_enum(status) for status in enums.ClientStatus],
            client_types=[IdNameDTO.from_int_enum(type_) for type_ in enums.ClientType],
            document_template_types=list(DocumentType),
            relationship_types=[
                IdNameDTO.from_int_enum(type_) for type_ in enums.ClientLinkRelationship
            ],
            risk_level=[IdNameDTO.from_int_enum(level) for level in enums.EstimatedRiskLevel],
        )

    async def _run_queries(self) -> Any:
        query_tasks: dict[str, asyncio.Task] = {}
        async with asyncio.TaskGroup() as tg:
            for result_key, mapping in self.refdata_mapping.items():
                query_tasks[result_key] = self._new_execute_query_task(
                    orm_model=mapping["orm_model"],
                    dto_type=mapping["dto_type"],
                    query_builder_type=mapping["query_builder_type"],
                    task_group=tg,
                )

        return {name: await task for name, task in query_tasks.items()}

    def _new_execute_query_task(
        self,
        orm_model: Type[OrmEntity],
        dto_type: Type[DtoType],
        query_builder_type: Type[SelectAllQueryBuilder],
        task_group: asyncio.TaskGroup,
    ) -> asyncio.Task:
        async def execute_query() -> list[DtoType]:
            async with self.session() as session:
                query_builder = query_builder_type(orm_model)
                result = await session.execute(query_builder.build())
                mapped: list[DtoType] = []
                for row in result.mappings():
                    mapped.append(self._map_to_dto(row[orm_model.__name__], row, dto_type))  # type: ignore
                return mapped

        return task_group.create_task(execute_query())

    def _map_to_dto(
        cls, entity: OrmEntity, extra_columns: Mapping[str, Any], dto_type: Type[DtoType]
    ) -> DtoType:
        def entity_to_dict(entity_instance: OrmEntity, visited=None) -> dict[str, Any]:
            if visited is None:
                visited = set()

            # Avoid infinite recursion
            if entity_instance in visited:
                return None  # type: ignore
            visited.add(entity_instance)

            entity_dict = {
                c.key: getattr(entity_instance, c.key)
                for c in inspect(entity_instance).mapper.column_attrs
            }

            # Handling relationships
            inspector = inspect(entity_instance)
            non_eager_relationships = inspector.unloaded
            for name, relation in inspector.mapper.relationships.items():
                if name in non_eager_relationships:
                    continue
                related_value = getattr(entity_instance, name)
                if related_value is None:
                    entity_dict[name] = None
                elif relation.uselist:  # If it's a list, build a list of dicts
                    entity_dict[name] = [
                        entity_to_dict(item, visited=visited) for item in related_value
                    ]
                else:  # If it's a single item, convert it directly
                    entity_dict[name] = entity_to_dict(related_value, visited=visited)
            return entity_dict

        entity_dict = {**entity_to_dict(entity), **extra_columns}
        return from_dict(data_class=dto_type, data=entity_dict)


class HoldingStatusRefDataService(BaseCommandService):
    async def __call__(self, *, category: HoldingStatusCategory) -> list[HoldingStatusDTO]:  # type: ignore
        async with self.uow:
            if category is None:
                holding_statuses = await HoldingStatusQuery(uow=self.uow).all()
            else:
                holding_statuses = await HoldingStatusQuery(uow=self.uow).by_category(
                    category=category
                )

        return holding_statuses
