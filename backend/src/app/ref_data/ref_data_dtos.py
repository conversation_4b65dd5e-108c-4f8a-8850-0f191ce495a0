from dataclasses import dataclass
from enum import IntEnum
from typing import Literal, Self

from api import enums
from app.shared_kernel.parsers import parse_camel_case_to_space_delimited, snake_case_to_title_case


@dataclass(slots=True, frozen=True)
class IdNameDTO:
    id: int
    name: str

    @classmethod
    def from_int_enum(cls, enum: IntEnum) -> Self:
        name = parse_camel_case_to_space_delimited(value=enum.name)
        return cls(id=enum.value, name=name)


@dataclass(slots=True, frozen=True)
class CountryDTO(IdNameDTO):
    iso_alpha2: str
    iso_alpha3: str


@dataclass(slots=True, frozen=True)
class ProviderDTO(IdNameDTO):
    is_active: bool | None


@dataclass(slots=True, frozen=True)
class GoalDTO(IdNameDTO):
    code: str
    is_default: bool
    description: str
    client_goal: bool


@dataclass(slots=True, frozen=True)
class ProductTypeDTO(IdNameDTO):
    product_type_group_id: int
    type: enums.ProductTypeType
    data_layout: enums.ProductDataLayout | None


@dataclass(slots=True, frozen=True)
class NoEmailReasonDTO:
    id: int
    reason: str


@dataclass(slots=True, frozen=True)
class AssigneeGroupDTO:
    value: enums.UserRole
    label: str

    @classmethod
    def from_role(cls, role: enums.UserRole):
        return cls(value=role, label=snake_case_to_title_case(role.value))


@dataclass(slots=True, frozen=True)
class AdviceTypeDTO:
    id: int
    name: str
    proposed: bool
    existing: bool
    requires_amount: bool
    requires_frequency: bool
    requires_portfolio: bool
    requires_account: bool
    complementary_advice_type_id: int | None


@dataclass(slots=True, frozen=True)
class ProductTypeGroupDTO:
    id: int
    name: str
    asset_or_debt: Literal["asset", "debt"]
    compatible_advice_types: list[int]


@dataclass(slots=True, frozen=True)
class IncomeExpenditureTypeGroupDTO:
    id: int
    type: enums.CashflowType | None
    name: str | None
    description: str | None


@dataclass(slots=True, frozen=True)
class IncomeExpenditureTypeDTO:
    id: int
    income_expenditure_group: IncomeExpenditureTypeGroupDTO
    name: str | None
    is_active: bool | None
    is_essential: bool | None


@dataclass(slots=True, frozen=True)
class RefDataBlob:
    cashflows: list[IncomeExpenditureTypeDTO]
    countries: list[CountryDTO]
    providers: list[ProviderDTO]
    nationalities: list[IdNameDTO]
    goals: list[GoalDTO]
    advice_groups: list[IdNameDTO]
    advice_types: list[AdviceTypeDTO]
    product_types: list[ProductTypeDTO]
    product_type_groups: list[ProductTypeGroupDTO]
    holding_statuses: list[IdNameDTO]
    no_email_reasons: list[NoEmailReasonDTO]
    genders: list[IdNameDTO]
    marital_statuses: list[IdNameDTO]
    personal_titles: list[IdNameDTO]
    portfolio_models: list[IdNameDTO]
    # Enums
    assignee_groups: list[AssigneeGroupDTO]
    client_sources: list[IdNameDTO]
    client_statuses: list[IdNameDTO]
    client_types: list[IdNameDTO]
    document_template_types: list[str]
    relationship_types: list[IdNameDTO]
    risk_level: list[IdNameDTO]
