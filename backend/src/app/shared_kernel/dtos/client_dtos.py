from __future__ import annotations

from dataclasses import dataclass
from datetime import date as Date
from datetime import datetime

from api.enums import <PERSON><PERSON>tatus, IdentityCheckResult, ReviewType
from api.exceptions.base_exceptions import BadRequestServiceException
from app.shared_kernel.dtos.administrators import Administrator<PERSON>asicD<PERSON>
from common.config.constants import (
    CLIENT_NOTE_MAX_LENGTH,
    MAX_MONTHS_DIFFERENCE_FOR_SELECTING_A_REVIEW_SLOT_INTO_THE_FUTURE,
)


@dataclass(frozen=True, slots=True)
class ClientDTO:
    id: int
    first_name: str | None
    last_name: str | None
    email: str | None = None


@dataclass(frozen=True, slots=True)
class AMLSearchResultDTO:
    result: IdentityCheckResult
    date: Date


@dataclass(frozen=True, slots=True)
class ClientNoteDTO:
    @dataclass()
    class AdministratorDTO:
        id: int
        first_name: str
        last_name: str

    id: int
    created_by: AdministratorDTO
    created_datetime: datetime
    client_id: int
    content: str


@dataclass(frozen=True, slots=True)
class AddClientNoteDTO:
    created_by_id: int
    client_id: int
    content: str

    def assert_content_length(self) -> None:
        if len(self.content) > CLIENT_NOTE_MAX_LENGTH:
            raise BadRequestServiceException(
                f"Note's content length ({len(self.content)}) is greater than"
                f" configured maximum ({CLIENT_NOTE_MAX_LENGTH})."
            )


@dataclass(frozen=True, slots=True)
class ReviewCaseDTO:
    id: int
    adviser_id: int | None
    status: CaseStatus


@dataclass(frozen=True, slots=True)
class ReviewDTO:
    id: int
    clients: list[ClientDTO]
    client_owner_id: int
    review_group_id: int
    review_month: int
    review_year: int
    review_type: ReviewType
    case_data: ReviewCaseDTO | None = None


@dataclass(frozen=True, slots=True)
class ReviewGroupDTO:
    review_group_id: int
    review_frequency: str
    review_month: int
    is_active: bool
    clients: list[ClientDTO]
    review_group_status: bool
    next_review: Date | None = None


@dataclass(frozen=True, slots=True)
class ClientReviewSlotDTO:
    id: int
    description: str


@dataclass(frozen=True, slots=True)
class AvailableClientReviewSlotsDTO:
    available_review_slots: list[ClientReviewSlotDTO]
    has_review_slots_outside_of_max_assignable_period: bool


@dataclass(frozen=True, slots=True)
class UpcomingReviewQueryFilterDTO:
    time_limit_months: int | None = (
        MAX_MONTHS_DIFFERENCE_FOR_SELECTING_A_REVIEW_SLOT_INTO_THE_FUTURE
    )
    client_owner_id: int | None = None
    case_adviser_id: int | None = None
    case_status: CaseStatus | None = None
    open_slots_only: bool = False
    text: str | None = None


@dataclass(frozen=True, slots=True)
class CountryDTO:
    id: int | None
    name: str | None
    iso_alpha2: str | None
    iso_alpha3: str | None


@dataclass(slots=True)
class AddressDTO:
    address_line_one: str | None = None
    address_line_two: str | None = None
    address_line_three: str | None = None
    address_line_four: str | None = None
    city: str | None = None
    country_id: int | None = None
    country: CountryDTO | None = None
    post_code: str | None = None
    moved_in_date: Date | None = None
    moved_out_date: Date | None = None
    is_primary: bool | None = None


@dataclass(slots=True)
class ExistingAddressDTO(AddressDTO):
    id: int | None = None


@dataclass(frozen=True, slots=True)
class ClientLinkDTO:
    client_id: int
    first_name: str
    last_name: str
    link_relationship_id: int | None


@dataclass(slots=True)
class ClientDetailDTO:
    id: int
    type: str
    email: str
    addresses: list[AddressDTO]
    links: list[ClientLinkDTO]
    access_enabled: bool | None = None
    no_email_reason_id: int | None = None
    title_id: int | None = None
    first_name: str | None = None
    last_name: str | None = None
    date_of_birth: Date | None = None
    phone_number: str | None = None
    mobile_number: str | None = None
    marital_status_id: int | None = None
    gender_id: int | None = None
    gender: str | None = None
    nationality_id: int | None = None
    client_source_id: int | None = None
    client_status_id: int | None = None
    client_type_id: int | None = None
    review_frequency: str | None = None
    review_month: int | None = None
    next_review_month: int | None = None
    chatbot_consent_granted: bool = False
    chatbot_consent_timestamp: datetime | None = None

    advisor: AdministratorBasicDTO | None = None

    client_agreement_id: int | None = None
    privacy_notice_id: int | None = None

    @property
    def primary_address(self) -> AddressDTO | None:
        for address in self.addresses:
            if address.is_primary:
                return address
        if self.addresses:
            return self.addresses[-1]
        else:
            return None

    @property
    def ready_for_aml(self) -> bool:
        if not self.date_of_birth or not self.primary_address or not self.primary_address.country:
            return False
        return True


@dataclass(frozen=True, slots=True)
class ClientSummaryDTO:
    id: int
    links: int
    client_status_id: int
    client_type_id: int
    first_name: str
    last_name: str
    email: str
    accounts: int
    owner_id: int | None
    owner_first_name: str | None
    owner_last_name: str | None
    chatbot_consent_granted: bool
