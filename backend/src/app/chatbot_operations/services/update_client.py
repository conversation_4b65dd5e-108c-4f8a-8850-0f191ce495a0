from api.enums import Client<PERSON>ource, ClientStatus, ClientType
from api.enums.enums import ClientLinkRelationship
from api.routers.api_v2.chatbot.operations_requests import PersonalDetails
from api.schemas.requests.client_requests import ClientLink, UpdateClientSchema
from app.base.base_service import BaseService
from app.chatbot_operations.exceptions import (
    NoClientAdvisor,
    NoClientFirstName,
    NoClientLastName,
    NoClientSource,
    NoClientStatus,
    NoClientType,
)
from app.clients.exceptions import ClientNotFound
from app.clients.services.client_query_services import ClientQueryServices
from app.clients.services.update_client import UpdateClientService as ClientUpdateClientService
from common.logging.logger import LoggingService


class UpdateClientService(BaseService):
    def __init__(
        self,
        *,
        logger: LoggingService,
        client_query_services: ClientQueryServices,
        update_client_service: ClientUpdateClientService,
    ):
        super().__init__(logger=logger)
        self.client_query_services = client_query_services
        self.update_client_service = update_client_service

    async def __call__(self, **kwargs) -> None:
        raise NotImplementedError

    async def update_client(
        self,
        client_id: int,
        *,
        data: PersonalDetails,
    ) -> None:
        if not (client := await self.client_query_services.get_by_id(client_id=client_id)):
            raise ClientNotFound(client_id)

        if client.advisor is None:
            raise NoClientAdvisor(client_id)

        if client.client_type_id is None:
            raise NoClientType(client_id)

        if client.client_status_id is None:
            raise NoClientStatus(client_id)

        if client.client_source_id is None:
            raise NoClientSource(client_id)

        if client.first_name is None:
            raise NoClientFirstName(client_id)

        if client.last_name is None:
            raise NoClientLastName(client_id)

        schema = UpdateClientSchema(
            client_type_id=ClientType(client.client_type_id),
            title_id=client.title_id,
            first_name=client.first_name,
            last_name=client.last_name,
            email=client.email,
            no_email_reason_id=client.no_email_reason_id,
            client_status=ClientStatus(client.client_status_id),
            client_source=ClientSource(client.client_source_id),
            owner_id=client.advisor.id,
            date_of_birth=client.date_of_birth,
            phone_number=client.phone_number,
            mobile_number=client.mobile_number,
            links=[
                ClientLink(
                    client_id=link.client_id,
                    link_relationship_id=ClientLinkRelationship(link.link_relationship_id),
                )
                for link in client.links
            ],
            client_agreement_id=client.client_agreement_id,
            privacy_notice_id=client.privacy_notice_id,
        )

        for field in data.available_fields:
            if field in schema.model_fields:
                new_value = getattr(data, field)
                setattr(schema, field, new_value)

        await self.update_client_service.update_client(
            client_id=client.id,
            email=schema.email,
            no_email_reason_id=schema.no_email_reason_id,
            first_name=schema.first_name,
            last_name=schema.last_name,
            client_type=schema.client_type_id,
            owner_id=schema.owner_id,
            title_id=schema.title_id,
            client_status=schema.client_status,
            client_source=schema.client_source,
            date_of_birth=schema.date_of_birth,
            phone_number=schema.phone_number,
            mobile_number=schema.mobile_number,
            client_links=[(link.client_id, link.link_relationship_id) for link in schema.links],
            client_agreement_id=schema.client_agreement_id,
            privacy_notice_id=schema.privacy_notice_id,
        )
