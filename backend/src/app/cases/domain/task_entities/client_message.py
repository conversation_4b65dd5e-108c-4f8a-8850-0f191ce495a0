from abc import abstractproperty
from dataclasses import dataclass
from typing import Any

from app.cases.domain.events.task_events import ClientEmailSendRequested
from app.cases.domain.models.case_models import UnOrderedTask, ensure_task_in_progress
from gateway_accessors.postmark.constants import PostmarkTemplates


@dataclass
class ClientMessageTask(UnOrderedTask):
    @abstractproperty
    def subject(self) -> str:
        raise NotImplementedError

    @abstractproperty
    def body(self) -> str:
        raise NotImplementedError

    @abstractproperty
    def template_alias(self) -> str:
        raise NotImplementedError

    @abstractproperty
    def template_model(self) -> dict[str, Any]:
        raise NotImplementedError

    @ensure_task_in_progress
    def send_email(self, cc_transaction_actor: bool) -> None:
        event = ClientEmailSendRequested(
            case_id=self.case.id,
            case_goal_id=self.case_goal_id,
            task_slug=self.task_slug,
            task_id=self.id,
            actor_first_name=self.case.actor.first_name,
            actor_last_name=self.case.actor.last_name,
            actor_email=self.case.actor.email,
            to=[client.email for client in self.case.clients],
            cc=self.case.actor.email if cc_transaction_actor else None,
            subject=self.subject,
            body=self.body,
            template_alias=self.template_alias,
            template_model=self.template_model,
        )
        self.case.queue_event(event)


@dataclass
class AnnualReviewMessageTask(ClientMessageTask):
    @property
    def subject(self) -> str:
        return "Aventur Annual Review"

    @property
    def body(self) -> str:
        return (
            "Your Aventur annual review is due soon. Please contact your financial advisor "
            "for more details."
        )

    @property
    def template_alias(self) -> str:
        return PostmarkTemplates.CLIENT_ANNUAL_REVIEW

    @property
    def template_model(self) -> dict[str, Any]:
        return {}


@dataclass
class WelcomeEmailMessageTask(ClientMessageTask):
    @property
    def subject(self) -> str:
        return "Aventur Onboarding"

    @property
    def body(self) -> str:
        return "Welcome to Aventur!"

    @property
    def template_alias(self) -> str:
        return PostmarkTemplates.CLIENT_ONBOARDING

    @property
    def template_model(self) -> dict[str, Any]:
        return {
            "client_name": f"{self.case.clients[0].first_name} {self.case.clients[0].last_name}"
        }
