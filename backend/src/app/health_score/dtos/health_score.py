from dataclasses import dataclass
from typing import Self

from financial_models.health_score.client import ClientHealthScore

from app.health_score.dtos.goal_score import GoalScoreDTO


@dataclass(frozen=True, slots=True)
class HealthScoreDTO:
    overall_score: int
    goal_scores: dict[int, GoalScoreDTO]
    errors: dict[int, str]
    warnings: dict[int, dict[int, str]]

    @classmethod
    def from_client_health_score(
        cls, score: ClientHealthScore, errors: dict[int, str], warnings: dict[int, dict[int, str]]
    ) -> Self:
        return cls(
            overall_score=score.overall_score,
            goal_scores={
                goal_score.goal_id: GoalScoreDTO(
                    goal_id=goal_score.goal_id,
                    score=goal_score.score,
                    audit_results=goal_score.audit_results,
                )
                for goal_score in score.goal_scores
            },
            errors=errors,
            warnings=warnings,
        )
