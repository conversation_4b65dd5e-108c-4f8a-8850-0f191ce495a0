from collections import Counter
from decimal import Decimal
from typing import TypeAlias

from financial_models.health_score.client import Client
from financial_models.health_score.goals.interface import Goal
from financial_models.simulator.account import (
    Account,
    EstimatedFinalSalaryPension,
    FinalSalaryPension,
    MortgageAccount,
    ProtectPolicy,
)
from financial_models.simulator.enums import (
    Frequency as ModelFrequency,
)
from financial_models.simulator.rates import get_adjusted_rate
from financial_models.simulator.utils import RateManager

from api.enums import Frequency
from app.base.base_service import BaseCommandService
from app.clients.services.view_factfind import GetFactfindDataService
from app.health_score.account_factory import AccountFactory
from app.health_score.constants import (
    MEAN_ANNUAL_INFLATION_RATE,
    RISK_LEVEL_INTEREST_RATE_CONFIG,
    STANDARD_VOLATILITY,
)
from app.health_score.dtos.health_score import HealthScoreDTO
from app.health_score.exceptions import HealthScoreError
from app.health_score.goal_factory import GoalFactory
from common.logging.logger import LoggingService
from database.models import ClientGoalsModel, ClientModel
from database.query_builders.client_query_builder import ClientQueryBuilder
from database.repositories.abstract_unit_of_work import AbstractUnitOfWork
from timestream.session import TimestreamQuerySession

AccountList: TypeAlias = list[Account | MortgageAccount]
FinalSalaryPensionList: TypeAlias = list[EstimatedFinalSalaryPension | FinalSalaryPension]
PolicyList: TypeAlias = list[ProtectPolicy]
ErrorDict: TypeAlias = dict[int, str]  # goal.id: error_message
WarningDict: TypeAlias = dict[int, dict[int, str]]  # goal.id: {holding.id: warning_message}


class HealthScoreService(BaseCommandService):
    def __init__(
        self,
        uow: AbstractUnitOfWork,
        ts_query_session: TimestreamQuerySession,
        logger: LoggingService,
    ) -> None:
        super().__init__(uow=uow, logger=logger)
        self.factfind_service = GetFactfindDataService(
            uow=uow, logger=logger, ts_query_session=ts_query_session
        )
        self._setup_rate_manager()

    async def __call__(self) -> None:
        raise NotImplementedError

    def _setup_rate_manager(self) -> None:
        manager = RateManager(random_seed=42)  # Ensure consistent results
        manager.set_inflation(
            inflation_rate=float(
                get_adjusted_rate(
                    rate=Decimal(MEAN_ANNUAL_INFLATION_RATE),
                    from_frequency=ModelFrequency.ANNUALLY,
                    to_frequency=ModelFrequency.MONTHLY,
                )
            ),
            volatility=STANDARD_VOLATILITY,
            frequency=ModelFrequency.MONTHLY,
            steps=100 * 12,  # 100 years worth of inflation data
        )
        manager.set_risk_level_interest_config(RISK_LEVEL_INTEREST_RATE_CONFIG)
        self.rate_manager = manager

    async def get_health_score(self, client_id: int) -> HealthScoreDTO:
        client, errors, warnings = await self._load_client(client_id)
        result = client.calculate_score()
        return HealthScoreDTO.from_client_health_score(result, errors, warnings)

    async def _load_client(self, client_id: int) -> tuple[Client, ErrorDict, WarningDict]:
        async with self.uow:
            client = await self._fetch_client_data(client_id)
            holding_ids = self._get_holding_ids(client)
            usable_holding_ids, duplicate_warnings = self._validate_holdings(client, holding_ids)

            holding_valuations = await self._get_holding_valuations(usable_holding_ids)
            goals, errors, warnings = await self._parse_goals(
                client, holding_valuations, usable_holding_ids
            )

            self._merge_warnings(warnings, duplicate_warnings)
            return self._create_health_score_client(client, goals), errors, warnings

    async def _fetch_client_data(self, client_id: int) -> ClientModel:
        query = (
            ClientQueryBuilder()
            .where_client_id(client_id)
            .with_factfind_answers()
            .with_cashflows()
            .with_holdings()
            .with_goals()
            .build()
        )
        result = await self.uow.session.execute(query)
        return result.scalars().one()

    def _get_holding_ids(self, client: ClientModel) -> list[int]:
        # Get the holdings that are linked to the client's goals
        goal_linked_holdings = [holding.id for goal in client.goals for holding in goal.holdings]

        # Also get any property holdings as the valuations are required for the Property Goal
        property_holdings = [
            link.holding.id
            for link in client.holdings
            if link.holding.product.product_type.product_type_group_id == 8
        ]

        return goal_linked_holdings + property_holdings

    def _validate_holdings(
        self, client: ClientModel, holding_ids: list[int]
    ) -> tuple[set[int], dict[int, dict[int, str]]]:
        holding_id_counts = Counter(holding_ids)
        duplicate_warnings = {
            goal.id: {
                holding.id: (
                    "This holding has been added to more than one goal, each holding "
                    "can only be linked to a single goal."
                )
                for holding in goal.holdings
                if holding_id_counts[holding.id] > 1
            }
            for goal in client.goals
            for holding in goal.holdings
        }

        usable_holding_ids = {
            holding_id for holding_id in holding_ids if holding_id_counts[holding_id] == 1
        }
        return usable_holding_ids, duplicate_warnings

    async def _get_holding_valuations(self, usable_holding_ids: set[int]) -> dict[int, Decimal]:
        valuation_data = await self.factfind_service.get_latest_valuation_for_holdings(
            list(usable_holding_ids)
        )
        return {key: Decimal(valuation_data[key]["valuation"]) for key in valuation_data}

    def _merge_warnings(
        self, warnings: WarningDict, duplicate_warnings: dict[int, dict[int, str]]
    ) -> None:
        for goal_id, holding_warnings in duplicate_warnings.items():
            if not holding_warnings:
                continue
            if warnings.get(goal_id):
                warnings[goal_id].update(holding_warnings)
            else:
                warnings[goal_id] = holding_warnings

    def _create_health_score_client(self, client: ClientModel, goals: list[Goal]) -> Client:
        return Client(
            client_id=client.id,  # type: ignore
            goals=tuple(goals),
        )

    async def _sum_income_expenses(
        self, client: ClientModel, type_: str, output_frequency: Frequency
    ) -> Decimal:
        total = Decimal(0)
        for cashflow in client.cashflows:
            if cashflow.type == type_:
                amount = convert_amount_between_frequencies(
                    amount=Decimal(cashflow.amount),
                    from_frequency=cashflow.frequency,
                    to_frequency=output_frequency,
                )
                total += amount
        return total

    async def _get_total_debt(self, client: ClientModel) -> Decimal:
        debt_holding_ids = {link.holding.id for link in client.holdings if link.holding.is_debt}
        valuations = await self._get_holding_valuations(debt_holding_ids)
        return sum(valuations.values(), Decimal(0))

    async def _parse_goals(
        self, client: ClientModel, valuations: dict[int, Decimal], available_holding_ids: set[int]
    ) -> tuple[list[Goal], ErrorDict, WarningDict]:
        parsed_goals: list[Goal] = []
        errors: ErrorDict = {}
        warnings: WarningDict = {}

        for goal in client.goals:
            if goal.goal_id == 12:
                continue  # Skip client setup goal
            (
                linked_accounts,
                linked_final_salary_pensions,
                linked_policies,
                account_errors,
            ) = await self._parse_accounts(goal, valuations, available_holding_ids)
            if account_errors:
                warnings[goal.id] = account_errors

            factory = GoalFactory(
                client,
                linked_accounts,
                linked_final_salary_pensions,
                linked_policies,
                self.rate_manager,
                self._sum_income_expenses,
                self._get_total_debt,
                valuations,
            )
            try:
                parsed_goals.append(
                    await factory.create_goal(goal.goal_id, goal.id, goal.attributes)
                )
            except HealthScoreError as err:
                errors[goal.id] = err.message

        return parsed_goals, errors, warnings

    async def _parse_accounts(
        self,
        goal: ClientGoalsModel,
        valuations: dict[int, Decimal],
        available_holding_ids: set[int],
    ) -> tuple[AccountList, FinalSalaryPensionList, PolicyList, ErrorDict]:
        accounts: list[Account | MortgageAccount] = []
        final_salary_pensions: list[EstimatedFinalSalaryPension | FinalSalaryPension] = []
        policies: list[ProtectPolicy] = []
        errors: ErrorDict = {}

        for holding in goal.holdings:
            if holding.id not in available_holding_ids:
                continue

            try:
                account_or_policy = AccountFactory.create_account(
                    holding=holding,
                    valuations=valuations,
                    rate_manager=self.rate_manager,
                )
                if isinstance(account_or_policy, ProtectPolicy):
                    policies.append(account_or_policy)
                elif isinstance(
                    account_or_policy, (EstimatedFinalSalaryPension, FinalSalaryPension)
                ):
                    final_salary_pensions.append(account_or_policy)
                else:
                    accounts.append(account_or_policy)
            except HealthScoreError as err:
                errors[holding.id] = err.message

        return accounts, final_salary_pensions, policies, errors


def convert_amount_between_frequencies(
    amount: Decimal, from_frequency: Frequency, to_frequency: Frequency
) -> Decimal:
    to_yearly = {
        Frequency.Daily: 365,
        Frequency.Weekly: 52,
        Frequency.Fortnightly: 26,
        Frequency.Monthly: 12,
        Frequency.Quarterly: 4,
        Frequency.Yearly: 1,
    }

    # Convert to yearly first
    yearly_amount = amount * Decimal(to_yearly[from_frequency])

    # Then convert from yearly to target frequency
    return yearly_amount / Decimal(to_yearly[to_frequency])
