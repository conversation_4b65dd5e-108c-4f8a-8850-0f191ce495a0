from datetime import date

from api.enums import ClientSource, ClientStatus, ClientType
from app.base.base_service import BaseCommandService
from app.clients import exceptions
from app.clients.domain.links import reverse_link_relationship
from app.clients.services.client_access import ClientAccessService
from app.review_slots.services import review_services
from app.shared_kernel.enums.repositories import ActivityType
from common.logging.logger import LoggingService
from database import models
from database.repositories.abstract_unit_of_work import AbstractUnitOfWork


class UpdateClientService(BaseCommandService):
    async def __call__(self, **kwargs) -> None:
        raise NotImplementedError

    def __init__(
        self,
        *,
        uow: AbstractUnitOfWork,
        logger: LoggingService,
        client_access: ClientAccessService,
    ) -> None:
        super().__init__(uow=uow, logger=logger)
        self.client_access = client_access

    async def update_client(
        self,
        client_id: int,
        *,
        email: str | None = None,
        no_email_reason_id: int | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        client_type: ClientType | None = None,
        owner_id: int | None = None,
        title_id: int | None = None,
        client_status: ClientStatus | None = None,
        client_source: ClientSource | None = None,
        date_of_birth: date | None = None,
        phone_number: str | None = None,
        mobile_number: str | None = None,
        client_links: list[tuple[int, int]] | None = None,
        client_agreement_id: int | None = None,
        privacy_notice_id: int | None = None,
    ) -> None:
        if client_links is None:
            client_links = []

        async with self.uow:
            if not (
                client := await self.uow.clients.with_eager_load(
                    [models.ClientModel.links, models.ClientModel.current_review_group]
                ).get(client_id)
            ):
                raise exceptions.ClientNotFound(client_id)

            # Check if the client email has changed
            email_changed = email != client.email
            current_email = client.email

            # Check if the client status has changed - we may need to update review slots
            client_status_changed = False
            original_status = client.client_status_id
            if client_status is not None:
                client_status_changed = original_status != client_status.value  # type: ignore

            if title_id is not None and await self.uow.personal_titles.get(title_id) is None:
                raise exceptions.PersonalTitleNotFound(title_id)

            client.email = email
            client.no_email_reason_id = no_email_reason_id
            client.first_name = first_name
            client.last_name = last_name
            client.client_type_id = client_type
            client.owner_id = owner_id
            client.title_id = title_id
            client.client_status_id = client_status
            client.client_source_id = client_source
            client.date_of_birth = date_of_birth
            client.phone_number = phone_number
            client.mobile_number = mobile_number
            client.client_agreement_id = client_agreement_id
            client.privacy_notice_id = privacy_notice_id

            if self.uow.session.is_modified(client):
                await self.uow.add_activity(activity_type=ActivityType.UPDATE, obj=client)

            links_by_id = {link[0]: link[1] for link in client_links}

            for existing_link in client.links:
                linked_client_id = existing_link.linked_client_id
                linked_client = await self.uow.clients.with_eager_load(
                    [models.ClientModel.links]
                ).get(linked_client_id)

                reverse_link = next(
                    link for link in linked_client.links if link.linked_client_id == client_id
                )

                if existing_link.linked_client_id not in links_by_id.keys():
                    linked_client.links.remove(reverse_link)
                    client.links.remove(existing_link)
                    await self.uow.add_activity(
                        activity_type=ActivityType.DELETE,
                        obj=reverse_link,
                        target=linked_client,
                    )
                    await self.uow.add_activity(
                        activity_type=ActivityType.DELETE,
                        obj=existing_link,
                        target=client,
                    )
                else:
                    existing_link.link_relationship = links_by_id[existing_link.linked_client_id]
                    reverse_link.link_relationship = reverse_link_relationship(
                        links_by_id[existing_link.linked_client_id]
                    )
                    del links_by_id[existing_link.linked_client_id]

            for client_to_link_id, relationship in links_by_id.items():
                client_to_link = await self.uow.clients.with_eager_load(
                    [models.ClientModel.links]
                ).get(client_to_link_id)
                if client_to_link is None:
                    raise exceptions.ClientNotFound(client_to_link_id)

                link = models.ClientLinkModel(
                    client_id=client_id,
                    linked_client_id=client_to_link_id,
                    link_relationship=relationship,
                )
                client.links.append(link)
                await self.uow.add_activity(
                    activity_type=ActivityType.CREATE,
                    obj=link,
                    target=client,
                )

                reverse_link = models.ClientLinkModel(
                    client_id=client_to_link_id,
                    linked_client_id=client_id,
                    link_relationship=reverse_link_relationship(relationship),
                )
                client_to_link.links.append(reverse_link)
                await self.uow.add_activity(
                    activity_type=ActivityType.CREATE,
                    obj=reverse_link,
                    target=models.ClientModel(id=client_to_link_id),
                )

            await self.uow.commit()

        if client_status_changed:
            await _update_review_slots(
                client,
                self.uow,
            )

        if email_changed:
            await self.client_access.handle_email_change(
                old_email=current_email,
                new_email=email,
            )


async def _update_review_slots(
    client: models.ClientModel,
    uow: AbstractUnitOfWork,
) -> None:
    if client.current_review_group:
        async with uow:
            await review_services.refresh_calendar(client.current_review_group.id, uow)
            await uow.commit()
