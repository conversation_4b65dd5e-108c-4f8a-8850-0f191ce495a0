from datetime import date

from api.enums import CaseType, ClientSource, ClientStatus, ClientType
from app.base.base_service import BaseCommandService, commit
from app.cases.services.case_manager_service import CaseCommandService
from app.clients import exceptions
from app.clients.domain.links import reverse_link_relationship
from app.clients.exceptions import DuplicateKey
from app.shared_kernel.enums.repositories import ActivityType
from app.shared_kernel.translators.integrity import _translate_integrity_error
from common.logging.logger import LoggingService
from database import models
from database.models import ClientGoalsModel
from database.repositories.abstract_unit_of_work import AbstractUnitOfWork
from database.repositories.db_error_handling import IntegrityError


async def add_onboarding_case(
    client_id: int, owner_id: int, uow: AbstractUnitOfWork, logger: LoggingService
):
    # TODO: MAYBE Use event bus for creating onboarding case using ClientCreated event
    case_service = CaseCommandService(uow=uow, logger=logger)

    async with uow:
        client = await uow.clients.get(client_id)

        client_setup_goal = await uow.goals.find(models.GoalTypeModel.code == "CS")
        client_setup_goal_id = client_setup_goal[0].id

    async with uow as async_uow:
        client_goal = models.ClientGoalsModel(client_id=client_id, goal_id=client_setup_goal_id)
        client_goal.clients = [client]
        async_uow.session.add(client_goal)
        await uow.add_activity(
            activity_type=ActivityType.CREATE,
            obj=client_goal,
            target=models.ClientModel(id=client_id),
        )
        await uow.commit()

    await case_service.create_case(
        transaction_actor_user_id=owner_id,
        client_ids=[client_id],
        adviser_id=owner_id,
        goal_ids=[client_goal.id],
        case_type=CaseType.Onboarding,
        review_slot_id=None,
    )


class AddClientService(BaseCommandService):
    async def __call__(self, **kwargs) -> None:
        raise NotImplementedError

    async def add_client(
        self,
        *,
        email: str | None = None,
        no_email_reason_id: int | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        client_type: ClientType | None = None,
        owner_id: int | None = None,
        title_id: int | None = None,
        client_status: ClientStatus | None = None,
        client_source: ClientSource | None = None,
        date_of_birth: date | None = None,
        phone_number: str | None = None,
        mobile_number: str | None = None,
        client_links: list[tuple[int, int]] = None,
    ) -> int:
        try:
            client_id = await self._create_client_transaction(
                email=email,
                no_email_reason_id=no_email_reason_id,
                first_name=first_name,
                last_name=last_name,
                client_type=client_type,
                owner_id=owner_id,
                title_id=title_id,
                client_status=client_status,
                client_source=client_source,
                date_of_birth=date_of_birth,
                phone_number=phone_number,
                mobile_number=mobile_number,
                client_links=client_links,
            )
        except IntegrityError as e:
            raise DuplicateKey(_translate_integrity_error(e))

        await add_onboarding_case(
            client_id=client_id, owner_id=owner_id, uow=self.uow, logger=self.logger
        )

        return client_id

    @commit
    async def _create_client_transaction(
        self,
        *,
        email: str | None = None,
        no_email_reason_id: int | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        client_type: ClientType | None = None,
        owner_id: int | None = None,
        title_id: int | None = None,
        client_status: ClientStatus | None = None,
        client_source: ClientSource | None = None,
        date_of_birth: date | None = None,
        phone_number: str | None = None,
        mobile_number: str | None = None,
        client_links: list[tuple[int, int]] = None,
    ) -> int:
        if title_id is not None and await self.uow.personal_titles.get(title_id) is None:
            raise exceptions.PersonalTitleNotFound(title_id)

        default_goals = [
            ClientGoalsModel(
                goal_id=goal.id,
                custom_name=goal.name,
                goal_objectives=None,  # noqa
            )
            for goal in await self.uow.goals.list()
            if goal.is_default
        ]

        client = models.ClientModel(
            client_type_id=client_type,
            owner_id=owner_id,
            title_id=title_id,
            date_of_birth=date_of_birth,
            phone_number=phone_number,
            mobile_number=mobile_number,
            client_source_id=client_source,
            client_status_id=client_status,
            email=email,
            no_email_reason_id=no_email_reason_id,
            first_name=first_name,
            last_name=last_name,
            links=[],
            goals=default_goals,
        )

        client.factfind = models.ClientFactfindModel()
        await self.uow.clients.add(client)
        await self.uow.add_activity(activity_type=ActivityType.CREATE, obj=client)

        for client_to_link_id, relationship in client_links:
            client_to_link = await self.uow.clients.with_eager_load([models.ClientModel.links]).get(
                client_to_link_id
            )
            if client_to_link is None:
                raise exceptions.ClientNotFound(client_to_link_id)

            for link in client.links:
                if link.linked_client_id == client_to_link_id:
                    raise exceptions.MultipleLinkError(client_to_link_id)

            link = models.ClientLinkModel(
                client_id=client.id,
                linked_client_id=client_to_link_id,
                link_relationship=relationship,
            )
            client.links.append(link)
            await self.uow.add_activity(activity_type=ActivityType.CREATE, obj=link, target=client)

            reverse_link = models.ClientLinkModel(
                client_id=client_to_link_id,
                linked_client_id=client.id,
                link_relationship=reverse_link_relationship(relationship),
            )
            client_to_link.links.append(reverse_link)
            await self.uow.add_activity(
                activity_type=ActivityType.CREATE,
                obj=reverse_link,
                target=models.ClientModel(id=client_to_link_id),
            )

        return client.id

    async def signup_client(
        self,
        *,
        email: str,
        first_name: str,
        last_name: str,
        cognito_id: str | None = None,
    ) -> int:
        try:
            return await self._signup_client_transaction(
                email=email, cognito_id=cognito_id, first_name=first_name, last_name=last_name
            )
        except IntegrityError as e:
            raise DuplicateKey(_translate_integrity_error(e))

    @commit
    async def _signup_client_transaction(
        self, *, email: str, cognito_id: str, first_name: str, last_name: str
    ) -> int:
        client = models.ClientModel(
            email=email,
            first_name=first_name,
            last_name=last_name,
            cognito_id=cognito_id,
            client_status_id=ClientStatus.LeadLive,
            client_source_id=ClientSource.DirectSignup,
        )

        client.factfind = models.ClientFactfindModel()
        await self.uow.clients.add(client)
        return client.id
