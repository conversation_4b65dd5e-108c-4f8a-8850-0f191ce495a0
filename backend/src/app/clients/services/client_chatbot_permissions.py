from api.enums import Use<PERSON><PERSON><PERSON>
from app.base.base_service import Base<PERSON>ommandService, BaseService, commit
from common.logging.logger import LoggingService
from database.repositories.abstract_unit_of_work import AbstractUnitOfWork
from database.repositories.clients.client_repository import AbstractClientRepository
from gateway_accessors.cognito.accessor import CognitoGateway


class ClientChatbotPermissionsQueryService(BaseService):
    def __init__(
        self,
        *,
        logger: LoggingService,
        client_repository: AbstractClientRepository,
    ) -> None:
        super().__init__(logger=logger)
        self.client_repository = client_repository

    async def get_chatbot_consent_status(self, client_id: int) -> bool:
        client = await self.client_repository.get_by_id(client_id)
        return client.chatbot_consent_granted if client else False


class ClientChatbotPermissionsService(BaseCommandService):
    def __init__(
        self,
        *,
        uow: AbstractUnitOfWork,
        logger: LoggingService,
        cognito_gateway: CognitoGateway,
    ) -> None:
        super().__init__(uow=uow, logger=logger)
        self.cognito_gateway = cognito_gateway

    @commit
    async def grant_chatbot_consent(self, client_id: int) -> None:
        client = await self.uow.clients2.get_by_id(client_id)
        if not client or not client.email:
            return

        # Update database consent status
        await self.uow.clients2.grant_chatbot_consent(client_id)

        # Add ChatbotUser role to Cognito
        current_groups = self.cognito_gateway.get_user_groups(client.email)
        new_groups = list(set(current_groups + [UserRole.ChatbotUser.value]))
        self.cognito_gateway.sync_user_groups(client.email, new_groups)

    @commit
    async def withdraw_chatbot_consent(self, client_id: int) -> None:
        client = await self.uow.clients2.get_by_id(client_id)
        if not client or not client.email:
            return

        # Update database consent status
        await self.uow.clients2.withdraw_chatbot_consent(client_id)

        # Remove ChatbotUser role from Cognito
        current_groups = self.cognito_gateway.get_user_groups(client.email)
        new_groups = [group for group in current_groups if group != UserRole.ChatbotUser.value]
        self.cognito_gateway.sync_user_groups(client.email, new_groups)
