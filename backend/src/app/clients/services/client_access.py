from collections.abc import Awaitable
from typing import Callable

import botocore.exceptions
from email_validator import EmailNotValidError, validate_email
from faker import Faker
from jinja2 import Environment, PackageLoader

from api.enums import ClientStatus, UserRole
from api.exceptions.base_exceptions import BadRequestServiceException
from app.base.base_service import BaseCommandService, commit
from app.clients import exceptions
from app.shared_kernel.dtos.client_dtos import ClientDetailDTO
from app.users.services.user_service import CognitoUserData
from common.logging.logger import LoggingService
from database.repositories.abstract_unit_of_work import AbstractUnitOfWork
from gateway_accessors.base_mailer import BaseMailer
from gateway_accessors.cognito.accessor import CognitoGateway
from gateway_accessors.cognito.exceptions import CognitoException
from gateway_accessors.postmark.accessor import get_common_template_model
from gateway_accessors.postmark.constants import PostmarkTemplates

jinja_env = Environment(loader=PackageLoader("app", "clients/email_templates"))


def raise_for_inactive_status(client: ClientDetailDTO):
    if client.client_status_id not in ClientStatus.active_statuses():
        raise exceptions.InactiveClient(client.id)


def raise_for_invalid_email(client: ClientDetailDTO):
    try:
        validate_email(client.email if client.email else "", check_deliverability=True)
    except EmailNotValidError:
        raise exceptions.InvalidEmail(client.id)


class ClientAccessService(BaseCommandService):
    def __init__(
        self,
        *,
        uow: AbstractUnitOfWork,
        logger: LoggingService,
        cognito: CognitoGateway,
        sync_cognito_command: Callable[..., Awaitable[CognitoUserData]],
        mailer: BaseMailer,
    ):
        super().__init__(uow=uow, logger=logger)
        self.cognito = cognito
        self.mailer = mailer
        self.sync_cognito_command = sync_cognito_command

    async def __call__(self, *, client_data: ClientDetailDTO, status: bool, **kwargs) -> None:
        self.logger.info(f"Setting client access for {client_data=}")

        if status:
            raise_for_inactive_status(client_data)
            raise_for_invalid_email(client_data)
            await self._enable_client_access(email=client_data.email)
        else:
            await self._disable_client_access(email=client_data.email)

    @staticmethod
    def generate_temporary_password() -> str:
        return (Faker()).password(
            length=12,
            special_chars=True,
            digits=True,
            upper_case=True,
            lower_case=True,
        )

    def _send_client_access_invite(self, email: str, **template_model_args):
        template_model = {
            "username": email,
            **get_common_template_model(),
            **template_model_args,
        }
        email_template = jinja_env.get_template("welcome.html.jinja")
        email_body = email_template.render(template_model).replace("\n", "")
        self.mailer.send_email(
            email,
            "Your Aventur account details",
            email_body,
            template_alias=PostmarkTemplates.CLIENT_ACCESS,
            template_model=template_model,
        )

    @commit
    async def _enable_client_access(self, *, email: str) -> None:
        temporary_password = self.generate_temporary_password()

        try:
            if not (cognito_user := self.cognito.get_cognito_user(email=email)):
                user = await self.uow.users.get_by_email(email=email)
                user.cognito_id = self.cognito.create_cognito_user(
                    email=email,
                    roles=[UserRole.Client],
                    temporary_password=temporary_password,
                )
            else:
                if not cognito_user["Enabled"]:
                    await self.sync_cognito_command(email=email)

                self.cognito.set_user_password(
                    email=email,
                    password=temporary_password,
                )
                self.cognito.enable_cognito_user(email=email)

            self._send_client_access_invite(
                email=email,
                password=temporary_password,
            )
        except CognitoException as e:
            raise BadRequestServiceException(str(e))
        except botocore.exceptions.ClientError as e:
            raise BadRequestServiceException(e.response["Error"]["Message"])

    @commit
    async def _disable_client_access(self, *, email: str, delete_user: bool = False) -> None:
        try:
            cognito_user = self.cognito.get_cognito_user(email=email)
            if delete_user:
                self.cognito.delete_cognito_user(email=email)

                user = await self.uow.users.get_by_cognito_id(
                    cognito_id=cognito_user.get("Username")
                )
                if user:
                    user.cognito_id = None
            else:
                self.cognito.disable_cognito_user(email=email)
        except CognitoException as e:
            raise BadRequestServiceException(str(e))
        except botocore.exceptions.ClientError as e:
            raise BadRequestServiceException(e.response["Error"]["Message"])

    async def handle_email_change(self, *, old_email: str | None, new_email: str | None) -> None:
        """Handles client email changes with 3 scenarios:
        1. Client's email has been changed
            - update Cognito email attribute if user exists
            - send invite with a new password if client access was previously enabled
        2. Client's email has been removed
            - delete Cognito user (silently ignore Cognito exception if user doesn't exist)
            - set database user `cognito_id` field to Null
        3. Client's email has been added
            - no action required

        """

        if old_email:
            # email changed
            if new_email:
                if cognito_user := self.cognito.get_cognito_user(email=old_email):
                    access_enabled = cognito_user["Enabled"]
                    self.cognito.update_user_email(current_email=old_email, new_email=new_email)

                    if access_enabled:
                        await self._enable_client_access(email=new_email)
            # email removed
            else:
                try:
                    await self._disable_client_access(email=old_email, delete_user=True)
                except BadRequestServiceException:
                    pass
