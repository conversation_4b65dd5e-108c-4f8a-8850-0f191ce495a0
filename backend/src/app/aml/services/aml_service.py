from datetime import date
from time import sleep
from typing import Any
from uuid import UUID

from api.enums import IdentityCheckResult
from api.exceptions.base_exceptions import ExternalServiceException
from api.exceptions.client_exceptions import (
    ClientDataIncompleteError,
    ClientNotFoundError,
    ClientNotRegisteredForMonitoringError,
)
from app.base.base_service import BaseCommandService, BaseService
from app.shared_kernel.dtos.client_dtos import AMLSearchResultDTO, ClientDetailDTO
from common.logging.logger import LoggingService
from database.repositories.abstract_unit_of_work import AbstractUnitOfWork
from database.repositories.aml.aml_monitoring_dtos import (
    AMLMonitoringDTO,
    AMLMonitoringMatchDTO,
    MatchStatus,
)
from database.repositories.aml.aml_monitoring_repository import AbstractAMLMonitoringRepository
from database.repositories.aml.aml_search_dtos import AddAMLSearchResultDTO, AMLSearchCriteriaDTO
from database.repositories.aml.aml_search_repository import AbstractAMLSearchRepository
from gateway_accessors.exceptions import ExternalAPIException
from gateway_accessors.zignsec.accessor import ZignSecGateway
from gateway_accessors.zignsec.models import (
    AMLSetupMonitoringZignsecRequest,
    AMLValidateIdentityZignsecRequest,
)


class AMLCommandService(BaseCommandService):
    async def __call__(self, *args, **kwargs) -> Any:
        raise NotImplementedError

    def __init__(
        self, *, logger: LoggingService, uow: AbstractUnitOfWork, zignsec_accessor: ZignSecGateway
    ) -> None:
        super().__init__(logger=logger, uow=uow)
        self.zignsec_accessor = zignsec_accessor

    async def _validate_client_identity(self, client: ClientDetailDTO) -> IdentityCheckResult:
        """Return the result of an identity validation for the specified client.

        The client's identity is validated using the ZignSec API.

        NOTE:
            - The Client must have at least one address setup.
            - The raw validation result from ZignSec is stored as JSON in the DB but is not
                used beyond extracting the overall result.

        Args:
            client_id: The ID of the client to validate.
            request_data: The client attributes necessary for identity validation.

        Returns:
            The monitoring list data from ZignSec.

        Raises:
            `ClientNotFoundError`: If the `client_id` does not correspond with a `Client`.
            `ExternalServiceException`: If the ZignSec API returns an error.

        """
        try:
            identity_request_data = AMLValidateIdentityZignsecRequest(
                first_name=client.first_name,
                last_name=client.last_name,
                country_code=client.primary_address.country.iso_alpha2,
                address=client.primary_address.address_line_one,
                city=client.primary_address.city,
                postcode=client.primary_address.post_code,
                date_of_birth=client.date_of_birth,
            )
            validation_response = await self.zignsec_accessor.validate_identity(
                identity_request_data
            )
        except ExternalAPIException as err:
            raise ExternalServiceException(str(err))

        async with self.uow:
            search_result = AddAMLSearchResultDTO(
                client_id=client.id,
                search_criteria=AMLSearchCriteriaDTO(
                    first_name=client.first_name,
                    last_name=client.last_name,
                    country_code=client.primary_address.country.iso_alpha2,
                    address=client.primary_address.address_line_one,
                    city=client.primary_address.city,
                    post_code=client.primary_address.post_code,
                    date_of_birth=client.date_of_birth,
                ),
                found=validation_response.was_found,
                match_level=validation_response.match_level,
                full_results=validation_response.model_dump(),
            )

            await self.uow.aml_searches2.add_search_result(search_result)

            await self.uow.commit()

        return validation_response.identity_check_result

    async def _add_client_to_monitoring_list(self, client: ClientDetailDTO) -> None:
        """Register the client for PEP, sanction and adverse media monitoring with ZignSec.

        ZignSec return a unique ID that can be used to fetch the monitoring results at
        any time (see `get_client_monitoring_detail`). This ID is saved in the DB.

        Args:
            client_id: The ID of the client to lookup.
            request_data: The client attributes necessary for AML monitoring.
            uow: The unit of work for accessing the database.

        Returns:
            None.

        Raises:
            `ExternalServiceException`: If the ZignSec API returns an error.

        """
        monitoring_request_data = AMLSetupMonitoringZignsecRequest(
            client_id=client.id,
            first_name=client.first_name,
            last_name=client.last_name,
            gender=client.gender,
            date_of_birth=client.date_of_birth,
            country_code=client.primary_address.country.iso_alpha2,
        )

        async with self.uow:
            # Don't setup a new session if the client already has client.id
            if await self.uow.aml_monitoring_sessions2.get_by_client_id(client.id) is not None:
                return None

            try:
                zignsec_response = await self.zignsec_accessor.add_person_to_monitoring(
                    monitoring_request_data
                )
            except ExternalAPIException as err:
                raise ExternalServiceException(str(err))

            add_monitoring_dto = AMLMonitoringDTO(
                client_id=client.id,
                zignsec_session_id=zignsec_response.id,
                created_datetime=zignsec_response.created,
                matches=[
                    AMLMonitoringMatchDTO(
                        match_id=match.match_id,
                        first_name=match.first_name,
                        last_name=match.last_name,
                        roles=[role.title for role in match.match.roles],
                        categories=match.match.categories,
                        country_code=match.country_code,
                        date_of_birth=date.today(),
                        match_status=MatchStatus(
                            zignsec_response.result.due_diligence.get_latest_decision_by_match_id(
                                match.match_id
                            ).decision
                        ),
                    )
                    for match in zignsec_response.result.matches
                ],
                raw_data=zignsec_response.model_dump(),
            )
            await self.uow.aml_monitoring_sessions2.add_monitoring_session(add_monitoring_dto)
            await self.uow.commit()

    async def client_identity_check(self, client_id: int) -> IdentityCheckResult:
        """Wrapper for validating identity and establishing an AML monitoring session.

        Args:
            client_id: The ID of the client to validate and monitor.

        Raises:
            `ClientNotFoundError`: If the `client_id` does not correspond with a `Client`.

        """
        async with self.uow:
            if not (client := await self.uow.clients2.get_by_id(client_id)):
                raise ClientNotFoundError(client_id)

            if not client.ready_for_aml:
                raise ClientDataIncompleteError(
                    f"Incomplete profile or address details (Client ID: {client_id})"
                )

        validation_result = await self._validate_client_identity(client)

        # Setup monitoring session
        if validation_result != IdentityCheckResult.FAIL:
            await self._add_client_to_monitoring_list(client)

        return validation_result

    async def is_aml_ready(self, client_id: int) -> bool:
        async with self.uow:
            if not (client := await self.uow.clients2.get_by_id(client_id)):
                raise ClientNotFoundError(client_id)

            if not client.ready_for_aml:
                raise ClientDataIncompleteError(
                    f"Incomplete profile or address details (Client ID: {client_id})"
                )
        return True

    async def remove_from_monitoring(self, client_id: int):
        async with self.uow:
            if (
                session := await self.uow.aml_monitoring_sessions2.get_by_client_id(client_id)
            ) is None:
                return None

            await self.zignsec_accessor.remove_from_monitoring(session.zignsec_session_id)

            await self.uow.aml_monitoring_sessions2.delete_monitoring_session(client_id)
            await self.uow.commit()
        return True

    async def update_match_status(
        self, session_id: UUID, match_id: UUID, match_status: MatchStatus
    ):
        async with self.uow:
            if (
                await self.uow.aml_monitoring_sessions2.update_match_status(match_id, match_status)
                == 0
            ):
                raise ValueError(f"No match found for id {match_id}")
            await self.uow.commit()

            await self.zignsec_accessor.update_match_status(session_id, match_id, match_status)
        return True

    async def update_watchlist_data(self, aml_data: AMLMonitoringDTO):
        async with self.uow:
            await self.uow.aml_monitoring_sessions2.update_monitoring_session(aml_data)
            await self.uow.commit()

    async def bulk_update_aml_monitoring(self):
        async with self.uow:
            clients = await self.uow.clients2.get_clients_missing_aml_monitoring()
            client_ids = [client.id for client in clients]

            self.logger.info(f"Clients founds: {len(client_ids)}")
            for client_id in client_ids:
                check_result = await self.client_identity_check(client_id)
                self.logger.info(f"{client_id}: {check_result}")
                sleep(0.5)


class AMLQueryService(BaseService):
    async def __call__(self, *args, **kwargs) -> Any:
        raise NotImplementedError

    def __init__(
        self,
        *,
        logger: LoggingService,
        aml_search_repo: AbstractAMLSearchRepository,
        aml_monitoring_repo: AbstractAMLMonitoringRepository,
        zignsec_accessor: ZignSecGateway,
    ) -> None:
        super().__init__(logger=logger)
        self.aml_search_repo = aml_search_repo
        self.aml_monitoring_repo = aml_monitoring_repo
        self.zignsec_accessor = zignsec_accessor

    async def get_latest_search_by_client_id(self, client_id: int) -> AMLSearchResultDTO:
        return await self.aml_search_repo.get_latest_by_client_id(client_id)

    async def get_client_monitoring_detail(self, client_id: int):
        if not (session := await self.aml_monitoring_repo.get_by_client_id(client_id)):
            err_msg = f"Client '{client_id}' has not been added to AML monitoring list"
            raise ClientNotRegisteredForMonitoringError(err_msg)

        try:
            result = await self.zignsec_accessor.get_monitoring_detail(session.zignsec_session_id)
        except ExternalAPIException as err:
            raise ExternalServiceException(str(err))

        return {
            "id": result.id,
            "status": result.status,
            "matches": [
                {
                    "id": match.match_id,
                    "image": match.match.image,
                    "first_name": match.first_name,
                    "last_name": match.last_name,
                    "categories": match.match.categories,
                    "roles": match.match.roles,
                    "match_status": result.result.due_diligence.get_latest_decision_by_match_id(
                        match.match_id
                    ).decision,
                }
                for match in result.result.matches
            ],
        }
