from dataclasses import dataclass

import boto3
from jinja2 import Environment, PackageLoader
from mypy_boto3_cognito_idp.type_defs import ListUsersResponseTypeDef, UserTypeTypeDef

from api.enums import ClientStatus, UserRole
from app.base.base_service import BaseService
from app.shared_kernel.app_session import AppSession
from app.shared_kernel.injection import get_uow
from app.users.entities.user_entity import User
from common.config.settings import get_settings
from common.logging.logger import LoggingService
from database.models import ClientFactfindModel, ClientModel, OwnerModel, UserModel
from database.repositories import AbstractUserRepository
from database.repositories.unit_of_work import AbstractUnitOfWork

settings = get_settings()

jinja_env = Environment(loader=PackageLoader("app", "clients/email_templates"))


@dataclass(frozen=True)
class CognitoUserData:
    cognito_id: str
    email: str
    roles: list[UserRole]


def get_aws_client():
    return boto3.client(
        "cognito-idp",
        region_name=settings.aws_region,
        endpoint_url=settings.aws_cognito_endpoint_url,
        aws_access_key_id=settings.aws_access_key_id,
        aws_secret_access_key=settings.aws_secret_access_key,
    )


async def create_client(
    uow: AbstractUnitOfWork,
    username: str,
    email: str,
    **kwargs,
) -> UserModel:
    factfind = ClientFactfindModel()
    client = ClientModel(
        cognito_id=username,
        email=email,
        factfind=factfind,
        client_status_id=kwargs.get("client_status_id", ClientStatus.LeadLive),
    )
    await uow.users.add(client)
    return client


async def create_owner(
    uow: AbstractUnitOfWork,
    username: str,
    email: str,
    roles: list[UserRole],
    first_name: str | None = None,
    last_name: str | None = None,
) -> UserModel:
    owner = OwnerModel(
        cognito_id=username,
        email=email,
        first_name=first_name,
        last_name=last_name,
        roles=roles,
    )
    await uow.owners.add(owner)
    return owner


async def sync_cognito_user(email: str) -> CognitoUserData:
    aws_client = get_aws_client()

    def extract_cognito_id(user_: UserTypeTypeDef) -> str | None:
        for attr in user_["Attributes"]:
            if attr.get("Name") == "sub":
                return attr.get("Value")
        return None

    users: ListUsersResponseTypeDef = aws_client.list_users(
        UserPoolId=settings.aws_user_pool_id,
        AttributesToGet=["email", "sub"],
        Filter=f'email = "{email}"',
        Limit=1,
    )
    if users["Users"]:
        cognito_id = extract_cognito_id(users["Users"][0])

        groups_response = aws_client.admin_list_groups_for_user(
            UserPoolId=settings.aws_user_pool_id, Username=email
        )
        roles = [UserRole(group["GroupName"]) for group in groups_response["Groups"]]

        async with get_uow() as uow:
            if not (user := await uow.users.get_by_email(email=email)):
                if roles[0] == UserRole.Client:
                    await create_client(uow, cognito_id, email)
                else:
                    await create_owner(
                        uow,
                        cognito_id,
                        email=email,
                        roles=roles,
                        first_name=email,
                    )
            else:
                if user.cognito_id:
                    raise Exception("User already exists")
                user.cognito_id = cognito_id
            await uow.session.commit()
        return CognitoUserData(cognito_id=cognito_id, email=email, roles=roles)
    else:
        raise Exception("Email address not found in Cognito")


def get_user_list():
    aws_client = get_aws_client()

    response = aws_client.list_users(
        UserPoolId=settings.aws_user_pool_id,
    )

    return response


async def get_user(email):
    async with get_uow() as uow:
        user = await uow.users.get_by_email(email)

    aws_client = get_aws_client()

    response = aws_client.admin_get_user(
        UserPoolId=settings.aws_user_pool_id, Username=user.cognito_id
    )

    return response


def disable_user(user_id):
    aws_client = get_aws_client()

    response = aws_client.admin_disable_user(UserPoolId=settings.aws_user_pool_id, Username=user_id)

    return response


class FindUserService(BaseService):
    async def __call__(self, **kwargs) -> None:
        raise NotImplementedError

    def __init__(
        self,
        *,
        logger: LoggingService,
        user_repository: AbstractUserRepository,
    ):
        super().__init__(logger=logger)
        self.user_repo = user_repository

    async def find_by_cognito_id(self, cognito_id: str) -> UserModel | None:
        return await self.user_repo.get_by_cognito_id(cognito_id)

    async def find_by_email(self, email: str) -> UserModel | None:
        return await self.user_repo.get_by_email(email)


async def get_user_id_from_session(app_session: AppSession) -> int | None:
    return app_session.actor_id


async def get_user_entity_from_session(app_session: AppSession) -> User | None:
    return app_session.user
