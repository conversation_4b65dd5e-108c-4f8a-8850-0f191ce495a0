from typing import Any, Set

from app.base.base_service import BaseService
from app.chatbot_operations.dtos.operations_responses import Company
from common.logging.logger import LoggingService


class CompanyDataService(BaseService):
    def __init__(
        self,
        *,
        logger: LoggingService,
    ):
        super().__init__(logger=logger)

    async def __call__(self, *args, **kwargs) -> Any:
        raise NotImplementedError

    async def get_company_fields_by_id(self, *, fields: Set[str]) -> dict[str, Any]:
        company = Company(
            email="<EMAIL>",
            phone_number="020 3740 1113",
            address="Broadgate Tower, 20 Primrose St, London, EC2A 2EW",
            website="https://www.aventur.co.uk/",
            documentation_address="Admin Centre, Aventur, PO Box 13267, Colchester, CO1 9YA",
        )

        return {field: value for field, value in company.__dict__.items() if field in fields}
