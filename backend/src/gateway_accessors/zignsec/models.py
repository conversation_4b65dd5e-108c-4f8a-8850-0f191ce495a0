from __future__ import annotations

from dataclasses import dataclass
from datetime import date, datetime
from functools import cached_property
from uuid import UUID

from pydantic import BaseModel, Field

from api.enums import IdentityCheckResult, IdentityMatchLevel
from database.repositories.aml.aml_monitoring_dtos import MatchStatus


class ValidateIdentityZignsecResponse(BaseModel):
    """Model for the ZignSec response to `POST zignsec.com/v2/ekyc/validateIdentity`."""

    id: UUID
    detail_code: str = Field(..., alias="DetailCode")
    match_level: IdentityMatchLevel = Field(..., alias="MatchLevel")
    was_found: bool = Field(..., alias="WasFound")
    validation_details: dict[str, str] = Field(..., alias="ValidationDetails")
    verified_age: int | None = Field(alias="VerifiedAge", default=None)

    @property
    def identity_check_result(self) -> IdentityCheckResult:
        if not self.was_found or self.match_level == IdentityMatchLevel.Low:
            return IdentityCheckResult.FAIL
        elif self.match_level == IdentityMatchLevel.Medium:
            return IdentityCheckResult.REFER
        else:
            return IdentityCheckResult.PASS


@dataclass
class AMLValidateIdentityZignsecRequest:
    first_name: str
    last_name: str
    country_code: str
    address: str  # This is first address line only
    postcode: str
    city: str | None = None
    date_of_birth: date | None = None

    @cached_property
    def zignsec_formatted_dict(self) -> dict[str, str]:
        data = {
            "FirstName": self.first_name,
            "LastName": self.last_name,
            "CountryCode": self.country_code,
            "Address": self.address,
            "PostalCode": self.postcode,
        }
        if self.city:
            data["City"] = self.city
        if self.date_of_birth:
            data["DateOfBirth"] = self.date_of_birth.strftime("%Y-%m-%d")
        return data


@dataclass
class AMLSetupMonitoringZignsecRequest:
    client_id: int
    first_name: str
    last_name: str
    gender: str | None
    date_of_birth: date
    country_code: str | None

    @cached_property
    def zignsec_formatted_dict(self) -> dict[str, str | int]:
        data = {
            "gdpr_user_id": str(self.client_id),
            "relay_state": str(self.client_id),
            "metadata": {
                "firstName": self.first_name,
                "lastName": self.last_name,
                "dateOfBirth": self.date_of_birth.strftime("%Y-%m-%d"),
                "countryCode": self.country_code,
                "gender": self.gender,
            },
        }

        return data  # type: ignore


class MonitoringSessionData(BaseModel):
    action_id: UUID
    id: UUID
    product_category: str
    product_name: str
    provider_action_id: str
    provider_id: UUID
    start_time: datetime


class MonitoringSessionMetadata(BaseModel):
    page_number: int
    page_size: int
    total_entries: int
    total_pages: int


class MonitoringSessionListResponse(BaseModel):
    """Model for the ZignSec response to `GET zignsec.com/core/api/sessions`."""

    data: list[MonitoringSessionData]
    metadata: MonitoringSessionMetadata


class Role(BaseModel):
    title: str
    status: str
    country: str


class MonitoringMatchResultResponse(BaseModel):
    data_source: str = Field(alias="dataSource")
    categories: list[str]
    image: str | None = None
    roles: list[Role] = Field(default_factory=list)


class MonitoringMatchesResultResponse(BaseModel):
    match_id: UUID = Field(alias="matchId")
    match: MonitoringMatchResultResponse
    first_name: str = Field(alias="firstName")
    last_name: str = Field(alias="lastName")
    gender: str | None = Field(alias="gender", default=None)
    country_code: str = Field(alias="countryCode")
    date_of_birth: date | None = Field(alias="dateOfBirth", default=None)
    status: str


class Decision(BaseModel):
    decision: int
    risk: MatchStatus
    date: datetime


class MatchDecisionHistory(BaseModel):
    match_id: UUID = Field(..., alias="matchId")
    history: list[Decision]

    @property
    def latest_decision(self) -> Decision | None:
        if not self.history:
            return None
        return max(self.history, key=lambda x: x.date)


class DueDiligence(BaseModel):
    decisions: list[Decision]
    matches_decision_history: list[MatchDecisionHistory] = Field(
        ..., alias="matchesDecisionHistory"
    )

    def get_latest_decision_by_match_id(self, match_id: UUID) -> Decision | None:
        for match_history in self.matches_decision_history:
            if match_history.match_id == match_id:
                return match_history.latest_decision
        return None


class MonitoringResultResponse(BaseModel):
    matches: list[MonitoringMatchesResultResponse]
    due_diligence: DueDiligence = Field(alias="dueDiligence")


class MonitoringSessionResponse(BaseModel):
    """Model for the ZignSec response to:

    `POST /core/api/sessions/legacy/identity_check_with_monitoring/membercheck`

    NOTE: we are only interested in the UUID returned for the time being. Using this ID
        we can GET the session details at any time (see
        `accessor.get_monitoring_detail`).

    """

    id: UUID
    created: datetime = Field(alias="lastUpdated")
    status: str
    result: MonitoringResultResponse
