import boto3
from botocore.exceptions import ClientError
from mypy_boto3_cognito_idp.client import CognitoIdentityProviderClient
from mypy_boto3_cognito_idp.type_defs import AdminGetUserResponseTypeDef

from api.enums import UserRole
from gateway_accessors.cognito.exceptions import (
    CognitoEmailAlreadyInUse,
    CognitoInvalidGroups,
)


class CognitoGateway:
    def __init__(
        self,
        aws_region: str,
        aws_access_key_id: str,
        aws_secret_access_key: str,
        aws_user_pool_id: str,
        aws_endpoint_url: str | None,
        aws_send_email_on_user_creation: bool,
    ):
        self.aws_user_pool_id = aws_user_pool_id
        self.aws_send_email_on_user_creation = aws_send_email_on_user_creation
        self.aws_client: CognitoIdentityProviderClient = boto3.client(
            "cognito-idp",
            region_name=aws_region,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            endpoint_url=aws_endpoint_url,
        )

    def get_cognito_user(self, email: str) -> AdminGetUserResponseTypeDef | None:
        try:
            user = self.aws_client.admin_get_user(
                UserPoolId=self.aws_user_pool_id,
                Username=email,
            )
        except ClientError as error:
            if error.response["Error"]["Code"] == "UserNotFoundException":
                return None
            raise error
        return user

    def create_cognito_user(
        self,
        email: str,
        roles: list[UserRole],
        temporary_password: str | None = None,
        suppress_invitation_email: bool = True,
    ) -> str:
        extra_kwargs = {}
        if suppress_invitation_email or not self.aws_send_email_on_user_creation:
            extra_kwargs["MessageAction"] = "SUPPRESS"
        if temporary_password:
            extra_kwargs["TemporaryPassword"] = temporary_password
        try:
            response = self.aws_client.admin_create_user(
                UserPoolId=self.aws_user_pool_id,
                Username=email,
                UserAttributes=[  # noqa
                    {"Name": "email", "Value": email},
                    {"Name": "email_verified", "Value": "true"},
                ],
                DesiredDeliveryMediums=["EMAIL"],
                **extra_kwargs,
            )
        except ClientError as error:
            if error.response["Error"]["Code"] == "UsernameExistsException":
                raise CognitoEmailAlreadyInUse(email)
            raise error

        self.sync_user_groups(email=email, groups=roles)

        cognito_id = {
            attr["Name"]: attr["Value"]
            for attr in response["User"]["Attributes"]
            if attr["Name"] == "sub"
        }.get("sub")

        return cognito_id

    def enable_cognito_user(self, email: str):
        self.aws_client.admin_enable_user(
            UserPoolId=self.aws_user_pool_id,
            Username=email,
        )

    def disable_cognito_user(self, email: str):
        self.aws_client.admin_disable_user(
            UserPoolId=self.aws_user_pool_id,
            Username=email,
        )

    def delete_cognito_user(self, email: str):
        self.aws_client.admin_delete_user(
            UserPoolId=self.aws_user_pool_id,
            Username=email,
        )

    def get_user_groups(self, email: str) -> list[str]:
        current_groups_response = self.aws_client.admin_list_groups_for_user(
            UserPoolId=self.aws_user_pool_id,
            Username=email,
        )
        return [group["GroupName"] for group in current_groups_response["Groups"]]

    def get_cognito_groups(self) -> list[str]:
        response = self.aws_client.list_groups(UserPoolId=self.aws_user_pool_id)
        groups = [group["GroupName"] for group in response["Groups"]]
        return groups

    def assert_roles_are_valid(self, groups: list[UserRole] | list[str]):
        cognito_groups = self.get_cognito_groups()
        missing_groups = []
        for group in groups:
            group = str(group)
            if group not in cognito_groups:
                missing_groups.append(group)
        if missing_groups:
            raise CognitoInvalidGroups(missing_groups)

    def sync_user_groups(self, email: str, groups: list[UserRole] | list[str]):
        self.assert_roles_are_valid(groups)
        current_groups = set(self.get_user_groups(email))

        groups_update = {str(group) for group in groups}
        groups_to_remove_user_from = current_groups - groups_update
        groups_to_add_user_to = groups_update - current_groups

        for group in groups_to_remove_user_from:
            self.aws_client.admin_remove_user_from_group(
                UserPoolId=self.aws_user_pool_id,
                Username=email,
                GroupName=group,
            )

        for group in groups_to_add_user_to:
            self.aws_client.admin_add_user_to_group(
                UserPoolId=self.aws_user_pool_id,
                Username=email,
                GroupName=group,
            )

    def set_user_password(self, email, *, password: str):
        self.aws_client.admin_set_user_password(
            UserPoolId=self.aws_user_pool_id,
            Username=email,
            Password=password,
            Permanent=False,
        )

    def update_user_email(self, current_email: str, new_email: str):
        self.aws_client.admin_update_user_attributes(
            UserPoolId=self.aws_user_pool_id,
            Username=current_email,
            UserAttributes=[  # noqa
                {"Name": "email", "Value": new_email},
                {"Name": "email_verified", "Value": "true"},
            ],
        )


class DevCognitoGateway(CognitoGateway):
    def create_group(self, group_name: str):
        self.aws_client.create_group(GroupName=group_name, UserPoolId=self.aws_user_pool_id)

    def delete_group(self, group_name: str):
        self.aws_client.delete_group(GroupName=group_name, UserPoolId=self.aws_user_pool_id)

    def list_users(self) -> list[str]:
        response = self.aws_client.list_users(UserPoolId=self.aws_user_pool_id)
        return [user["Username"] for user in response["Users"]]
