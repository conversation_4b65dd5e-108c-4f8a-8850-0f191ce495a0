import sentry_sdk
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from sentry_sdk.integrations.asgi import SentryAsgiMiddleware
from starlette.middleware.authentication import AuthenticationMiddleware
from starlette.middleware.gzip import GZipMiddleware

from api.exceptions import ServiceException, service_exception_handler
from api.middleware.authentication_middleware import BearerTokenAuthBackend
from api.routers.api_v1.api import api_router, internal_api_router
from api.routers.api_v2.api import api as api_v2
from api.routers.auth.api import api as auth_api
from common.config.settings import get_settings
from common.containers.core_containers import Container
from common.event_handler_map import event_handler_map
from common.event_publisher import EventPublisher

settings = get_settings()

app = FastAPI(openapi_url="/api/openapi.json")

if settings.sentry_dns:
    sentry_sdk.init(
        dsn=settings.sentry_dns,
        environment=settings.app_env,
        traces_sample_rate=0.1,
        release=f"{settings.app_name}@{settings.app_version}",
    )
    try:
        app.add_middleware(SentryAsgiMiddleware)
    except Exception:
        pass

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(AuthenticationMiddleware, backend=BearerTokenAuthBackend())

app.add_exception_handler(ServiceException, service_exception_handler)
api_v2.add_exception_handler(ServiceException, service_exception_handler)
auth_api.add_exception_handler(ServiceException, service_exception_handler)

app.include_router(api_router, prefix="/api/v1")
app.include_router(internal_api_router, prefix="/api/internal/v1")
app.mount("/api/v2", api_v2)
app.mount("/auth", auth_api)

container = Container()
container.config.from_dict(settings.model_dump())
container.wire(
    packages=[
        "api.routers.api_v1",
        "api.routers.api_v2",
        "api.routers.auth",
        "api.middleware",
        "lambda_handlers.services.document_generation",
    ],
    modules=[
        "app.cases.event_handlers",
        "app.holdings.event_handlers",
        "common.event_handlers",
    ],
)

app.container = container

EventPublisher.handlers = event_handler_map
