from typing import <PERSON><PERSON>

from dependency_injector.wiring import Provide, inject
from fastapi import Request
from jwt import ExpiredSignatureError
from starlette.authentication import AuthCredentials, AuthenticationBackend

from api.exceptions.utils import raise_auth_exception
from api.security.casl import build_casl
from api.security.token_validation import Valida<PERSON><PERSON>oken
from app.users.entities.user_entity import User
from app.users.services.user_service import FindUserService
from common.containers.core_containers import Container


class BearerTokenAuthBackend(AuthenticationBackend):
    def get_token(self, request: Request) -> str | None:
        if "Authorization" not in request.headers:
            return None

        auth = request.headers["Authorization"]

        scheme, token = auth.split()
        if scheme.lower() != "bearer":
            raise_auth_exception()

        return token

    @inject
    async def authenticate(
        self,
        request: Request,
        find_user_service: FindUserService = Provide[Container.user_service.find_user_service],
        token_validator: ValidateToken = Provide[Container.security.token_validator],
    ) -> <PERSON><PERSON>[AuthCredentials, User] | None:
        try:
            if not (token := self.get_token(request)):
                return
            token_data = token_validator.validate_token(token)

            if not (user := await find_user_service.find_by_cognito_id(token_data.cognito_id)):
                return

            request.state.permissions = token_data.scopes
            request.state.user_id = user.id
            request.state.user_email = token_data.email
            request.state.user_cognito_id = token_data.cognito_id

            user_entity = User(
                id=user.id,
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                is_authenticated=True,
                roles=token_data.scopes,  # noqa
                permissions=build_casl(request.app.routes, frozenset(request.state.permissions)),
            )

            request.state.user = user_entity

            return AuthCredentials(token_data.scopes), user_entity
        except (ValueError, UnicodeDecodeError):
            raise_auth_exception()
        except ExpiredSignatureError:
            return
