from __future__ import annotations

from enum import IntEnum, StrEnum
from typing import Final, Iterator, Self


class HoldingStatus(IntEnum):
    NotSet = 0
    Proposed = 1
    ActiveAdvised = 2
    ActiveNonAdvised = 3
    Closed = 4
    ClosedCancelled = 5
    ClosedTransferred = 6
    AccountKept = 7

    @classmethod
    def active_holding_statuses(cls) -> set[HoldingStatus]:
        return {
            HoldingStatus.ActiveAdvised,
            HoldingStatus.ActiveNonAdvised,
            HoldingStatus.AccountKept,
        }


ACTIVE_HOLDINGS_STATUSES = HoldingStatus.active_holding_statuses()


class FeeModel(IntEnum):
    Tiered = 1
    Percent100 = 2
    Percent075 = 3
    Percent050 = 4
    PercentNil = 5
    Invoice = 6
    Custom = 7
    Percent150 = 8
    Percent025 = 9


class FeeSplitType(StrEnum):
    Standard_Fee = "Standard_Fee"
    Bonus = "Bonus"
    Aventur = "Aventur"


class FeeFromType(IntEnum):
    FeeProvider = 1
    CommissionProvider = 2
    FeeClient = 3


class FeeType(IntEnum):
    InitialFee = 1
    InitialCommission = 2
    OngoingFee = 3
    OngoingCommission = 4


class ExpectedFeeType(IntEnum):
    FeeFromProvider = 1
    FeeFromClient = 2
    CommissionFromProvider = 3


class HoldingFeeType(StrEnum):
    ESTIMATE = "estimate"
    FINAL = "final"


class PaymentStatus(IntEnum):
    Unpaid = 1
    PartPaid = 2
    Paid = 3


class MatchedAndPaymentStatus(IntEnum):
    Matched = 1
    Unmatched = 2
    MatchedAndUnpaidOnly = 3
    MatchedAndPaidOnly = 4
    MatchedPartPaid = 5


class CashflowType(StrEnum):
    Income = "Income"
    Expenditure = "Expenditure"


class HoldingCategory(IntEnum):
    Asset = 1
    Account = 2
    Debt = 3


class CaseType(IntEnum):
    NewBusiness = 1
    AnnualReview = 2
    Onboarding = 3
    AdHocWithdrawal = 4
    CatchUp = 5
    AdHocItem = 6


class ReviewType(IntEnum):
    AnnualReview = 2
    CatchUp = 5


class CaseStatus(IntEnum):
    Open = 1
    Completed = 2
    Cancelled = 3


class StageTypes(IntEnum):
    DataGathering = 1
    Planning = 2
    Implementation = 3


class TaskStatus(IntEnum):
    ToDo = 1
    InProgress = 2
    Completed = 3
    Canceled = 4
    NotApplicable = 5
    Review = 6

    @classmethod
    def closed_statuses(cls) -> set[TaskStatus]:
        return {TaskStatus.Completed, TaskStatus.Canceled, TaskStatus.NotApplicable}


class WorkstreamStatus(IntEnum):
    Pending = 1
    InProgress = 2
    Completed = 3


class PersonalTitle(IntEnum):
    NotSet = 0
    Mr = 1
    Miss = 2
    Mrs = 3
    Ms = 4
    Dr = 5
    Other = 6


class ClientType(IntEnum):
    Individual = 1
    Trust = 2
    Corporate = 3


class MartialStatus(IntEnum):
    NotSet = 0
    Single = 1
    Married = 2
    Divorced = 3
    Widowed = 4
    Separated = 5
    CivilPartnership = 6
    Cohabitating = 7


class Frequency(StrEnum):
    Weekly = "Weekly"
    Monthly = "Monthly"
    Fortnightly = "Fortnightly"
    Quarterly = "Quarterly"
    Yearly = "Yearly"
    Daily = "Daily"


class EmploymentStatus(StrEnum):
    Employed = "Employed"
    SelfEmployed = "Self Employed"
    Unemployed = "Unemployed"
    CompanyDirector = "Company Director"
    Retired = "Retired"


class CreditHistory(StrEnum):
    Good = "Good"
    Medium = "Medium"
    Bad = "Bad"


class ClientSource(IntEnum):
    NotSet = 0
    ExistingClientReferral = 1
    Unbiased = 2
    VouchedFor = 3
    OtherLeadProvider = 4
    GoogleSearchEngine = 5
    AventurWebsite = 6
    GoogleAds = 7
    GoogleBusinessPage = 8
    SocialMedia = 9
    ExistingClient = 10
    DirectSignup = 11
    # Acquisitions 1XX
    AcquisitionJPSL = 101
    AcquisitionMatthewScott = 102  # CDC
    # Referrals 2XX
    ReferralJPSL = 201


class InvestmentExperience(StrEnum):
    NoExperience = "No Experience"
    Small = "Small"
    Average = "Average"
    High = "High"


class ClientStatus(IntEnum):
    LeadLive = 1
    ActiveTransactional = 2
    LeadDormant = 3
    Inactive = 4
    ActiveOngoing = 5
    InactiveDeceased = 6
    InactiveLeaving = 7
    InactiveLostLead = 8

    @staticmethod
    def active_marketing_statuses() -> list[ClientStatus]:
        return [
            status
            for status in ClientStatus
            if status
            not in {
                ClientStatus.LeadLive,
                ClientStatus.Inactive,
                ClientStatus.InactiveLostLead,
                ClientStatus.InactiveLeaving,
                ClientStatus.InactiveDeceased,
            }
        ]

    @staticmethod
    def active_statuses() -> list[ClientStatus]:
        return [ClientStatus.ActiveOngoing, ClientStatus.ActiveTransactional]

    @staticmethod
    def closed_statuses() -> list[ClientStatus]:
        return [ClientStatus.LeadDormant, ClientStatus.Inactive, ClientStatus.InactiveDeceased]


class Relationship(StrEnum):
    Spouse = "Spouse"
    Child = "Child"
    Sibling = "Sibling"
    Niece_Nephew = "Niece/Nephew"


class StatementPaymentType(IntEnum):
    Client = 1
    Provider = 2


class PricingSources(IntEnum):
    Manual = 1
    Transact = 2
    Fundment = 3


class ValueType(IntEnum):
    Unit = 1
    Net = 2


class UserRole(StrEnum):
    Client = "client"
    SuperAdmin = "superadmin"
    Adviser = "adviser"
    Compliance = "compliance"
    Introducer = "introducer"
    Paraplanner = "paraplanner"
    RelationshipManager = "relationship_manager"
    CaseManagement = "case_management"
    ChatbotUser = "chatbot_user"

    @staticmethod
    def assignable_roles() -> Iterator[UserRole]:
        """Yield roles applicable for case/task assignment."""
        for role in UserRole:
            if role != UserRole.Client and role != UserRole.ChatbotUser:
                yield role


class FeeRoleType(StrEnum):
    Adviser = "Adviser"
    Introducer = "Introducer"
    RelationshipManager = "RelationshipManager"
    Aventur = "Aventur"


class ErrorCode(StrEnum):
    FIELD_NOT_UNIQUE = "FIELD_NOT_UNIQUE"


class ClientLinkRelationship(IntEnum):
    NotSet = 0
    Spouse = 1
    Child = 2
    Parent = 3
    Friend = 4
    BusinessPartner = 5
    Director = 6
    Shareholder = 7
    Beneficiary = 8
    Trustee = 9
    BusinessDirector = 10
    BusinessShareholder = 11
    TrustBeneficiary = 12
    TrustTrustee = 13
    Sibling = 14
    Grandparent = 15
    Grandchild = 16


class AdviceHoldingType(IntEnum):
    Existing = 1
    Proposed = 2


class TaskType(StrEnum):
    AccountsToReview = "AccountsToReview"
    CreateAdvice = "CreateAdvice"
    Default = "Default"
    FinalCheck = "FinalCheck"
    ImplementAccount = "ImplementAccount"
    RiskProfile = "RiskProfile"
    CashflowPlan = "CashflowPlan"
    AcceptanceFromClient = "AcceptanceFromClient"
    AdviceFeeEstimation = "AdviceFeeEstimation"
    PlanningReportGenerated = "PlanningReportGenerated"
    LetterOfAuthorityGenerated = "LetterOfAuthorityGenerated"
    HeadedLetterGenerated = "HeadedLetterGenerated"
    FinancialSummaryDocumentGenerated = "FinancialSummaryDocumentGenerated"
    LoaCoverLetterGenerated = "LoaCoverLetterGenerated"
    ClientAnnualReviewMessage = "ClientAnnualReviewMessage"
    ClientOnboardingMessage = "ClientOnboardingMessage"


class YesNoNeedsUpdating(StrEnum):
    Yes = "Yes"
    No = "No"
    NeedsUpdating = "Needs updating"


class HoldingAssetOrDebt(IntEnum):
    Asset = 1
    Debt = -1


class ProductTypeType(StrEnum):
    Account = "account"
    Property = "property"
    CompanyShares = "company_shares"
    CryptoCurrency = "crypto_currency"
    OtherAsset = "other_asset"
    TermPolicy = "term_policy"
    IndemnityPolicy = "indemnity_policy"
    WholeOfLifePolicy = "whole_of_life_policy"
    IncomeProtectionPolicy = "income_protection_policy"
    DefinedBenefitPension = "defined_benefit_pension"
    Mortgage = "mortgage"
    PersonalLoan = "personal_loan"
    CreditCard = "credit_card"
    OtherDebt = "other_debt"

    def is_asset(self) -> bool:
        return self in (
            self.Account,
            self.Property,
            self.CompanyShares,
            self.CryptoCurrency,
            self.OtherAsset,
            self.TermPolicy,
            self.IndemnityPolicy,
            self.WholeOfLifePolicy,
            self.IncomeProtectionPolicy,
            self.DefinedBenefitPension,
        )

    def is_debt(self) -> bool:
        return not self.is_asset()


ACCOUNT_LIKE_PRODUCT_TYPES: Final[frozenset[ProductTypeType]] = frozenset(
    {
        ProductTypeType.Account,
        ProductTypeType.Mortgage,
        ProductTypeType.CreditCard,
        ProductTypeType.PersonalLoan,
        ProductTypeType.OtherDebt,
        # NOTE: The below added to maintain behaviour - previously these were classed as ProductTypeType.Account
        ProductTypeType.TermPolicy,
        ProductTypeType.IndemnityPolicy,
        ProductTypeType.WholeOfLifePolicy,
        ProductTypeType.IncomeProtectionPolicy,
        ProductTypeType.DefinedBenefitPension,
    }
)


class OwnershipType(StrEnum):
    NoType = ""
    Sole = "sole"
    Joint = "joint"
    TenantsInCommon = "tenants_in_common"


class Goals(IntEnum):
    EmergencyFund = 1
    PropertyOwnership = 2
    ProtectYourselfAndFamily = 3
    Retirement = 5
    EstatePlanningAndWills = 6
    InvestForChildren = 7
    InvestForSchoolFees = 8
    TravelPlanning = 9
    BuildWealth = 10
    Custom = 11
    ClientSetup = 12
    CaseTask = 13


class IdentityCheckResult(StrEnum):
    PASS = "Pass"
    REFER = "Refer"
    FAIL = "Fail"


class IdentityMatchLevel(StrEnum):
    High = "HIGH"
    Medium = "MEDIUM"
    Low = "LOW"


class UserStatus(StrEnum):
    Active = "Active"
    Inactive = "Inactive"

    @classmethod
    def from_bool(cls, value: bool) -> Self:
        return UserStatus.Active if value else UserStatus.Inactive

    def to_bool(self) -> bool:
        return True if self is UserStatus.Active else False


class PaginationEnum(StrEnum):
    NO_PAGINATION = "no_pagination"


class ProcessStepType(StrEnum):
    ACCEPTANCE_FROM_CLIENT = "acceptance_from_client"
    ADD_ACCOUNT_TO_REVIEW = "accounts_to_review"
    ADVICE_SELECTION = "create_advice"
    DEFAULT = "base_task"
    FINAL_CHECK = "final_check"
    IMPLEMENT_ACCOUNT = "implement_account"
    ADVICE_FEE_ESTIMATION = "advice_fee_estimation"
    RISK_PROFILE = "risk_profile"
    CASHFLOW_PLAN = "cashflow_plan"
    PLANNING_REPORT_GENERATED = "planning_report_generated"
    LETTER_OF_AUTHORITY_GENERATED = "letter_of_authority_generated"
    HEADED_LETTER_GENERATED = "headed_letter_generated"
    FINANCIAL_SUMMARY_DOCUMENT_GENERATED = "financial_summary_document_generated"
    LOA_COVER_LETTER_GENERATED = "loa_cover_letter_generated"
    CLIENT_ANNUAL_REVIEW_MESSAGE = "client_annual_review_message"
    CLIENT_ONBOARDING_MESSAGE = "client_onboarding_message"


class HoldingSectionType(StrEnum):
    PLAN_INFO = "has_plan_information"
    INVESTMENT_MIX = "has_investment_mix"


class CaseResultOrder(IntEnum):
    ID = 1
    DUE_DATE = 2


class CaseLevelCommand(StrEnum):
    COMPLETE_CASE = "completeCase"
    CANCEL_CASE = "cancelCase"
    UPDATE_CASE_ADVISER = "updateCaseAdviser"
    UPDATE_CASE_MANAGER = "updateCaseManager"
    UPDATE_CASE_REVIEW_SLOT = "updateCaseReviewSlot"
    ADD_NOTE = "addNote"


class TaskSlugLevelCommand(StrEnum):
    UPDATE_TASK_ASSIGNEE = "updateTaskAssignee"
    UPDATE_TASK_ASSIGNEE_GROUP = "updateTaskAssigneeGroup"
    UPDATE_TASK_DUE_DATE = "updateTaskDueDate"
    CHANGE_TASK_STATUS = "changeTaskStatus"


class CustomTaskCommand(StrEnum):
    ADD_ACCOUNTS_TO_REVIEW = "addAccountsToReview"
    REMOVE_ACCOUNT_FROM_REVIEW = "removeAccountFromReview"
    UPDATE_ACCOUNT_ADVICE = "updateAccountAdvice"
    UPDATE_ACCOUNT_EXPECTED_FEES = "updateAccountExpectedFees"
    ADD_PROPOSED_ACCOUNT = "addProposedAccount"
    REMOVE_PROPOSED_ACCOUNT = "removeProposedAccount"
    UPDATE_ADVICE_ACCEPTANCE = "updateAdviceAcceptance"
    UPDATE_ADVICE_IMPLEMENTATION = "updateAdviceImplementation"
    REQUEST_DOCUMENT = "requestDocument"
    SEND_CLIENT_EMAIL = "sendClientEmail"
    UPDATE_DOCUMENTS_REQUIREMENT = "updateDocumentsRequirement"


class ClientAgreement(IntEnum):
    NONE = 0
    V8 = 1
    V9 = 2
    V9_1 = 3
    V10 = 4


class PrivacyNotice(IntEnum):
    NONE = 0
    V4 = 1
    V5 = 2
    V6 = 3
    V8 = 4


class ProductDataLayout(StrEnum):
    PENSION_LAYOUT = "pension_layout"
    PROTECTION_LAYOUT = "protection_layout"
    INVESTMENT_LAYOUT = "investment_layout"
    PENSION_INCOME_PAYING_LAYOUT = "pension_income_paying_layout"


class MonthlyPaymentTypes(StrEnum):
    CONTRIBUTION = "contribution"
    WITHDRAWAL = "withdrawal"


class EstimatedRiskLevel(IntEnum):
    DontKnow = -1
    AV0 = 0
    AV1 = 1
    AV2 = 2
    AV3 = 3
    AV4 = 4
    AV5 = 5


class ProductTypeGroup(IntEnum):
    INVESTMENT = 1
    PENSION_INCOME_PAYING = 2
    PENSION = 4
    PROTECTION = 5
    BANK_AND_BUILDING_SOCIETY = 6
    ESTATE_PLANNING = 7
    PROPERTY = 8
    OTHER_ASSETS = 9
    LOAN = 10
    MORTGAGE = 11
    OTHER_DEBT = 12
