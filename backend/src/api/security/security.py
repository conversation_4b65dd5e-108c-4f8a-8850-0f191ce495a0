import functools
from typing import List, Optional

from fastapi import Depends, Request
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer, SecurityScopes
from jwt import InvalidTokenError

from api.enums import UserRole
from api.exceptions.base_exceptions import UnauthorizedException
from api.exceptions.utils import raise_auth_exception, raise_chatbot_permission_exception


class HashableSecurityScopes(SecurityScopes):
    def __init__(self, scopes: Optional[List[str]] = None):
        super().__init__(scopes)
        self.scopes = frozenset(scopes)
        self.scope_str = " ".join(sorted(self.scopes))

    def __eq__(self, other):
        if not isinstance(other, HashableSecurityScopes):
            return False
        return self.scope_str == other.scope_str

    def __hash__(self):
        return hash(self.scope_str)

    def authenticate_value(self):
        if self.scopes:
            return f'Bearer scope="{self.scope_str}"'
        else:
            return "Bearer"


class Permissions:
    def __init__(self, permissions: List[str], client_param: str = "client_id"):
        self.permissions = HashableSecurityScopes(permissions)
        self.client_param = client_param

    def request_scopes(self, request: Request):
        return request.auth.scopes

    def is_chatbot_permission_failure(self, request_scopes) -> bool:
        """Check if this is specifically a ChatbotUser permission failure"""
        return (
            UserRole.ChatbotUser in self.permissions.scopes
            and UserRole.ChatbotUser not in request_scopes
        )

    async def __call__(
        self,
        request: Request,
        token: HTTPAuthorizationCredentials = Depends(HTTPBearer()),
    ):
        try:
            # let go if those permissions are empty of if user has at least one group in common with permissions
            if self.permissions.scopes and not self.permissions.scopes.intersection(
                self.request_scopes(request)
            ):
                # Check if this is specifically a chatbot permission failure
                if self.is_chatbot_permission_failure(self.request_scopes(request)):
                    raise_chatbot_permission_exception(
                        auth_value=self.permissions.authenticate_value()
                    )
                else:
                    raise_auth_exception(
                        "Not enough permissions", auth_value=self.permissions.authenticate_value()
                    )

            path_param = request.path_params.get(self.client_param)
            if request and path_param:
                if (
                    UserRole.Client in self.request_scopes(request)
                    and str(request.user.id) != path_param
                ):
                    raise UnauthorizedException

        except InvalidTokenError:
            raise_auth_exception(auth_value=self.permissions.authenticate_value())

    def __eq__(self, other):
        if not isinstance(other, type(self)):
            return False
        return self.permissions == other.permissions

    def __hash__(self):
        return hash(self.permissions)


def authorize_client_request(_func=None, *, param: str = "client_id"):
    def decorator(func):
        @functools.wraps(func)
        async def authorize(*args, **kwargs):
            request = kwargs.get("request")
            path_param = request.path_params.get(param)

            if request and path_param:
                if UserRole.Client in request.auth.scopes and str(request.user.id) != path_param:
                    raise UnauthorizedException
            return await func(*args, **kwargs)

        return authorize

    return decorator(_func) if _func else decorator
