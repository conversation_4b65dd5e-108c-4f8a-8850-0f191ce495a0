from fastapi import Depends

from api.enums import UserRole
from api.security.security import Permissions

permission_roles = {
    "authenticated": [*UserRole.__members__.values()],
    "default": [
        UserRole.SuperAdmin,
        UserRole.Adviser,
        UserRole.Paraplanner,
        UserRole.CaseManagement,
    ],
    "superadmin": [UserRole.SuperAdmin],
    "client": [UserRole.Client],
    "chatbot_user": [UserRole.ChatbotUser],
}

AUTHENTICATED_PERMISSIONS = Depends(Permissions(permission_roles["authenticated"]))
DEFAULT_PERMISSIONS = Depends(Permissions(permission_roles["default"]))
SUPERADMIN_PERMISSIONS = Depends(Permissions(permission_roles["superadmin"]))
CLIENT_PERMISSIONS = Depends(Permissions(permission_roles["client"]))
CHATBOT_PERMISSIONS = Depends(  # Indicates the user has granted chatbot consent
    Permissions(permission_roles["chatbot_user"])
)
