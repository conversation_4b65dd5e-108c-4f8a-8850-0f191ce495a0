from datetime import date
from uuid import UUID

from pydantic import BaseModel, HttpUrl

from api.enums import IdentityCheckResult
from database.repositories.aml.aml_monitoring_dtos import MatchStatus


class Role(BaseModel):
    title: str
    status: str
    country: str


class Match(BaseModel):
    id: UUID
    first_name: str
    last_name: str
    categories: list[str]
    image: HttpUrl | None
    match_status: MatchStatus
    roles: list[Role]


class AMLMonitoringSession(BaseModel):
    id: UUID
    matches: list[Match]
    status: str


class AMLSearchCriteria(BaseModel):
    first_name: str
    last_name: str
    address: str | None
    city: str | None
    post_code: str | None
    country_code: str
    date_of_birth: date


class AMLSearchResult(BaseModel):
    result: IdentityCheckResult
    date: date
    criteria: AMLSearchCriteria
    detail: list
