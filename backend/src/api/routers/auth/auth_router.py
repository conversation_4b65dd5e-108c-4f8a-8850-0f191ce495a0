from typing import Annotated

from dependency_injector.wiring import Provide, inject
from fastapi import Body, Depends
from starlette.status import HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST

from api.routers.api_router import Jarvis<PERSON><PERSON>outer
from api.schemas.requests.user_requests import UserCreate
from api.schemas.responses.error_responses import ErrorResponse
from app.shared_kernel.dtos.client_dtos import ClientDetailDTO
from app.users.exceptions import DuplicateUserException
from common.containers.core_containers import Container

router = Jarvis<PERSON>IRouter()


@router.post(
    "/signup",
    response_model=None,
    status_code=HTTP_204_NO_CONTENT,
    responses={
        HTTP_400_BAD_REQUEST: {
            "model": ErrorResponse,
            "description": "Email is already in use.",
        },
    },
)
@inject
async def handle_signup(
    data: Annotated[UserCreate, Body(embed=True)],
    find_user_service=Depends(
        Provide[Container.user_service.find_user_service],
    ),
    add_client_service=Depends(
        Provide[Container.client_services.add_client],
    ),
    set_client_access_command=Depends(
        Provide[Container.client_access_services.set_client_access],
    ),
):
    if await find_user_service.find_by_email(data.email) is not None:
        raise DuplicateUserException(email=data.email)

    client_id = await add_client_service.signup_client(**data.model_dump())

    await set_client_access_command(
        client_data=ClientDetailDTO(
            id=client_id,
            email=data.email,
            client_status_id=5,  # ActiveOngoing
            type="client",
            addresses=[],
            links=[],
        ),
        status=True,
    )
