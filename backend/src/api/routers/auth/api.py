from fastapi import FastAP<PERSON>
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

from api.routers.auth import auth_router

api = FastAPI(
    middleware=[
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        ),
    ]
)
api.include_router(auth_router.router, tags=["Auth"])
