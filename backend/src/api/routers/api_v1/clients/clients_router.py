from select import select
from typing import List

from dependency_injector.wiring import Provide, inject
from fastapi import Depends, Response
from starlette.status import (
    HTTP_200_OK,
    HTTP_201_CREATED,
    HTTP_204_NO_CONTENT,
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
)

from api.common.db_injection import AsyncSessionInjection
from api.common.injection import UnitOfWorkInject
from api.common.pagination import PaginationDep
from api.enums import CaseStatus
from api.routers.api_router import Jarvis<PERSON><PERSON>outer
from api.schemas.requests.client_requests import (
    CreateClientSchema,
    IncomeExpenditureCreate,
    UpdateClientSchema,
)
from api.schemas.responses import holding_responses
from api.schemas.responses.case_responses import (
    AvailableClientReviewSlotsSchema,
    CaseListItem,
    CaseListResponse,
    ReviewSchema,
)
from api.schemas.responses.client_responses import (
    IncomeExpenditureRead,
)
from api.schemas.responses.error_responses import ErrorResponse
from api.schemas.responses.review_responses import ReviewGroupSchema
from api.security.permission_roles import DEFAULT_PERMISSIONS
from app.cases.services.case_query_service import CaseQueryService
from app.clients import services as client_services
from app.holdings.queries import holding_queries as holding_views
from app.review_slots.queries.get_client_review_slots import get_client_review_slots
from app.review_slots.services.review_services import (
    ClientReviewSlots,
    ReviewGroupServices,
)
from app.shared_kernel.dtos.case_dtos import CaseDTO
from common.containers.core_containers import Container
from database.models import ClientIncomeExpenditureModel, ClientModel
from database.repositories.client_repository import ClientRepository

router = JarvisAPIRouter(prefix="/clients")


@router.post(
    "/",
    response_model=int,
    dependencies=[DEFAULT_PERMISSIONS],
    status_code=HTTP_201_CREATED,
    responses={
        HTTP_201_CREATED: {
            "model": int,
            "description": "New client created successfully. Id of new client returned.",
        },
        HTTP_409_CONFLICT: {
            "model": dict[str, str],
            "description": "New client conflicts with an existing one (eg. email.).",
            "content": {"application/json": {"example": {"email": "This email is in use."}}},
        },
    },
)
@inject
async def create_client(
    client: CreateClientSchema,
    service: client_services.add_client.AddClientService = Depends(
        Provide[Container.client_services.add_client]
    ),
):
    client_id = await service.add_client(
        email=client.email,
        no_email_reason_id=client.no_email_reason_id,
        first_name=client.first_name,
        last_name=client.last_name,
        client_type=client.client_type_id,
        owner_id=client.owner_id,
        title_id=client.title_id,
        client_status=client.client_status,
        client_source=client.client_source,
        date_of_birth=client.date_of_birth,
        phone_number=client.phone_number,
        mobile_number=client.mobile_number,
        client_links=[(link.client_id, link.link_relationship_id) for link in client.links],
    )
    return client_id


@router.put(
    "/{client_id}",
    dependencies=[DEFAULT_PERMISSIONS],
    responses={
        HTTP_204_NO_CONTENT: {
            "model": None,
            "description": "Returned when a client is updated as a result of this request.",
        },
        HTTP_409_CONFLICT: {
            "model": dict[str, str],
            "description": "Returned when request conflicts with the existing state eg. email is duplicate.",
            "content": {"application/json": {"example": {"email": "This email is in use."}}},
        },
    },
    status_code=HTTP_204_NO_CONTENT,
)
@inject
async def update_client(
    client_id: int,
    client: UpdateClientSchema,
    service: client_services.update_client.UpdateClientService = Depends(
        Provide[Container.client_services.update_client]
    ),
):
    await service.update_client(
        client_id=client_id,
        email=client.email,
        no_email_reason_id=client.no_email_reason_id,
        first_name=client.first_name,
        last_name=client.last_name,
        client_type=client.client_type_id,
        owner_id=client.owner_id,
        title_id=client.title_id,
        client_status=client.client_status,
        client_source=client.client_source,
        date_of_birth=client.date_of_birth,
        phone_number=client.phone_number,
        mobile_number=client.mobile_number,
        client_links=[(link.client_id, link.link_relationship_id) for link in client.links],
        client_agreement_id=client.client_agreement_id,
        privacy_notice_id=client.privacy_notice_id,
    )
    return Response(None, HTTP_204_NO_CONTENT)


@router.get(
    "/{client_id}/cashflows",
    response_model=List[IncomeExpenditureRead],
    dependencies=[DEFAULT_PERMISSIONS],
    deprecated=True,
)
async def get_client_cashflows(client_id: int, repo: ClientRepository = Depends()):
    client = await repo.with_eager_load([ClientModel.cashflows]).get(client_id)
    return client.cashflows


@router.put(
    "/{client_id}/cashflows",
    response_model=List[IncomeExpenditureRead],
    dependencies=[DEFAULT_PERMISSIONS],
    deprecated=True,
)
async def create_client_cashflow(
    client_id: int,
    cashflow: IncomeExpenditureCreate,
    repo: ClientRepository = Depends(),
):
    client = await repo.with_eager_load([ClientModel.cashflows]).get(client_id)

    flow = ClientIncomeExpenditureModel(
        income_expenditure_type_id=cashflow.type_id,
        amount=cashflow.amount,
        period=cashflow.frequency,
        share_percent=cashflow.shared_percent,
    )
    client.cashflows.append(flow)
    await repo.session.commit()

    return client.cashflows


@router.delete(
    "/{client_id}/cashflows/{id}",
    response_model=List[IncomeExpenditureRead],
    dependencies=[DEFAULT_PERMISSIONS],
    deprecated=True,
)
async def delete_client_cashflow(client_id: int, id: int, repo: ClientRepository = Depends()):
    smt = select(ClientIncomeExpenditureModel).where(ClientIncomeExpenditureModel.id == id)
    result = await repo.session.execute(smt)
    flow = result.scalar_one()
    await repo.session.delete(flow)

    await repo.session.commit()

    client = await repo.with_eager_load([ClientModel.cashflows]).get(client_id)

    return client.cashflows


@router.get(
    "/{client_id}/case",
    response_model=CaseListResponse,
    dependencies=[DEFAULT_PERMISSIONS],
)
@inject
async def get_client_cases(
    pagination: PaginationDep,
    client_id: int,
    status: CaseStatus | None = None,
    cases_services: CaseQueryService = Depends(Provide[Container.case_service.case_queries]),
):
    cases, total_case_count = await cases_services.get_cases_old(
        client_id=client_id,
        case_status=status,
        pagination=pagination,
    )
    return _prepare_client_cases_list_view(cases, total_case_count)


@router.get(
    "/{client_id}/holdings",
    response_model=list[holding_responses.HoldingSchema],
    dependencies=[DEFAULT_PERMISSIONS],
)
async def get_client_holdings(
    client_id: int,
    uow: UnitOfWorkInject,
):
    async with uow:
        holdings = await uow.holdings.get_by_client_id(client_id)
    return holdings


@router.get(
    "/{client_id}/holdings-active",
    response_model=list[holding_responses.HoldingSchema],
    dependencies=[DEFAULT_PERMISSIONS],
)
async def get_client_holdings_active(
    client_id: int,
    uow: UnitOfWorkInject,
):
    holdings = await holding_views.get_active_holdings([client_id], uow)
    response = []
    for holding in holdings:
        response.append(holding_responses.HoldingSchema.model_validate(holding))
    return response


@router.get(
    "/{client_id}/case/available-review-slots",
    summary="Get the available review slots for the specified client",
    response_model=AvailableClientReviewSlotsSchema,
    response_description="Successful response",
    status_code=HTTP_200_OK,
    dependencies=[DEFAULT_PERMISSIONS],
)
async def get_available_review_slots(client_id: int, session: AsyncSessionInjection):
    return await get_client_review_slots(client_id, session)


def _prepare_client_cases_list_view(
    cases: list[CaseDTO], total_case_count: int
) -> CaseListResponse:
    case_list = []
    for case in cases:
        tasks_complete: int = 0
        tasks_total: int = 0
        for goal in case.goals:
            tasks_complete += len(goal.tasks_closed)
            tasks_total += len(goal.tasks)
        case_list_item = CaseListItem(
            id=case.id,
            name=case.name,
            adviser=case.adviser,
            status=case.status,
            tasks_complete=tasks_complete,
            tasks_total=tasks_total,
        )
        case_list.append(case_list_item)

    return CaseListResponse(cases=case_list, total_case_count=total_case_count)


@router.get(
    "/{client_id}/is-aml-ready",
    summary="Validate client profile is AML ready",
    response_description="Successful response",
    dependencies=[DEFAULT_PERMISSIONS],
    status_code=HTTP_204_NO_CONTENT,
    responses={
        HTTP_404_NOT_FOUND: {
            "model": ErrorResponse,
            "description": "Client not found for the specified ID",
        },
    },
)
@inject
async def validate_for_aml(
    client_id: int,
    command=Depends(Provide[Container.aml_services.aml_command_service]),
):
    await command.is_aml_ready(client_id)


@router.get(
    "/{client_id}/review-group",
    response_model=ReviewGroupSchema,
    dependencies=[DEFAULT_PERMISSIONS],
)
@inject
async def get_current_group(
    client_id: int,
    review_group_services: ReviewGroupServices = Depends(
        Provide[Container.review_services.review_group_services]
    ),
):
    return await review_group_services.find_by_client_id(client_id=client_id)


@router.get(
    "/{client_id}/review-slots",
    response_model=list[ReviewSchema],
    dependencies=[DEFAULT_PERMISSIONS],
)
@inject
async def get_review_slots(
    client_id: int,
    query: ClientReviewSlots = Depends(Provide[Container.review_services.review_slots]),
):
    return await query(client_id=client_id)
