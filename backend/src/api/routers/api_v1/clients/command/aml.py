from datetime import date
from typing import Annotated
from uuid import UUID

from dependency_injector.wiring import Provide, inject
from fastapi import Body, Depends
from pydantic import BaseModel
from starlette.status import HTTP_201_CREATED, HTTP_404_NOT_FOUND, HTTP_502_BAD_GATEWAY

from api.enums import IdentityCheckResult
from api.routers.api_router import Jarvis<PERSON><PERSON>outer
from api.schemas.responses.error_responses import ErrorResponse
from api.security.permission_roles import DEFAULT_PERMISSIONS
from common.containers.core_containers import Container
from database.repositories.aml.aml_monitoring_dtos import MatchStatus

router = JarvisAPIRouter(prefix="/clients")


class IdentityValidationResult(BaseModel):
    result: IdentityCheckResult
    date: date


@router.post(
    "/{client_id}/aml/validate-identity",
    summary="""
    Request identity verification for the specified client.
    """,
    description="""
    This service verifies a client's address and identity information via the 3rd Party
    ZignSec API. The verification result is saved to the Jarvis database and returned
    in the response. If the identity check result is not a fail then the client is also
    enrolled for PEP, Sanction and Adverse Media Check monitoring.
    """,
    response_model=bool,
    response_description="Successful response",
    status_code=HTTP_201_CREATED,
    dependencies=[DEFAULT_PERMISSIONS],
    responses={
        HTTP_404_NOT_FOUND: {
            "model": ErrorResponse,
            "description": "Client not found for the specified ID",
        },
        HTTP_502_BAD_GATEWAY: {
            "model": ErrorResponse,
            "description": "Error in the ZignSec API",
        },
    },
)
@inject
async def post_validate_identity(
    client_id: int, aml_service=Depends(Provide[Container.aml_services.aml_command_service])
):
    await aml_service.client_identity_check(client_id)
    return True


@router.post(
    "/{client_id}/aml/monitoring/",
    summary="""
    Request identity verification for the specified client.
    """,
    description="""
    This service verifies a client's address and identity information via the 3rd Party
    ZignSec API. The verification result is saved to the Jarvis database and returned
    in the response. If the identity check result is not a fail then the client is also
    enrolled for PEP, Sanction and Adverse Media Check monitoring.
    """,
    response_model=bool,
    response_description="Successful response",
    status_code=HTTP_201_CREATED,
    dependencies=[DEFAULT_PERMISSIONS],
    responses={
        HTTP_404_NOT_FOUND: {
            "model": ErrorResponse,
            "description": "Client not found for the specified ID",
        },
        HTTP_502_BAD_GATEWAY: {
            "model": ErrorResponse,
            "description": "Error in the ZignSec API",
        },
    },
)
@inject
async def post_match_status(
    client_id: int,  # noqa
    session_id: Annotated[UUID, Body()],
    match_id: Annotated[UUID, Body()],
    match_status: Annotated[MatchStatus, Body()],
    aml_service=Depends(Provide[Container.aml_services.aml_command_service]),
):
    await aml_service.update_match_status(session_id, match_id, match_status)
    return True
