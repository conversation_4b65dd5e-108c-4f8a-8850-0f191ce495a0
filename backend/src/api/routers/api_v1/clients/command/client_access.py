from typing import Annotated

from dependency_injector.wiring import Provide, inject
from fastapi import Body, Depends
from starlette.status import HTTP_204_NO_CONTENT

from api.routers.api_router import Jarvis<PERSON><PERSON>outer
from api.security.permission_roles import DEFAULT_PERMISSIONS
from app.clients.services.client_access import ClientAccessService
from app.clients.services.client_query_services import ClientQueryServices
from common.containers.core_containers import Container

router = Jarvis<PERSON>IRouter(prefix="/clients")


@router.put(
    "/{client_id}/client-access",
    summary="Enabled/Disable client access to Jarvis application(s)",
    response_description="Successful response",
    response_model=None,
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[DEFAULT_PERMISSIONS],
)
@inject
async def set_access(
    client_id: int,
    enabled: Annotated[bool, Body(embed=True)],
    client_query_services: ClientQueryServices = Depends(
        Provide[Container.client_services.client_query_services]
    ),
    set_client_access: ClientAccessService = Depends(
        Provide[Container.client_access_services.set_client_access]
    ),
):
    client = await client_query_services.get_by_id(client_id=client_id)
    await set_client_access(client_data=client, status=enabled)
