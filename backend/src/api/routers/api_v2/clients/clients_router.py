from dependency_injector.wiring import Provide, inject
from fastapi import Depends, Query
from starlette import status

from api.routers.api_router import JarvisAPIRouter
from api.routers.api_v2.clients.clients_requests import (
    ClientSnapshotAvailableAttribute,
    UpdateClientAccountSchema,
)
from api.security.permission_roles import AUTHENTICATED_PERMISSIONS
from app.clients import services as client_services
from app.clients.services.client_query_services import ClientQueryServices
from app.health_score.dtos.health_score import HealthScoreDTO
from app.health_score.services.health_score_service import HealthScoreService
from app.profile_completeness.dtos import SectionCompletenessDTO
from app.profile_completeness.service import ClientProfileCompletenessService
from common.containers.core_containers import Container

router = JarvisAPIRouter(prefix="/clients")


@router.get(
    "/{client_id}/health-score",
    dependencies=[AUTHENTICATED_PERMISSIONS],
    response_model=HealthScoreDTO,
)
@inject
async def get_health_score(
    client_id: int,
    service: HealthScoreService = Depends(Provide[Container.client_services.health_score_service]),
):
    return await service.get_health_score(client_id)


@router.patch(
    "/{client_id}/account",
    dependencies=[AUTHENTICATED_PERMISSIONS],
    response_model=None,
    status_code=status.HTTP_204_NO_CONTENT,
)
@inject
async def update_account(
    client_id: int,
    account_data: UpdateClientAccountSchema,
    client_update_service: client_services.update_client.UpdateClientService = Depends(
        Provide[Container.client_services.update_client]
    ),
    get_client_details_command: ClientQueryServices = Depends(
        Provide[Container.client_services.client_query_services]
    ),
):
    client = await get_client_details_command.get_by_id(client_id=client_id)
    await client_update_service.update_client(
        client_id=client_id,
        no_email_reason_id=client.no_email_reason_id,
        first_name=client.first_name,
        last_name=client.last_name,
        client_type=client.client_type_id,
        owner_id=client.advisor.id,
        title_id=client.title_id,
        client_status=client.client_status_id,
        client_source=client.client_source_id,
        date_of_birth=client.date_of_birth,
        phone_number=client.phone_number,
        mobile_number=client.mobile_number,
        client_links=[(link.client_id, link.link_relationship_id) for link in client.links],
        client_agreement_id=client.client_agreement_id,
        privacy_notice_id=client.privacy_notice_id,
        # update email
        email=account_data.email,
    )


@router.get(
    "/{client_id}/profile-completeness",
    dependencies=[AUTHENTICATED_PERMISSIONS],
    response_model=SectionCompletenessDTO
    | dict[str, SectionCompletenessDTO | list[SectionCompletenessDTO]]
    | list[SectionCompletenessDTO],
    responses={
        200: {
            "description": "Profile completeness information",
            "content": {
                "application/json": {
                    "examples": {
                        "full_response": {
                            "summary": "Full response (no attributes specified)",
                            "value": {
                                "status": "partial",
                                "completed_required_fields": ["client_details", "addresses"],
                                "missing_required_fields": ["factfind_answers", "incomes"],
                                "nested": {
                                    "client_details": {
                                        "status": "complete",
                                        "completed_required_fields": [
                                            "first_name",
                                            "last_name",
                                            "email",
                                        ],
                                        "missing_required_fields": [],
                                        "percentage_complete": 100.0,
                                    },
                                    "factfind_answers": {
                                        "status": "partial",
                                        "completed_required_fields": ["has_will_in_place"],
                                        "missing_required_fields": ["retirement_age"],
                                        "percentage_complete": 50.0,
                                    },
                                },
                                "percentage_complete": 75.5,
                                "object_id": 123,
                            },
                        },
                        "single_attribute": {
                            "summary": "Single attribute (e.g., ?attributes=client_details)",
                            "value": {
                                "status": "complete",
                                "completed_required_fields": ["first_name", "last_name", "email"],
                                "missing_required_fields": [],
                                "percentage_complete": 100.0,
                            },
                        },
                        "multiple_attributes": {
                            "summary": "Multiple attributes (e.g., ?attributes=client_details&attributes=factfind_answers)",
                            "value": {
                                "client_details": {
                                    "status": "complete",
                                    "completed_required_fields": [
                                        "first_name",
                                        "last_name",
                                        "email",
                                    ],
                                    "missing_required_fields": [],
                                    "percentage_complete": 100.0,
                                },
                                "factfind_answers": {
                                    "status": "partial",
                                    "completed_required_fields": ["has_will_in_place"],
                                    "missing_required_fields": ["retirement_age"],
                                    "percentage_complete": 50.0,
                                },
                            },
                        },
                        "list_attribute": {
                            "summary": "List attribute (e.g., ?attributes=addresses)",
                            "value": [
                                {
                                    "status": "complete",
                                    "completed_required_fields": ["address_line_one", "post_code"],
                                    "missing_required_fields": [],
                                    "percentage_complete": 100.0,
                                    "object_id": 1,
                                },
                                {
                                    "status": "partial",
                                    "completed_required_fields": ["address_line_one"],
                                    "missing_required_fields": ["post_code"],
                                    "percentage_complete": 50.0,
                                    "object_id": 2,
                                },
                            ],
                        },
                    }
                }
            },
        }
    },
)
@inject
async def get_profile_completeness(
    client_id: int,
    attributes: list[ClientSnapshotAvailableAttribute] = Query(
        None, description="List of ClientSnapshotDTO attributes to include in the response"
    ),
    service: ClientProfileCompletenessService = Depends(
        Provide[Container.client_services.profile_completeness_service]
    ),
):
    dto = await service.get_profile_completeness(client_id)

    # If attributes are specified and there's only one, return just that nested attribute
    if attributes and len(attributes) == 1 and dto.nested and attributes[0].value in dto.nested:
        return dto.nested[attributes[0].value]

    # If multiple attributes are specified, return a dict with just those attributes
    elif attributes and dto.nested:
        attr_values = [attr.value for attr in attributes]
        return {k: v for k, v in dto.nested.items() if k in attr_values}

    # Otherwise return the full DTO
    return dto
