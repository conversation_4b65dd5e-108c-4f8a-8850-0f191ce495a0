from enum import StrEnum

from pydantic import BaseModel


class ClientSnapshotAvailableAttribute(StrEnum):
    """Valid attributes of ClientSnapshotDTO for filtering."""

    CLIENT_DETAILS = "client_details"
    FACTFIND_ANSWERS = "factfind_answers"
    INCOMES = "incomes"
    EXPENSES = "expenses"
    ADDRESSES = "addresses"
    FAMILY_MEMBERS = "family_members"
    ASSETS = "assets"
    DEBTS = "debts"
    GOALS = "goals"
    AML_SEARCHES = "aml_searches"


class UpdateClientAccountSchema(BaseModel):
    email: str
