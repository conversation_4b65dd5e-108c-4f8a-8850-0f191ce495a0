from abc import ABC, abstractmethod
from datetime import date, datetime
from typing import Literal, Union

from pydantic import BaseModel, ConfigDict

from app.chatbot.constants import OperationGroup
from app.chatbot.dtos.requests import (
    InboundDataDTO,
    SubSchema,
    User_AddressesDTO,
    User_PersonalDetailsDTO,
    User_RelationsDTO,
    User_WithdrawConsentDTO,
    UserTextDTO,
)


class BaseChatbotRequest(ABC, BaseModel):
    @abstractmethod
    def to_dto(self, **kwargs) -> UserTextDTO | InboundDataDTO:
        raise NotImplementedError

    model_config = ConfigDict(extra="forbid")


class DataSchema(BaseChatbotRequest):
    id: int
    timestamp: datetime
    completed: bool
    available_fields: list[str] | None = None


class UserTextSchema(BaseChatbotRequest):
    data_layout: Literal["user_text_layout"]
    message: str

    def to_dto(
        self,
    ) -> UserTextDTO:
        return UserTextDTO(
            message=self.message,
        )


class User:
    class PersonalDetailsSchema(DataSchema):
        data_layout: Literal[f"{OperationGroup.User_PersonalDetails.value}_layout"]
        first_name: str | None = None
        last_name: str | None = None
        email: str | None = None
        phone_number: str | None = None
        mobile_number: str | None = None
        date_of_birth: date | None = None

        def to_dto(
            self,
        ) -> User_PersonalDetailsDTO:
            return User_PersonalDetailsDTO(
                timestamp=self.timestamp,
                id=self.id,
                completed=self.completed,
                available_fields=self.available_fields,
                data_layout=self.data_layout,
                first_name=self.first_name,
                last_name=self.last_name,
                email=self.email,
                phone_number=self.phone_number,
                mobile_number=self.mobile_number,
                date_of_birth=self.date_of_birth,
            )

    class AddressesSchema(DataSchema):
        data_layout: Literal[f"{OperationGroup.User_Addresses.value}_layout"]
        addresses: list[SubSchema.AddressSchema]

        def to_dto(
            self,
        ) -> User_AddressesDTO:
            return User_AddressesDTO(
                timestamp=self.timestamp,
                id=self.id,
                completed=self.completed,
                available_fields=self.available_fields,
                data_layout=self.data_layout,
                addresses=self.addresses,
            )

    class RelationsSchema(DataSchema):
        data_layout: Literal[f"{OperationGroup.User_Relations.value}_layout"]
        relations: list[SubSchema.RelationSchema]

        def to_dto(
            self,
        ) -> User_RelationsDTO:
            return User_RelationsDTO(
                timestamp=self.timestamp,
                id=self.id,
                completed=self.completed,
                available_fields=self.available_fields,
                data_layout=self.data_layout,
                relations=self.relations,
            )

    class WithdrawConsentSchema(DataSchema):
        data_layout: Literal[f"{OperationGroup.User_Consent_Withdrawal.value}_layout"]
        withdrawn: bool

        def to_dto(
            self,
        ) -> User_WithdrawConsentDTO:
            return User_WithdrawConsentDTO(
                timestamp=self.timestamp,
                id=self.id,
                completed=self.completed,
                available_fields=self.available_fields,
                data_layout=self.data_layout,
                withdrawn=self.withdrawn,
            )


RequestSchema = Union[
    UserTextSchema,
    User.PersonalDetailsSchema,
    User.AddressesSchema,
    User.RelationsSchema,
    User.WithdrawConsentSchema,
]
