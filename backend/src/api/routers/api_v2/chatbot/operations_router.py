from datetime import date

from dependency_injector.wiring import Provide, inject
from fastapi import Depends, Query
from starlette import status

from api.enums import Relationship
from api.routers.api_router import JarvisAPIRouter
from api.routers.api_v2.chatbot.operations_requests import PersonalDetails, RelationData
from api.schemas.requests.client_requests import AddressData
from api.security.permission_roles import (
    AUTHENTICATED_PERMISSIONS,
    CLIENT_PERMISSIONS,
)
from app.chatbot_operations.services.get_client import GetClientService
from app.chatbot_operations.services.get_client_addresses import GetClientAddressesService
from app.chatbot_operations.services.update_client import UpdateClientService
from app.clients.services.client_address_services import (
    AddClientAddressService,
    UpdateClientAddressService,
)
from app.clients.services.client_relations_service import (
    AddClientRelationService,
    GetClientRelationsService,
    UpdateClientRelationService,
)
from app.company_data.services.company_data import CompanyDataService
from app.shared_kernel.dtos.client_dtos import AddressDTO as NewAddressDTO
from app.shared_kernel.dtos.client_dtos import ExistingAddressD<PERSON>
from app.shared_kernel.dtos.client_relation_dtos import AddClientRelationDTO
from app.shared_kernel.dtos.client_snapshot_dtos import ClientRelationDTO
from common.containers.core_containers import Container

router = JarvisAPIRouter(prefix="/operations")


@router.get("/company", dependencies=[AUTHENTICATED_PERMISSIONS])
@inject
async def get_company_details(
    fields: set[str] = Query(default=[]),
    get_company_data_command: CompanyDataService = Depends(
        Provide[Container.chatbot_operations_services.get_company_data_service]
    ),
):
    return await get_company_data_command.get_company_fields_by_id(fields=fields)


@router.get("/client/{client_id}", dependencies=[CLIENT_PERMISSIONS])
@inject
async def get_client_details(
    client_id: int,
    fields: set[str] = Query(default=[]),
    get_client_details_command: GetClientService = Depends(
        Provide[Container.chatbot_operations_services.get_client_service]
    ),
):
    return await get_client_details_command.get_client_fields_by_id(
        client_id=client_id, fields=fields
    )


@router.put("/client/{client_id}", dependencies=[CLIENT_PERMISSIONS])
@inject
async def update_client(
    client_id: int,
    personal_details: PersonalDetails,
    service: UpdateClientService = Depends(
        Provide[Container.chatbot_operations_services.update_client_service]
    ),
):
    await service.update_client(client_id=client_id, data=personal_details)


@router.get(
    "/client/{client_id}/addresses",
    response_model=list[ExistingAddressDTO],
    dependencies=[CLIENT_PERMISSIONS],
)
@inject
async def get_client_addresses(
    client_id: int,
    address_line_one: str | None = Query(None, description="Filter by address line one"),
    address_line_two: str | None = Query(None, description="Filter by address line two"),
    address_line_three: str | None = Query(None, description="Filter by address line three"),
    address_line_four: str | None = Query(None, description="Filter by address line four"),
    city: str | None = Query(None, description="Filter by city"),
    post_code: str | None = Query(None, description="Filter by post code"),
    country_id: int | None = Query(None, description="Filter by country ID"),
    moved_in_date_from: date | None = Query(None, description="Filter by moved in date (from)"),
    moved_in_date_to: date | None = Query(None, description="Filter by moved in date (to)"),
    moved_out_date_from: date | None = Query(None, description="Filter by moved out date (from)"),
    moved_out_date_to: date | None = Query(None, description="Filter by moved out date (to)"),
    is_primary: bool | None = Query(None, description="Filter by primary address status"),
    exact_match: bool = Query(
        False, description="Use exact matching instead of partial matching for text fields"
    ),
    service: GetClientAddressesService = Depends(
        Provide[Container.chatbot_operations_services.get_client_addresses_service]
    ),
):
    return await service.get_client_addresses(
        user_id=client_id,
        address_line_one=address_line_one,
        address_line_two=address_line_two,
        address_line_three=address_line_three,
        address_line_four=address_line_four,
        city=city,
        post_code=post_code,
        country_id=country_id,
        moved_in_date_from=moved_in_date_from,
        moved_in_date_to=moved_in_date_to,
        moved_out_date_from=moved_out_date_from,
        moved_out_date_to=moved_out_date_to,
        is_primary=is_primary,
        exact_match=exact_match,
    )


@router.post(
    "/client/{client_id}/addresses",
    dependencies=[CLIENT_PERMISSIONS],
    status_code=status.HTTP_201_CREATED,
)
@inject
async def add_client_address(
    client_id: int,
    address: AddressData,
    service: AddClientAddressService = Depends(
        Provide[Container.client_services.add_client_address_service]
    ),
):
    address_dto = NewAddressDTO(
        address_line_one=address.address_line_one,
        address_line_two=address.address_line_two,
        address_line_three=address.address_line_three,
        address_line_four=address.address_line_four,
        city=address.city,
        country_id=address.country_id,
        post_code=address.post_code,
        moved_in_date=address.moved_in_date,
        moved_out_date=address.moved_out_date,
        is_primary=address.is_primary,
    )
    await service.add_client_address(client_id, address_dto)


@router.put(
    "/client/{client_id}/addresses/{address_id}",
    dependencies=[CLIENT_PERMISSIONS],
)
@inject
async def update_client_address(
    client_id: int,
    address_id: int,
    address: AddressData,
    service: UpdateClientAddressService = Depends(
        Provide[Container.client_services.update_client_address_service]
    ),
):
    address_dto = NewAddressDTO(
        address_line_one=address.address_line_one,
        address_line_two=address.address_line_two,
        address_line_three=address.address_line_three,
        address_line_four=address.address_line_four,
        city=address.city,
        country_id=address.country_id,
        post_code=address.post_code,
        moved_in_date=address.moved_in_date,
        moved_out_date=address.moved_out_date,
        is_primary=address.is_primary,
    )
    await service.update_client_address(client_id, address_id, address_dto)


@router.get(
    "/client/{client_id}/relations",
    response_model=list[ClientRelationDTO],
    dependencies=[CLIENT_PERMISSIONS],
)
@inject
async def get_client_relations(
    client_id: int,
    relationship_type: Relationship | None = None,
    service: GetClientRelationsService = Depends(
        Provide[Container.client_services.get_client_relations_service]
    ),
):
    return await service.get_relations(client_id=client_id, relationship_type=relationship_type)


@router.post(
    "/client/{client_id}/relations",
    status_code=status.HTTP_201_CREATED,
    dependencies=[CLIENT_PERMISSIONS],
)
@inject
async def add_client_relation(
    client_id: int,
    relation: RelationData,
    service: AddClientRelationService = Depends(
        Provide[Container.client_services.add_client_relation_service]
    ),
):
    relation_dto = AddClientRelationDTO(
        client_id=client_id,
        first_name=relation.first_name,
        last_name=relation.last_name,
        date_of_birth=relation.date_of_birth,
        relationship_type=relation.relationship_type,
    )
    await service.add_relation(relation_dto)


@router.put(
    "/client/{client_id}/relations/{relation_id}",
    dependencies=[CLIENT_PERMISSIONS],
)
@inject
async def update_client_relation(
    client_id: int,
    relation_id: int,
    relation: RelationData,
    service: UpdateClientRelationService = Depends(
        Provide[Container.client_services.update_client_relation_service]
    ),
):
    relation_dto = ClientRelationDTO(
        id=relation_id,
        first_name=relation.first_name,
        last_name=relation.last_name,
        date_of_birth=relation.date_of_birth,
        relationship_type=relation.relationship_type,
    )
    await service.update_relation(relation_dto)
