import json
from dataclasses import asdict
from typing import AsyncIterator

from dependency_injector.wiring import Provide, inject
from fastapi import Body, Depends
from fastapi.responses import StreamingResponse

from api.exceptions.base_exceptions import UnauthorizedException
from api.routers.api_router import <PERSON><PERSON><PERSON>out<PERSON>, JarvisRequest
from api.routers.api_v2.chatbot.chatbot_requests import RequestSchema
from api.security.permission_roles import (
    AUTHENTICATED_PERMISSIONS,
    CHATBOT_PERMISSIONS,
    CLIENT_PERMISSIONS,
)
from app.chatbot.dtos.responses import UserFacingMessage
from app.chatbot.helpers import json_serial_default
from app.chatbot.services.get_messages import GetMessages
from app.chatbot.services.new_conversation import NewConversation
from app.chatbot.services.post_message.post_message import PostMessage
from app.clients.services.client_chatbot_permissions import ClientChatbotPermissionsService
from common.containers.core_containers import Container

router = Jarvis<PERSON>IRouter(prefix="/chatbot")


@router.get(
    "/messages",
    response_model=list[UserFacingMessage],
    dependencies=[AUTHENTICATED_PERMISSIONS, CHATBOT_PERMISSIONS],
)
@inject
async def get_messages(
    user_id: int = Depends(Provide[Container.user_service.user_id]),
    command: GetMessages = Depends(Provide[Container.chatbot_services.get_messages]),
):
    return await command.get_messages(user_id=user_id)


@router.post(
    "/message",
    response_model=str,
    dependencies=[AUTHENTICATED_PERMISSIONS, CHATBOT_PERMISSIONS],
)
@inject
async def post_message(
    request: JarvisRequest,
    user_id: int = Depends(Provide[Container.user_service.user_id]),
    message: RequestSchema = Body(discriminator="data_layout"),
    command: PostMessage = Depends(Provide[Container.chatbot_services.post_message]),
):
    if (token := request.headers.get("Authorization")) is None:
        raise UnauthorizedException

    async def event_generator() -> AsyncIterator[str]:
        async for chunk in command.process_message(
            dto=message.to_dto(), user_id=user_id, jwt_token=token
        ):
            yield json.dumps(asdict(chunk), default=json_serial_default) + "\n---\n"

    # using text/event-stream prevents GZipMiddleware buffering response, despite normally only being used for streaming SSEs.
    return StreamingResponse(event_generator(), media_type="text/event-stream", status_code=200)


@router.get(
    "/new-conversation",
    response_model=list[UserFacingMessage],
    dependencies=[AUTHENTICATED_PERMISSIONS],
)
@inject
async def new_conversation(
    user_id: int = Depends(Provide[Container.user_service.user_id]),
    command: NewConversation = Depends(Provide[Container.chatbot_services.new_conversation]),
):
    return await command.new_conversation(user_id=user_id)


@router.put(
    "/consent/{client_id}/grant",
    status_code=204,
    dependencies=[CLIENT_PERMISSIONS],
)
@inject
async def grant_chatbot_consent(
    client_id: int,
    service: ClientChatbotPermissionsService = Depends(
        Provide[Container.client_services.chatbot_permissions_service]
    ),
):
    await service.grant_chatbot_consent(client_id)


@router.put(
    "/consent/{client_id}/withdraw",
    status_code=204,
    dependencies=[CLIENT_PERMISSIONS],
)
@inject
async def withdraw_chatbot_consent(
    client_id: int,
    service: ClientChatbotPermissionsService = Depends(
        Provide[Container.client_services.chatbot_permissions_service]
    ),
):
    await service.withdraw_chatbot_consent(client_id)
