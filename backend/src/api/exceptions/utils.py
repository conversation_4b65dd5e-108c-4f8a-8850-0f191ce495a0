from fastapi import HTT<PERSON>Exception
from starlette import status


def raise_auth_exception(message: str = "Could not validate credentials", auth_value="Bearer"):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=message,
        headers={"WWW-Authenticate": auth_value},
    )
    raise credentials_exception


def raise_chatbot_permission_exception(auth_value="Bearer"):
    """Raise a custom 401 exception specifically for chatbot permission failures"""
    chatbot_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail={
            "error": "chatbot_permission_required",
            "message": "Chatbot access permission is required to use this feature",
            "type": "chatbot_authorization_error",
        },
        headers={"WWW-Authenticate": auth_value},
    )
    raise chatbot_exception
