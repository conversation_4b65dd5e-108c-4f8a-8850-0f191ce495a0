[project]
name = "<PERSON>"
version = "1.14.3"
description = "<PERSON><PERSON><PERSON> Wealth - Jarvis for Wealth Advisers"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
requires-python = "<4.0,>=3.12"
dependencies = [
    "alembic>=1.14.1",
    "arrow<2.0.0,>=1.2.3",
    "asyncpg>=0.29.0",
    "aws-encryption-sdk>=4.0.0",
    "boto3-stubs-lite>=1.35.18",
    "boto3>=1.35.18",
    "dacite<2.0.0,>=1.8.1",
    "defusedxml<1.0.0,>=0.7.1",
    "dependency-injector<5.0,>=4.45",
    "email-validator<3.0.0,>=2.0.0",
    "fastapi>=0.115.8",
    "httpx>=0.27.0",
    "jinja2<4.0.0,>=3.1.2",
    "mangum>=0.19.0",
    "matplotlib<4.0.0,>=3.8.0",
    "more-itertools<11.0.0,>=10.2.0",
    "openpyxl<4.0.0,>=3.0.10",
    "pandas<3.0.0,>=2.0.0",
    "pydantic-ai-slim[bedrock]>=0.2.11",
    "pydantic-settings>=2.5.2",
    "pydantic>=2.10.0",
    "pydash<9.0.0,>=8.0.0",
    "PyJWT[crypto]<3.0.0,>=2.3.0",
    "python-dateutil==2.9.0.post0",
    "python-multipart<1.0.0,>=0.0.9",
    "redis<7.0.0,>=6.0.0",
    "sentry-sdk<3.0.0,>=2.5.1",
    "SQLAlchemy-Continuum<2.0.0,>=1.4.0",
    "sqlalchemy>=2.0.37",
    "tenacity<10.0.0,>=9.0.0",
    "tomli<3.0.0,>=2.0.1",
    "typer<1.0.0,>=0.12.0",
    "uvicorn[standard]>=0.34.0",
    "financial-models",
]

[project.scripts]
manage = "commands.manage:app"

[dependency-groups]
dev = [
    "boto3-stubs-lite[cognito-idp]<2.0.0,>=1.26.156",
    "faker>=25.8.0",
    "localstack<5.0.0,>=4.0.0",
    "mypy<2.0.0,>=1.3.0",
    "mypy-boto3-timestream-query<2.0.0,>=1.34.0",
    "mypy-boto3-timestream-write<2.0.0,>=1.34.0",
    "pre-commit>=3.8.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=5.0.0",
    "pytest-env<2.0.0,>=1.0.0",
    "pytest-order<2.0.0,>=1.1.0",
    "pytest-profiling<2.0.0,>=1.7.0",
    "pytest>=8.0.0",
    "requests<3.0.0,>=2.28.1",
    "respx<1.0.0,>=0.21.1",
    "ruff>=0.4.9",
    "xlsxwriter<4.0.0,>=3.2.0",
    "time-machine>=2.16.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = [
    "src/api",
    "src/app",
    "src/commands",
    "src/common",
    "src/database",
    "src/timestream",
    "src/lambda_handlers",
    "tests"
]


[tool.mypy]
plugins = ["sqlmypy", "pydantic.mypy"]

follow_imports = "silent"
warn_redundant_casts = true
warn_unused_ignores = true
disallow_any_generics = true
check_untyped_defs = true
no_implicit_reexport = true
ignore_missing_imports = true

# for strict mypy: (this is the tricky one :-))
disallow_untyped_defs = true

[format]
line-ending = "auto"

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true
warn_untyped_fields = true

[tool.ruff]
src = ["src", "tests"]
exclude = ["migrations", ".venv"]
line-length = 100
lint.select = [
    "E", # pycodestyle errors
    "W", # pycodestyle warnings
    "F", # pyflakes
    "I", # isort
    "C", # flake8-comprehensions
    "B", # flake8-bugbear
]
lint.ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex,
    "F405",
    "B904",
    "E711", # Comparison to `None`
    "E712", # Comparison to `True`
]

[tool.ruff.lint.extend-per-file-ignores]
"__init__.py" = ["F401", "F403", "E402"]
"src/app/shared_kernel/dtos/app_session.py" = ["F821"]
"src/api/common/enums.py" = ["E721"]

[tool.ruff.lint.isort]
known-third-party = ["fastapi", "pydantic", "starlette"]
known-first-party = [
    "api",
    "app",
    "commands",
    "common",
    "database",
    "lambda_handlers",
    "tests",
]

[tool.ruff.lint.flake8-comprehensions]
allow-dict-calls-with-keyword-arguments = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
env = ["APP_ENV=testing"]
markers = ["no_permission_overrides: Marks test to skip permission checks"]
asyncio_default_fixture_loop_scope = "session"
asyncio_default_test_loop_scope = "session"

[tool.uv.sources]
financial-models = { git = "https://bitbucket.org/aventur_wealth/financial-models.git", rev = "v0.0.2" }
