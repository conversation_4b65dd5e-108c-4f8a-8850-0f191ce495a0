"""Added chatbot consent columns

Revision ID: ffad2dcd53dd
Revises: 919f1bebaa7e
Create Date: 2025-06-24 08:02:55.868852

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "ffad2dcd53dd"
down_revision = "919f1bebaa7e"
branch_labels = None
depends_on = None


def upgrade():
    # Add columns as nullable first
    op.add_column("client", sa.Column(
        "chatbot_consent_granted", sa.<PERSON>(), nullable=True))
    op.add_column("client", sa.Column(
        "chatbot_consent_timestamp", sa.DateTime(), nullable=True))

    # Set default value for existing rows
    op.execute("UPDATE client SET chatbot_consent_granted = false")

    # Make column NOT NULL
    op.alter_column("client", "chatbot_consent_granted", nullable=False)

    op.add_column(
        "client_version",
        sa.Column("chatbot_consent_granted", sa.<PERSON>(),
                  autoincrement=False, nullable=True),
    )
    op.add_column(
        "client_version",
        sa.Column("chatbot_consent_timestamp", sa.DateTime(),
                  autoincrement=False, nullable=True),
    )


def downgrade():
    op.drop_column("client_version", "chatbot_consent_timestamp")
    op.drop_column("client_version", "chatbot_consent_granted")
    op.drop_column("client", "chatbot_consent_timestamp")
    op.drop_column("client", "chatbot_consent_granted")
